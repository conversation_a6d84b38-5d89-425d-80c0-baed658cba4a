import React, { useState } from "react";
import { Layout, CodeEditor, <PERSON><PERSON><PERSON>, OutputPanel } from "./components";
import type { CodeExecutionResult } from "./types";

const DEFAULT_CODE = `# Welcome to Python Playground!
# Write your Python code here and click "Run Code" to execute it.

print("Hello, World!")

# Try some basic Python operations:
x = 10
y = 20
result = x + y
print(f"The sum of {x} and {y} is {result}")

# You can use most Python built-in functions:
numbers = [1, 2, 3, 4, 5]
squared = [n**2 for n in numbers]
print(f"Squared numbers: {squared}")`;

function App() {
  const [code, setCode] = useState(DEFAULT_CODE);
  const [executionResult, setExecutionResult] = useState<CodeExecutionResult>({
    output: [],
    isRunning: false,
  });

  const handleRunCode = () => {
    // TODO: Implement Python code execution with Pyodide
    setExecutionResult((prev) => ({ ...prev, isRunning: true }));

    // Simulate code execution for now
    setTimeout(() => {
      setExecutionResult({
        output: [
          "Hello, World!",
          "The sum of 10 and 20 is 30",
          "Squared numbers: [1, 4, 9, 16, 25]",
        ],
        isRunning: false,
      });
    }, 1500);
  };

  const handleClearOutput = () => {
    setExecutionResult({
      output: [],
      isRunning: false,
    });
  };

  return (
    <Layout>
      <div className="h-full flex flex-col space-y-3">
        {/* Code Editor Section */}
        <div className="flex-1 bg-white rounded-lg shadow-sm">
          <div className="p-3 h-full">
            <CodeEditor
              value={code}
              onChange={setCode}
              language="python"
              className="h-full"
            />
          </div>
        </div>

        {/* Toolbar Section */}
        <div className="flex-shrink-0">
          <Toolbar
            onRun={handleRunCode}
            onClear={handleClearOutput}
            isRunning={executionResult.isRunning}
          />
        </div>

        {/* Output Section */}
        <div className="flex-1 bg-white rounded-lg shadow-sm">
          <div className="p-3 h-full">
            <OutputPanel
              output={executionResult.output}
              error={executionResult.error}
              isRunning={executionResult.isRunning}
              onClear={handleClearOutput}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default App;
