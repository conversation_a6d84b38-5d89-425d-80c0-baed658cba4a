import React, { useState } from "react";
import { Layout, CodeEditor, Too<PERSON>bar, OutputPanel } from "./components";
import type { CodeExecutionResult } from "./types";

const DEFAULT_CODE = `# Welcome to Python Playground!
# Write your Python code here and click "Run Code" to execute it.

print("Hello, World!")

# Try some basic Python operations:
x = 10
y = 20
result = x + y
print(f"The sum of {x} and {y} is {result}")

# You can use most Python built-in functions:
numbers = [1, 2, 3, 4, 5]
squared = [n**2 for n in numbers]
print(f"Squared numbers: {squared}")`;

function App() {
  const [code, setCode] = useState(DEFAULT_CODE);
  const [executionResult, setExecutionResult] = useState<CodeExecutionResult>({
    output: [],
    isRunning: false,
  });

  const handleRunCode = () => {
    // TODO: Implement Python code execution with Pyodide
    setExecutionResult((prev) => ({ ...prev, isRunning: true }));

    // Simulate code execution for now
    setTimeout(() => {
      setExecutionResult({
        output: [
          "Hello, World!",
          "The sum of 10 and 20 is 30",
          "Squared numbers: [1, 4, 9, 16, 25]",
        ],
        isRunning: false,
      });
    }, 1500);
  };

  const handleClearOutput = () => {
    setExecutionResult({
      output: [],
      isRunning: false,
    });
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Code Editor Section */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">
              Python Code Editor
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Write your Python code below. The code will run in your browser
              using WebAssembly.
            </p>
          </div>
          <div className="p-4">
            <CodeEditor
              value={code}
              onChange={setCode}
              language="python"
            />
          </div>
        </div>

        {/* Toolbar Section */}
        <Toolbar
          onRun={handleRunCode}
          onClear={handleClearOutput}
          isRunning={executionResult.isRunning}
        />

        {/* Output Section */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="p-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Output</h3>
            <p className="text-sm text-gray-600 mt-1">
              Code execution results will appear here.
            </p>
          </div>
          <div className="p-4">
            <OutputPanel
              output={executionResult.output}
              error={executionResult.error}
              isRunning={executionResult.isRunning}
              onClear={handleClearOutput}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}

export default App;
