import { WorkerMessage } from '../types';

export class WorkerHelper {
  private worker: Worker | null = null;
  private messageHandlers: Map<string, (data: any) => void> = new Map();

  constructor(workerScript: string) {
    this.initWorker(workerScript);
  }

  private initWorker(workerScript: string) {
    try {
      this.worker = new Worker(workerScript, { type: 'module' });
      this.worker.onmessage = this.handleMessage.bind(this);
      this.worker.onerror = this.handleError.bind(this);
    } catch (error) {
      console.error('Failed to initialize worker:', error);
    }
  }

  private handleMessage(event: MessageEvent<WorkerMessage>) {
    const { type, data, error } = event.data;
    const handler = this.messageHandlers.get(type);
    
    if (handler) {
      handler({ data, error });
    }
  }

  private handleError(error: ErrorEvent) {
    console.error('Worker error:', error);
    const errorHandler = this.messageHandlers.get('error');
    if (errorHandler) {
      errorHandler({ error: error.message });
    }
  }

  public on(type: string, handler: (data: any) => void) {
    this.messageHandlers.set(type, handler);
  }

  public off(type: string) {
    this.messageHandlers.delete(type);
  }

  public postMessage(message: any) {
    if (this.worker) {
      this.worker.postMessage(message);
    }
  }

  public terminate() {
    if (this.worker) {
      this.worker.terminate();
      this.worker = null;
    }
    this.messageHandlers.clear();
  }

  public isReady(): boolean {
    return this.worker !== null;
  }
}
