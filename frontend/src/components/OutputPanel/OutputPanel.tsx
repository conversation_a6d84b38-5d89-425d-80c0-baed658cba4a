import React from "react";
import type { OutputPanelProps } from "../../types";

const OutputPanel: React.FC<OutputPanelProps> = ({
  output,
  error,
  isRunning,
  onClear,
}) => {
  const hasContent = output.length > 0 || error;

  return (
    <div className="bg-gray-50 border border-gray-300 rounded-lg p-4 font-mono text-sm min-h-[200px] max-h-[400px] overflow-y-auto">
      {/* Header with clear button */}
      <div className="flex justify-between items-center mb-3 pb-2 border-b border-gray-200">
        <h3 className="text-sm font-medium text-gray-700">Output</h3>
        {hasContent && (
          <button
            onClick={onClear}
            className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100 transition-colors"
            disabled={isRunning}
          >
            Clear
          </button>
        )}
      </div>

      {/* Loading state */}
      {isRunning && (
        <div className="flex items-center space-x-2 text-blue-600 mb-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-sm">Running code...</span>
        </div>
      )}

      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-3">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-red-800">Error</h4>
              <pre className="mt-1 text-sm text-red-700 whitespace-pre-wrap font-mono">
                {error}
              </pre>
            </div>
          </div>
        </div>
      )}

      {/* Output display */}
      {output.length > 0 && (
        <div className="space-y-1">
          {output.map((line, index) => (
            <div
              key={index}
              className="text-sm text-gray-800 font-mono"
            >
              {line || "\u00A0"} {/* Non-breaking space for empty lines */}
            </div>
          ))}
        </div>
      )}

      {/* Empty state */}
      {!hasContent && !isRunning && (
        <div className="text-center text-gray-500 py-8">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
          <p className="mt-2 text-sm">No output yet</p>
          <p className="text-xs text-gray-400">
            Run your Python code to see results here
          </p>
        </div>
      )}
    </div>
  );
};

export default OutputPanel;
