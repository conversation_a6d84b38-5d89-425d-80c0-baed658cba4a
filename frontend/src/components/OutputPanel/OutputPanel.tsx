import React from "react";
import type { OutputPanelProps } from "../../types";

const OutputPanel: React.FC<OutputPanelProps> = ({
  output,
  error,
  isRunning,
  onClear,
}) => {
  const hasContent = output.length > 0 || error;

  return (
    <div className="bg-gray-50 border border-gray-300 rounded-lg p-4 font-mono text-sm h-full overflow-y-auto">
      {/* Clear button - only show when there's content */}
      {hasContent && (
        <div className="flex justify-end mb-2">
          <button
            onClick={onClear}
            className="text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100 transition-colors"
            disabled={isRunning}
          >
            Clear
          </button>
        </div>
      )}

      {/* Loading state */}
      {isRunning && (
        <div className="flex items-center space-x-2 text-blue-600 mb-2">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
          <span className="text-sm">Running code...</span>
        </div>
      )}

      {/* Error display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3 mb-3">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg
                className="h-5 w-5 text-red-400"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h4 className="text-sm font-medium text-red-800">Error</h4>
              <pre className="mt-1 text-sm text-red-700 whitespace-pre-wrap font-mono">
                {error}
              </pre>
            </div>
          </div>
        </div>
      )}

      {/* Output display */}
      {output.length > 0 && (
        <div className="space-y-1">
          {output.map((line, index) => (
            <div
              key={index}
              className="text-sm text-gray-800 font-mono"
            >
              {line || "\u00A0"} {/* Non-breaking space for empty lines */}
            </div>
          ))}
        </div>
      )}

      {/* Empty state */}
      {!hasContent && !isRunning && (
        <div className="text-center text-gray-500 py-4">
          <p className="text-sm">No output yet</p>
          <p className="text-xs text-gray-400">
            Run your Python code to see results
          </p>
        </div>
      )}
    </div>
  );
};

export default OutputPanel;
