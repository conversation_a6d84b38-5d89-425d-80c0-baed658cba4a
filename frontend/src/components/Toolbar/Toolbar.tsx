import React from 'react';
import { ToolbarProps } from '../../types';

const Toolbar: React.FC<ToolbarProps> = ({
  onRun,
  onClear,
  isRunning,
  disabled = false
}) => {
  return (
    <div className="flex justify-center items-center space-x-4 py-4">
      {/* Run Button */}
      <button
        onClick={onRun}
        disabled={disabled || isRunning}
        className={`
          run-button flex items-center space-x-2 px-6 py-3 text-base font-medium
          ${disabled || isRunning 
            ? 'bg-gray-400 cursor-not-allowed' 
            : 'bg-blue-600 hover:bg-blue-700 active:bg-blue-800'
          }
          disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow-md
        `}
      >
        {isRunning ? (
          <>
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            <span>Running...</span>
          </>
        ) : (
          <>
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h1m4 0h1M9 6h6" />
            </svg>
            <span>Run Code</span>
          </>
        )}
      </button>

      {/* Clear Button */}
      <button
        onClick={onClear}
        disabled={disabled || isRunning}
        className={`
          px-4 py-3 text-base font-medium border border-gray-300 rounded-lg
          ${disabled || isRunning
            ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
            : 'bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400'
          }
          disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow-md
        `}
      >
        Clear Output
      </button>
    </div>
  );
};

export default Toolbar;
