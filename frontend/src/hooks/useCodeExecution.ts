import { useState, useEffect, useCallback, useRef } from "react";
import { CodeExecutionResult } from "../types";

export const useCodeExecution = () => {
  const [executionResult, setExecutionResult] = useState<CodeExecutionResult>({
    output: [],
    isRunning: false,
  });

  const [isWorkerReady, setIsWorkerReady] = useState(false);
  const workerRef = useRef<Worker | null>(null);
  const executionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize worker
  useEffect(() => {
    try {
      // Create worker from the TypeScript file
      // Vite will handle the worker compilation
      workerRef.current = new Worker(
        new URL("../workers/pythonWorker.ts", import.meta.url)
      );

      // Handle worker messages - boot.dev format
      workerRef.current.onmessage = (event: MessageEvent) => {
        const data = event.data;

        // Handle different message types from boot.dev approach
        if (data.message) {
          // Output message
          setExecutionResult((prev) => ({
            ...prev,
            output: [...prev.output, data.message],
          }));
        } else if (data.error) {
          // Error message
          setExecutionResult((prev) => ({
            ...prev,
            error: String(data.error),
            isRunning: false,
          }));
          // Clear timeout on error
          if (executionTimeoutRef.current) {
            clearTimeout(executionTimeoutRef.current);
            executionTimeoutRef.current = null;
          }
        } else if (data.done) {
          // Completion message - worker is ready
          setIsWorkerReady(true);
          setExecutionResult((prev) => ({
            ...prev,
            isRunning: false,
          }));
          // Clear timeout on completion
          if (executionTimeoutRef.current) {
            clearTimeout(executionTimeoutRef.current);
            executionTimeoutRef.current = null;
          }
        }
      };

      // Handle worker errors
      workerRef.current.onerror = (error) => {
        console.error("Worker error:", error);
        setExecutionResult((prev) => ({
          ...prev,
          error: "Worker error occurred",
          isRunning: false,
        }));
        setIsWorkerReady(false);
      };

      // Worker will auto-initialize (boot.dev approach)
    } catch (error) {
      console.error("Failed to create worker:", error);
      setExecutionResult((prev) => ({
        ...prev,
        error: "Failed to initialize Python environment",
        isRunning: false,
      }));
    }

    // Cleanup on unmount
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
      }
      if (executionTimeoutRef.current) {
        clearTimeout(executionTimeoutRef.current);
      }
    };
  }, []);

  // Execute Python code
  const executeCode = useCallback(
    (code: string) => {
      if (!workerRef.current || !isWorkerReady) {
        setExecutionResult((prev) => ({
          ...prev,
          error: "Python environment not ready. Please wait...",
          isRunning: false,
        }));
        return;
      }

      if (!code.trim()) {
        setExecutionResult((prev) => ({
          ...prev,
          error: "No code to execute",
          isRunning: false,
        }));
        return;
      }

      // Clear previous results and start execution
      setExecutionResult({
        output: [],
        error: undefined,
        isRunning: true,
        executionTime: Date.now(),
      });

      // Set execution timeout (30 seconds)
      executionTimeoutRef.current = setTimeout(() => {
        setExecutionResult((prev) => ({
          ...prev,
          error: "Code execution timed out (30 seconds)",
          isRunning: false,
        }));

        // Terminate and recreate worker on timeout
        if (workerRef.current) {
          workerRef.current.terminate();
          setIsWorkerReady(false);

          // Recreate worker
          setTimeout(() => {
            try {
              workerRef.current = new Worker(
                new URL("../workers/pythonWorker.ts", import.meta.url)
              );
              // Worker will auto-initialize
            } catch (error) {
              console.error("Failed to recreate worker:", error);
            }
          }, 1000);
        }
      }, 30000);

      // Send code directly to worker (boot.dev approach)
      workerRef.current.postMessage(code);
    },
    [isWorkerReady]
  );

  // Clear output
  const clearOutput = useCallback(() => {
    setExecutionResult({
      output: [],
      error: undefined,
      isRunning: false,
    });
  }, []);

  return {
    executionResult,
    executeCode,
    clearOutput,
    isWorkerReady,
  };
};
