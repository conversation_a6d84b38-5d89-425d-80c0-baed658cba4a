import { useState, useEffect, useCallback, useRef } from "react";
import { CodeExecutionResult, WorkerMessage } from "../types";

export const useCodeExecution = () => {
  const [executionResult, setExecutionResult] = useState<CodeExecutionResult>({
    output: [],
    isRunning: false,
  });

  const [isWorkerReady, setIsWorkerReady] = useState(false);
  const workerRef = useRef<Worker | null>(null);
  const executionTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize worker
  useEffect(() => {
    try {
      // Create worker from the TypeScript file
      // Vite will handle the worker compilation
      workerRef.current = new Worker(
        new URL("../workers/pythonWorker.ts", import.meta.url)
      );

      // Handle worker messages
      workerRef.current.onmessage = (event: MessageEvent<WorkerMessage>) => {
        const { type, data, error } = event.data;

        console.log({ type, data, error });

        switch (type) {
          case "ready":
            setIsWorkerReady(true);
            console.log("Python worker ready:", data);
            break;

          case "output":
            if (data) {
              setExecutionResult((prev) => ({
                ...prev,
                output: [...prev.output, data],
              }));
            }
            break;

          case "error":
            if (error) {
              setExecutionResult((prev) => ({
                ...prev,
                error: error,
                isRunning: false,
              }));
            }
            // Clear timeout on error
            if (executionTimeoutRef.current) {
              clearTimeout(executionTimeoutRef.current);
              executionTimeoutRef.current = null;
            }
            break;

          case "done":
            setExecutionResult((prev) => ({
              ...prev,
              isRunning: false,
            }));
            // Clear timeout on completion
            if (executionTimeoutRef.current) {
              clearTimeout(executionTimeoutRef.current);
              executionTimeoutRef.current = null;
            }
            break;

          default:
            console.warn("Unknown worker message type:", type);
        }
      };

      // Handle worker errors
      workerRef.current.onerror = (error) => {
        console.error("Worker error:", error);
        setExecutionResult((prev) => ({
          ...prev,
          error: "Worker error occurred",
          isRunning: false,
        }));
        setIsWorkerReady(false);
      };

      // Initialize the worker
      workerRef.current.postMessage({ type: "init" });
    } catch (error) {
      console.error("Failed to create worker:", error);
      setExecutionResult((prev) => ({
        ...prev,
        error: "Failed to initialize Python environment",
        isRunning: false,
      }));
    }

    // Cleanup on unmount
    return () => {
      if (workerRef.current) {
        workerRef.current.terminate();
      }
      if (executionTimeoutRef.current) {
        clearTimeout(executionTimeoutRef.current);
      }
    };
  }, []);

  // Execute Python code
  const executeCode = useCallback(
    (code: string) => {
      if (!workerRef.current || !isWorkerReady) {
        setExecutionResult((prev) => ({
          ...prev,
          error: "Python environment not ready. Please wait...",
          isRunning: false,
        }));
        return;
      }

      if (!code.trim()) {
        setExecutionResult((prev) => ({
          ...prev,
          error: "No code to execute",
          isRunning: false,
        }));
        return;
      }

      // Clear previous results and start execution
      setExecutionResult({
        output: [],
        error: undefined,
        isRunning: true,
        executionTime: Date.now(),
      });

      // Set execution timeout (30 seconds)
      executionTimeoutRef.current = setTimeout(() => {
        setExecutionResult((prev) => ({
          ...prev,
          error: "Code execution timed out (30 seconds)",
          isRunning: false,
        }));

        // Terminate and recreate worker on timeout
        if (workerRef.current) {
          workerRef.current.terminate();
          setIsWorkerReady(false);

          // Recreate worker
          setTimeout(() => {
            try {
              workerRef.current = new Worker(
                new URL("../workers/pythonWorker.ts", import.meta.url)
              );
              workerRef.current.postMessage({ type: "init" });
            } catch (error) {
              console.error("Failed to recreate worker:", error);
            }
          }, 1000);
        }
      }, 30000);

      // Send code to worker
      workerRef.current.postMessage({
        type: "execute",
        data: code,
      });
    },
    [isWorkerReady]
  );

  // Clear output
  const clearOutput = useCallback(() => {
    setExecutionResult({
      output: [],
      error: undefined,
      isRunning: false,
    });
  }, []);

  return {
    executionResult,
    executeCode,
    clearOutput,
    isWorkerReady,
  };
};
