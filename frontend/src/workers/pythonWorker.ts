// Python execution worker using Pyodide
import { WorkerMessage } from "../types";

declare const self: DedicatedWorkerGlobalScope & {
  importScripts: (...urls: string[]) => void;
  loadPyodide: (config: any) => Promise<any>;
};

let pyodide: any = null;
let isInitialized = false;

// Load Pyodide from CDN
async function loadPyodide() {
  if (isInitialized) return;

  try {
    // Import Pyodide from CDN using importScripts (classic worker)
    self.importScripts(
      "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js"
    );

    // Initialize Pyodide
    pyodide = await self.loadPyodide({
      indexURL: "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/",
    });

    // Setup stdout/stderr capture
    pyodide.runPython(`
      import sys
      import io
      from js import console
      
      class OutputCapture:
          def __init__(self):
              self.output = []
          
          def write(self, text):
              if text.strip():
                  self.output.append(text.rstrip())
          
          def flush(self):
              pass
          
          def get_output(self):
              result = self.output.copy()
              self.output.clear()
              return result
      
      # Create output capture instances
      stdout_capture = OutputCapture()
      stderr_capture = OutputCapture()
      
      # Redirect stdout and stderr
      sys.stdout = stdout_capture
      sys.stderr = stderr_capture
    `);

    isInitialized = true;

    // Notify main thread that worker is ready
    const message: WorkerMessage = {
      type: "ready",
      data: "Pyodide loaded successfully",
    };
    self.postMessage(message);
  } catch (error) {
    const message: WorkerMessage = {
      type: "error",
      error: `Failed to load Pyodide: ${error}`,
    };
    self.postMessage(message);
  }
}

// Execute Python code
async function executePythonCode(code: string) {
  if (!isInitialized || !pyodide) {
    const message: WorkerMessage = {
      type: "error",
      error: "Pyodide not initialized",
    };
    self.postMessage(message);
    return;
  }

  try {
    // Clear previous output
    pyodide.runPython(`
      stdout_capture.output.clear()
      stderr_capture.output.clear()
    `);

    // Execute the user code
    pyodide.runPython(code);

    // Get captured output
    const stdout = pyodide.runPython("stdout_capture.get_output()");
    const stderr = pyodide.runPython("stderr_capture.get_output()");

    // Send stdout output
    if (stdout && stdout.length > 0) {
      for (const line of stdout) {
        const message: WorkerMessage = {
          type: "output",
          data: line,
        };
        self.postMessage(message);
      }
    }

    // Send stderr as errors
    if (stderr && stderr.length > 0) {
      const message: WorkerMessage = {
        type: "error",
        error: stderr.join("\n"),
      };
      self.postMessage(message);
    }

    // Send completion signal
    const message: WorkerMessage = {
      type: "done",
    };
    self.postMessage(message);
  } catch (error) {
    // Handle Python execution errors
    const message: WorkerMessage = {
      type: "error",
      error: String(error),
    };
    self.postMessage(message);

    // Still send done signal
    const doneMessage: WorkerMessage = {
      type: "done",
    };
    self.postMessage(doneMessage);
  }
}

// Handle messages from main thread
self.addEventListener("message", async (event: MessageEvent) => {
  const { type, data } = event.data;

  switch (type) {
    case "init":
      await loadPyodide();
      break;

    case "execute":
      await executePythonCode(data);
      break;

    default:
      const message: WorkerMessage = {
        type: "error",
        error: `Unknown message type: ${type}`,
      };
      self.postMessage(message);
  }
});

// Initialize Pyodide when worker starts
loadPyodide();
