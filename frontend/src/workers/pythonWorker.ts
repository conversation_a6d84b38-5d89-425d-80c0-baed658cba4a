/* eslint-disable */
// Boot.dev's exact Python worker implementation
declare const self: any;

self.importScripts("https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js");

// https://pyodide.org/en/stable/usage/webworker.html
async function loadPyodideAndPackages() {
  self.pyodide = await self.loadPyodide({
    indexURL: "https://cdn.jsdelivr.net/pyodide/v0.23.4/full/",
  });
  await self.pyodide.loadPackage("pyodide-http");
}
const pyodideReadyPromise = loadPyodideAndPackages();

let stdOutBuf = "";
self.writeStdOut = (chunk: string) => {
  if (!chunk) {
    if (stdOutBuf) self.postMessage({ message: stdOutBuf });
    stdOutBuf = "";
    return;
  }

  stdOutBuf += chunk;
  let nl;
  while ((nl = stdOutBuf.indexOf("\n")) !== -1) {
    self.postMessage({ message: stdOutBuf.slice(0, nl) });
    stdOutBuf = stdOutBuf.slice(nl + 1);
  }
};

self.runCode = async (code: string) => {
  try {
    const ns = self.pyodide.globals.get("dict")();
    ns.set("writeStdOut", self.pyodide.globals.get("writeStdOut"));

    const isAsync = /(^|\n)\s*async\s+def\s+/m.test(code);

    const header = `
import sys, io
from js import writeStdOut

class StdoutFile:
  def write(self, s): writeStdOut(s)
  def flush(self): pass

sys.stdout = sys.stderr = StdoutFile()
`;

    if (isAsync) {
      const wrapped = `
${header}
import gc, warnings, asyncio

warnings.filterwarnings(
    "ignore",
    category=RuntimeWarning,
    message=r"coroutine '.*' was never awaited",
)
gc.collect()

${code}
`;
      await self.pyodide.runPythonAsync(wrapped, { globals: ns });
    } else {
      const wrapped = `
${header}
${code}
`;
      self.pyodide.runPython(wrapped, { globals: ns });
    }
  } catch (err) {
    self.postMessage({ error: `${trimTraceback(err)}` });
  } finally {
    self.writeStdOut("");
  }
};

self.addEventListener(
  "message",
  async (e: MessageEvent) => {
    await pyodideReadyPromise;

    if (e.data.type === "WORKER_READY") {
      self.postMessage({
        ready: true,
      });
      return;
    }

    if (e.data.type === "EXEC_CODE") {
      await self.runCode(e.data.code);
      self.postMessage({
        done: true,
      });
      return;
    }

    // Simple code execution (for compatibility)
    if (typeof e.data === "string") {
      await self.runCode(e.data);
      self.postMessage({
        done: true,
      });
    }
  },
  false
);

const trimTraceback = (err: any) => {
  if (err?.message) {
    err.message = err.message.replace(
      /File "\/lib(.|\n)*(File "<exec>)/gm,
      "$2"
    );
  }
  return err;
};
