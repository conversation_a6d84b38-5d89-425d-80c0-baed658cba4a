// Python execution worker using Pyodide
import { WorkerMessage } from "../types";

declare const self: DedicatedWorkerGlobalScope & {
  importScripts: (...urls: string[]) => void;
  loadPyodide: (config: any) => Promise<any>;
};

let pyodide: any = null;
let isInitialized = false;

// Load Pyodide from CDN
async function loadPyodide() {
  if (isInitialized) return;

  try {
    // Import Pyodide from CDN using importScripts (classic worker)
    self.importScripts(
      "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js"
    );

    // Initialize Pyodide
    pyodide = await self.loadPyodide({
      indexURL: "https://cdn.jsdelivr.net/pyodide/v0.24.1/full/",
    });

    // Setup stdout/stderr capture using boot.dev approach
    pyodide.runPython(`
      import io, sys
      sys.stdout = io.StringIO()
      sys.stderr = io.StringIO()
    `);

    isInitialized = true;

    // Notify main thread that worker is ready
    const message: WorkerMessage = {
      type: "ready",
      data: "Pyodide loaded successfully",
    };
    self.postMessage(message);
  } catch (error) {
    const message: WorkerMessage = {
      type: "error",
      error: `Failed to load Pyodide: ${error}`,
    };
    self.postMessage(message);
  }
}

// Execute Python code using boot.dev approach
function executePythonCode(code: string) {
  if (!isInitialized || !pyodide) {
    const message: WorkerMessage = {
      type: "error",
      error: "Pyodide not initialized",
    };
    self.postMessage(message);
    return;
  }

  try {
    // Clear previous output (reset StringIO) - boot.dev approach
    pyodide.runPython(`
      import io, sys
      sys.stdout = io.StringIO()
      sys.stderr = io.StringIO()
    `);

    // Execute the user code
    pyodide.runPython(code);

    // Capture stdout using boot.dev approach
    let stdout = pyodide.runPython("sys.stdout.getvalue()");
    if (stdout) {
      stdout = stdout.split("\n");
      for (const line of stdout) {
        if (line.trim()) {
          // Only send non-empty lines
          const message: WorkerMessage = {
            type: "output",
            data: line,
          };
          self.postMessage(message);
        }
      }
    }

    // Capture stderr
    let stderr = pyodide.runPython("sys.stderr.getvalue()");
    if (stderr && stderr.trim()) {
      const message: WorkerMessage = {
        type: "error",
        error: stderr,
      };
      self.postMessage(message);
    }

    // Send completion signal
    const message: WorkerMessage = {
      type: "done",
    };
    self.postMessage(message);
  } catch (error) {
    // Handle Python execution errors
    const message: WorkerMessage = {
      type: "error",
      error: String(error),
    };
    self.postMessage(message);

    // Still send done signal
    const doneMessage: WorkerMessage = {
      type: "done",
    };
    self.postMessage(doneMessage);
  }
}

// Handle messages from main thread
self.addEventListener("message", async (event: MessageEvent) => {
  const { type, data } = event.data;

  switch (type) {
    case "init":
      await loadPyodide();
      break;

    case "execute":
      executePythonCode(data);
      break;

    default:
      const message: WorkerMessage = {
        type: "error",
        error: `Unknown message type: ${type}`,
      };
      self.postMessage(message);
  }
});

// Initialize Pyodide when worker starts
loadPyodide();
