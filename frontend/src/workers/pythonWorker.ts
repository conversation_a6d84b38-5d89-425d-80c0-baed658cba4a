// Python execution worker using Pyodide - boot.dev approach

declare const self: any;

let pyodide: any = null;
let isInitialized = false;

// Load Pyodide from CDN
async function loadPyodide() {
  if (isInitialized) return;

  try {
    // Use the exact same Pyodide version as boot.dev
    self.importScripts(
      "https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js"
    );

    // Wait for the interpreter to be fully loaded (boot.dev approach)
    await (self as any).languagePluginLoader;

    // Pyodide is now available as a global
    pyodide = (self as any).pyodide;

    // Setup stdout/stderr capture using boot.dev approach
    pyodide.runPython(`
      import io, sys
      sys.stdout = io.StringIO()
      sys.stderr = io.StringIO()
    `);

    isInitialized = true;
  } catch (error) {
    console.error("Failed to load Pyodide:", error);
  }
}

// Handle messages from main thread - boot.dev approach
self.addEventListener("message", async (event: MessageEvent) => {
  // Wait for the interpreter to be fully loaded
  await (self as any).languagePluginLoader;

  // Define the execution function in the worker scope
  (self as any).runPythonWithStdout = () => {
    try {
      // Execute the code passed to the worker
      pyodide.runPython(event.data);
    } catch (err) {
      self.postMessage({
        error: err,
      });
      return;
    }

    // Capture the code's standard output and send it back to the main thread
    let stdout = pyodide.runPython("sys.stdout.getvalue()");
    if (stdout) {
      stdout = stdout.split("\n");
      for (const line of stdout) {
        if (line.trim()) {
          self.postMessage({
            message: line,
          });
        }
      }
    }
  };

  // Redirect stdout to io.StringIO so that we can get it later
  pyodide.runPython(`
    import io, code, sys
    from js import runPythonWithStdout
    sys.stdout = io.StringIO()
    sys.stderr = io.StringIO()
    ## This runs self.runPythonWithStdout defined in the JS
    runPythonWithStdout()
  `);

  self.postMessage({
    done: true,
  });
});

// Initialize Pyodide when worker starts
loadPyodide();
