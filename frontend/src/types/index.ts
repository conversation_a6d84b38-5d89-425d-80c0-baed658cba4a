// Types for the Python IDE playground

export interface CodeExecutionResult {
  output: string[];
  error?: string;
  isRunning: boolean;
  executionTime?: number;
}

export interface WorkerMessage {
  type: 'output' | 'error' | 'done' | 'ready';
  data?: string;
  error?: string;
}

export interface CodeEditorProps {
  value: string;
  onChange: (value: string) => void;
  language?: string;
  readOnly?: boolean;
  className?: string;
}

export interface OutputPanelProps {
  output: string[];
  error?: string;
  isRunning: boolean;
  onClear: () => void;
}

export interface ToolbarProps {
  onRun: () => void;
  onClear: () => void;
  isRunning: boolean;
  disabled?: boolean;
}

export interface LayoutProps {
  children: React.ReactNode;
}

// Pyodide-related types
export interface PyodideInterface {
  runPython: (code: string) => any;
  loadPackage: (packages: string | string[]) => Promise<void>;
  globals: any;
  FS: any;
}

declare global {
  interface Window {
    loadPyodide: () => Promise<PyodideInterface>;
  }
}
