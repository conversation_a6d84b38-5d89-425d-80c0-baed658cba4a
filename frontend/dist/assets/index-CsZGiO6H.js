(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const a of document.querySelectorAll('link[rel="modulepreload"]'))l(a);new MutationObserver(a=>{for(const o of a)if(o.type==="childList")for(const h of o.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&l(h)}).observe(document,{childList:!0,subtree:!0});function i(a){const o={};return a.integrity&&(o.integrity=a.integrity),a.referrerPolicy&&(o.referrerPolicy=a.referrerPolicy),a.crossOrigin==="use-credentials"?o.credentials="include":a.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function l(a){if(a.ep)return;a.ep=!0;const o=i(a);fetch(a.href,o)}})();var rf={exports:{}},aa={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Cm;function av(){if(Cm)return aa;Cm=1;var r=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function i(l,a,o){var h=null;if(o!==void 0&&(h=""+o),a.key!==void 0&&(h=""+a.key),"key"in a){o={};for(var c in a)c!=="key"&&(o[c]=a[c])}else o=a;return a=o.ref,{$$typeof:r,type:l,key:h,ref:a!==void 0?a:null,props:o}}return aa.Fragment=t,aa.jsx=i,aa.jsxs=i,aa}var Dm;function rv(){return Dm||(Dm=1,rf.exports=av()),rf.exports}var st=rv(),of={exports:{}},ht={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qm;function ov(){if(qm)return ht;qm=1;var r=Symbol.for("react.transitional.element"),t=Symbol.for("react.portal"),i=Symbol.for("react.fragment"),l=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),o=Symbol.for("react.consumer"),h=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),d=Symbol.for("react.suspense"),p=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),O=Symbol.iterator;function S(w){return w===null||typeof w!="object"?null:(w=O&&w[O]||w["@@iterator"],typeof w=="function"?w:null)}var b={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},T=Object.assign,z={};function C(w,U,V){this.props=w,this.context=U,this.refs=z,this.updater=V||b}C.prototype.isReactComponent={},C.prototype.setState=function(w,U){if(typeof w!="object"&&typeof w!="function"&&w!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,w,U,"setState")},C.prototype.forceUpdate=function(w){this.updater.enqueueForceUpdate(this,w,"forceUpdate")};function q(){}q.prototype=C.prototype;function $(w,U,V){this.props=w,this.context=U,this.refs=z,this.updater=V||b}var j=$.prototype=new q;j.constructor=$,T(j,C.prototype),j.isPureReactComponent=!0;var W=Array.isArray,X={H:null,A:null,T:null,S:null,V:null},J=Object.prototype.hasOwnProperty;function Y(w,U,V,P,tt,ft){return V=ft.ref,{$$typeof:r,type:w,key:U,ref:V!==void 0?V:null,props:ft}}function Ot(w,U){return Y(w.type,U,void 0,void 0,void 0,w.props)}function gt(w){return typeof w=="object"&&w!==null&&w.$$typeof===r}function Et(w){var U={"=":"=0",":":"=2"};return"$"+w.replace(/[=:]/g,function(V){return U[V]})}var ot=/\/+/g;function F(w,U){return typeof w=="object"&&w!==null&&w.key!=null?Et(""+w.key):U.toString(36)}function xt(){}function Qt(w){switch(w.status){case"fulfilled":return w.value;case"rejected":throw w.reason;default:switch(typeof w.status=="string"?w.then(xt,xt):(w.status="pending",w.then(function(U){w.status==="pending"&&(w.status="fulfilled",w.value=U)},function(U){w.status==="pending"&&(w.status="rejected",w.reason=U)})),w.status){case"fulfilled":return w.value;case"rejected":throw w.reason}}throw w}function vt(w,U,V,P,tt){var ft=typeof w;(ft==="undefined"||ft==="boolean")&&(w=null);var at=!1;if(w===null)at=!0;else switch(ft){case"bigint":case"string":case"number":at=!0;break;case"object":switch(w.$$typeof){case r:case t:at=!0;break;case m:return at=w._init,vt(at(w._payload),U,V,P,tt)}}if(at)return tt=tt(w),at=P===""?"."+F(w,0):P,W(tt)?(V="",at!=null&&(V=at.replace(ot,"$&/")+"/"),vt(tt,U,V,"",function(Fi){return Fi})):tt!=null&&(gt(tt)&&(tt=Ot(tt,V+(tt.key==null||w&&w.key===tt.key?"":(""+tt.key).replace(ot,"$&/")+"/")+at)),U.push(tt)),1;at=0;var me=P===""?".":P+":";if(W(w))for(var Vt=0;Vt<w.length;Vt++)P=w[Vt],ft=me+F(P,Vt),at+=vt(P,U,V,ft,tt);else if(Vt=S(w),typeof Vt=="function")for(w=Vt.call(w),Vt=0;!(P=w.next()).done;)P=P.value,ft=me+F(P,Vt++),at+=vt(P,U,V,ft,tt);else if(ft==="object"){if(typeof w.then=="function")return vt(Qt(w),U,V,P,tt);throw U=String(w),Error("Objects are not valid as a React child (found: "+(U==="[object Object]"?"object with keys {"+Object.keys(w).join(", ")+"}":U)+"). If you meant to render a collection of children, use an array instead.")}return at}function D(w,U,V){if(w==null)return w;var P=[],tt=0;return vt(w,P,"","",function(ft){return U.call(V,ft,tt++)}),P}function L(w){if(w._status===-1){var U=w._result;U=U(),U.then(function(V){(w._status===0||w._status===-1)&&(w._status=1,w._result=V)},function(V){(w._status===0||w._status===-1)&&(w._status=2,w._result=V)}),w._status===-1&&(w._status=0,w._result=U)}if(w._status===1)return w._result.default;throw w._result}var it=typeof reportError=="function"?reportError:function(w){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var U=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof w=="object"&&w!==null&&typeof w.message=="string"?String(w.message):String(w),error:w});if(!window.dispatchEvent(U))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",w);return}console.error(w)};function St(){}return ht.Children={map:D,forEach:function(w,U,V){D(w,function(){U.apply(this,arguments)},V)},count:function(w){var U=0;return D(w,function(){U++}),U},toArray:function(w){return D(w,function(U){return U})||[]},only:function(w){if(!gt(w))throw Error("React.Children.only expected to receive a single React element child.");return w}},ht.Component=C,ht.Fragment=i,ht.Profiler=a,ht.PureComponent=$,ht.StrictMode=l,ht.Suspense=d,ht.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=X,ht.__COMPILER_RUNTIME={__proto__:null,c:function(w){return X.H.useMemoCache(w)}},ht.cache=function(w){return function(){return w.apply(null,arguments)}},ht.cloneElement=function(w,U,V){if(w==null)throw Error("The argument must be a React element, but you passed "+w+".");var P=T({},w.props),tt=w.key,ft=void 0;if(U!=null)for(at in U.ref!==void 0&&(ft=void 0),U.key!==void 0&&(tt=""+U.key),U)!J.call(U,at)||at==="key"||at==="__self"||at==="__source"||at==="ref"&&U.ref===void 0||(P[at]=U[at]);var at=arguments.length-2;if(at===1)P.children=V;else if(1<at){for(var me=Array(at),Vt=0;Vt<at;Vt++)me[Vt]=arguments[Vt+2];P.children=me}return Y(w.type,tt,void 0,void 0,ft,P)},ht.createContext=function(w){return w={$$typeof:h,_currentValue:w,_currentValue2:w,_threadCount:0,Provider:null,Consumer:null},w.Provider=w,w.Consumer={$$typeof:o,_context:w},w},ht.createElement=function(w,U,V){var P,tt={},ft=null;if(U!=null)for(P in U.key!==void 0&&(ft=""+U.key),U)J.call(U,P)&&P!=="key"&&P!=="__self"&&P!=="__source"&&(tt[P]=U[P]);var at=arguments.length-2;if(at===1)tt.children=V;else if(1<at){for(var me=Array(at),Vt=0;Vt<at;Vt++)me[Vt]=arguments[Vt+2];tt.children=me}if(w&&w.defaultProps)for(P in at=w.defaultProps,at)tt[P]===void 0&&(tt[P]=at[P]);return Y(w,ft,void 0,void 0,null,tt)},ht.createRef=function(){return{current:null}},ht.forwardRef=function(w){return{$$typeof:c,render:w}},ht.isValidElement=gt,ht.lazy=function(w){return{$$typeof:m,_payload:{_status:-1,_result:w},_init:L}},ht.memo=function(w,U){return{$$typeof:p,type:w,compare:U===void 0?null:U}},ht.startTransition=function(w){var U=X.T,V={};X.T=V;try{var P=w(),tt=X.S;tt!==null&&tt(V,P),typeof P=="object"&&P!==null&&typeof P.then=="function"&&P.then(St,it)}catch(ft){it(ft)}finally{X.T=U}},ht.unstable_useCacheRefresh=function(){return X.H.useCacheRefresh()},ht.use=function(w){return X.H.use(w)},ht.useActionState=function(w,U,V){return X.H.useActionState(w,U,V)},ht.useCallback=function(w,U){return X.H.useCallback(w,U)},ht.useContext=function(w){return X.H.useContext(w)},ht.useDebugValue=function(){},ht.useDeferredValue=function(w,U){return X.H.useDeferredValue(w,U)},ht.useEffect=function(w,U,V){var P=X.H;if(typeof V=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return P.useEffect(w,U)},ht.useId=function(){return X.H.useId()},ht.useImperativeHandle=function(w,U,V){return X.H.useImperativeHandle(w,U,V)},ht.useInsertionEffect=function(w,U){return X.H.useInsertionEffect(w,U)},ht.useLayoutEffect=function(w,U){return X.H.useLayoutEffect(w,U)},ht.useMemo=function(w,U){return X.H.useMemo(w,U)},ht.useOptimistic=function(w,U){return X.H.useOptimistic(w,U)},ht.useReducer=function(w,U,V){return X.H.useReducer(w,U,V)},ht.useRef=function(w){return X.H.useRef(w)},ht.useState=function(w){return X.H.useState(w)},ht.useSyncExternalStore=function(w,U,V){return X.H.useSyncExternalStore(w,U,V)},ht.useTransition=function(){return X.H.useTransition()},ht.version="19.1.0",ht}var _m;function Oc(){return _m||(_m=1,of.exports=ov()),of.exports}var Le=Oc(),uf={exports:{}},ra={},hf={exports:{}},ff={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nm;function uv(){return Nm||(Nm=1,function(r){function t(D,L){var it=D.length;D.push(L);t:for(;0<it;){var St=it-1>>>1,w=D[St];if(0<a(w,L))D[St]=L,D[it]=w,it=St;else break t}}function i(D){return D.length===0?null:D[0]}function l(D){if(D.length===0)return null;var L=D[0],it=D.pop();if(it!==L){D[0]=it;t:for(var St=0,w=D.length,U=w>>>1;St<U;){var V=2*(St+1)-1,P=D[V],tt=V+1,ft=D[tt];if(0>a(P,it))tt<w&&0>a(ft,P)?(D[St]=ft,D[tt]=it,St=tt):(D[St]=P,D[V]=it,St=V);else if(tt<w&&0>a(ft,it))D[St]=ft,D[tt]=it,St=tt;else break t}}return L}function a(D,L){var it=D.sortIndex-L.sortIndex;return it!==0?it:D.id-L.id}if(r.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var o=performance;r.unstable_now=function(){return o.now()}}else{var h=Date,c=h.now();r.unstable_now=function(){return h.now()-c}}var d=[],p=[],m=1,O=null,S=3,b=!1,T=!1,z=!1,C=!1,q=typeof setTimeout=="function"?setTimeout:null,$=typeof clearTimeout=="function"?clearTimeout:null,j=typeof setImmediate<"u"?setImmediate:null;function W(D){for(var L=i(p);L!==null;){if(L.callback===null)l(p);else if(L.startTime<=D)l(p),L.sortIndex=L.expirationTime,t(d,L);else break;L=i(p)}}function X(D){if(z=!1,W(D),!T)if(i(d)!==null)T=!0,J||(J=!0,F());else{var L=i(p);L!==null&&vt(X,L.startTime-D)}}var J=!1,Y=-1,Ot=5,gt=-1;function Et(){return C?!0:!(r.unstable_now()-gt<Ot)}function ot(){if(C=!1,J){var D=r.unstable_now();gt=D;var L=!0;try{t:{T=!1,z&&(z=!1,$(Y),Y=-1),b=!0;var it=S;try{e:{for(W(D),O=i(d);O!==null&&!(O.expirationTime>D&&Et());){var St=O.callback;if(typeof St=="function"){O.callback=null,S=O.priorityLevel;var w=St(O.expirationTime<=D);if(D=r.unstable_now(),typeof w=="function"){O.callback=w,W(D),L=!0;break e}O===i(d)&&l(d),W(D)}else l(d);O=i(d)}if(O!==null)L=!0;else{var U=i(p);U!==null&&vt(X,U.startTime-D),L=!1}}break t}finally{O=null,S=it,b=!1}L=void 0}}finally{L?F():J=!1}}}var F;if(typeof j=="function")F=function(){j(ot)};else if(typeof MessageChannel<"u"){var xt=new MessageChannel,Qt=xt.port2;xt.port1.onmessage=ot,F=function(){Qt.postMessage(null)}}else F=function(){q(ot,0)};function vt(D,L){Y=q(function(){D(r.unstable_now())},L)}r.unstable_IdlePriority=5,r.unstable_ImmediatePriority=1,r.unstable_LowPriority=4,r.unstable_NormalPriority=3,r.unstable_Profiling=null,r.unstable_UserBlockingPriority=2,r.unstable_cancelCallback=function(D){D.callback=null},r.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Ot=0<D?Math.floor(1e3/D):5},r.unstable_getCurrentPriorityLevel=function(){return S},r.unstable_next=function(D){switch(S){case 1:case 2:case 3:var L=3;break;default:L=S}var it=S;S=L;try{return D()}finally{S=it}},r.unstable_requestPaint=function(){C=!0},r.unstable_runWithPriority=function(D,L){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var it=S;S=D;try{return L()}finally{S=it}},r.unstable_scheduleCallback=function(D,L,it){var St=r.unstable_now();switch(typeof it=="object"&&it!==null?(it=it.delay,it=typeof it=="number"&&0<it?St+it:St):it=St,D){case 1:var w=-1;break;case 2:w=250;break;case 5:w=1073741823;break;case 4:w=1e4;break;default:w=5e3}return w=it+w,D={id:m++,callback:L,priorityLevel:D,startTime:it,expirationTime:w,sortIndex:-1},it>St?(D.sortIndex=it,t(p,D),i(d)===null&&D===i(p)&&(z?($(Y),Y=-1):z=!0,vt(X,it-St))):(D.sortIndex=w,t(d,D),T||b||(T=!0,J||(J=!0,F()))),D},r.unstable_shouldYield=Et,r.unstable_wrapCallback=function(D){var L=S;return function(){var it=S;S=L;try{return D.apply(this,arguments)}finally{S=it}}}}(ff)),ff}var Um;function hv(){return Um||(Um=1,hf.exports=uv()),hf.exports}var cf={exports:{}},Se={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bm;function fv(){if(Bm)return Se;Bm=1;var r=Oc();function t(d){var p="https://react.dev/errors/"+d;if(1<arguments.length){p+="?args[]="+encodeURIComponent(arguments[1]);for(var m=2;m<arguments.length;m++)p+="&args[]="+encodeURIComponent(arguments[m])}return"Minified React error #"+d+"; visit "+p+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(){}var l={d:{f:i,r:function(){throw Error(t(522))},D:i,C:i,L:i,m:i,X:i,S:i,M:i},p:0,findDOMNode:null},a=Symbol.for("react.portal");function o(d,p,m){var O=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:a,key:O==null?null:""+O,children:d,containerInfo:p,implementation:m}}var h=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(d,p){if(d==="font")return"";if(typeof p=="string")return p==="use-credentials"?p:""}return Se.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,Se.createPortal=function(d,p){var m=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!p||p.nodeType!==1&&p.nodeType!==9&&p.nodeType!==11)throw Error(t(299));return o(d,p,null,m)},Se.flushSync=function(d){var p=h.T,m=l.p;try{if(h.T=null,l.p=2,d)return d()}finally{h.T=p,l.p=m,l.d.f()}},Se.preconnect=function(d,p){typeof d=="string"&&(p?(p=p.crossOrigin,p=typeof p=="string"?p==="use-credentials"?p:"":void 0):p=null,l.d.C(d,p))},Se.prefetchDNS=function(d){typeof d=="string"&&l.d.D(d)},Se.preinit=function(d,p){if(typeof d=="string"&&p&&typeof p.as=="string"){var m=p.as,O=c(m,p.crossOrigin),S=typeof p.integrity=="string"?p.integrity:void 0,b=typeof p.fetchPriority=="string"?p.fetchPriority:void 0;m==="style"?l.d.S(d,typeof p.precedence=="string"?p.precedence:void 0,{crossOrigin:O,integrity:S,fetchPriority:b}):m==="script"&&l.d.X(d,{crossOrigin:O,integrity:S,fetchPriority:b,nonce:typeof p.nonce=="string"?p.nonce:void 0})}},Se.preinitModule=function(d,p){if(typeof d=="string")if(typeof p=="object"&&p!==null){if(p.as==null||p.as==="script"){var m=c(p.as,p.crossOrigin);l.d.M(d,{crossOrigin:m,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0})}}else p==null&&l.d.M(d)},Se.preload=function(d,p){if(typeof d=="string"&&typeof p=="object"&&p!==null&&typeof p.as=="string"){var m=p.as,O=c(m,p.crossOrigin);l.d.L(d,m,{crossOrigin:O,integrity:typeof p.integrity=="string"?p.integrity:void 0,nonce:typeof p.nonce=="string"?p.nonce:void 0,type:typeof p.type=="string"?p.type:void 0,fetchPriority:typeof p.fetchPriority=="string"?p.fetchPriority:void 0,referrerPolicy:typeof p.referrerPolicy=="string"?p.referrerPolicy:void 0,imageSrcSet:typeof p.imageSrcSet=="string"?p.imageSrcSet:void 0,imageSizes:typeof p.imageSizes=="string"?p.imageSizes:void 0,media:typeof p.media=="string"?p.media:void 0})}},Se.preloadModule=function(d,p){if(typeof d=="string")if(p){var m=c(p.as,p.crossOrigin);l.d.m(d,{as:typeof p.as=="string"&&p.as!=="script"?p.as:void 0,crossOrigin:m,integrity:typeof p.integrity=="string"?p.integrity:void 0})}else l.d.m(d)},Se.requestFormReset=function(d){l.d.r(d)},Se.unstable_batchedUpdates=function(d,p){return d(p)},Se.useFormState=function(d,p,m){return h.H.useFormState(d,p,m)},Se.useFormStatus=function(){return h.H.useHostTransitionStatus()},Se.version="19.1.0",Se}var Xm;function cv(){if(Xm)return cf.exports;Xm=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(t){console.error(t)}}return r(),cf.exports=fv(),cf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Pm;function dv(){if(Pm)return ra;Pm=1;var r=hv(),t=Oc(),i=cv();function l(e){var n="https://react.dev/errors/"+e;if(1<arguments.length){n+="?args[]="+encodeURIComponent(arguments[1]);for(var s=2;s<arguments.length;s++)n+="&args[]="+encodeURIComponent(arguments[s])}return"Minified React error #"+e+"; visit "+n+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function o(e){var n=e,s=e;if(e.alternate)for(;n.return;)n=n.return;else{e=n;do n=e,(n.flags&4098)!==0&&(s=n.return),e=n.return;while(e)}return n.tag===3?s:null}function h(e){if(e.tag===13){var n=e.memoizedState;if(n===null&&(e=e.alternate,e!==null&&(n=e.memoizedState)),n!==null)return n.dehydrated}return null}function c(e){if(o(e)!==e)throw Error(l(188))}function d(e){var n=e.alternate;if(!n){if(n=o(e),n===null)throw Error(l(188));return n!==e?null:e}for(var s=e,u=n;;){var f=s.return;if(f===null)break;var g=f.alternate;if(g===null){if(u=f.return,u!==null){s=u;continue}break}if(f.child===g.child){for(g=f.child;g;){if(g===s)return c(f),e;if(g===u)return c(f),n;g=g.sibling}throw Error(l(188))}if(s.return!==u.return)s=f,u=g;else{for(var y=!1,v=f.child;v;){if(v===s){y=!0,s=f,u=g;break}if(v===u){y=!0,u=f,s=g;break}v=v.sibling}if(!y){for(v=g.child;v;){if(v===s){y=!0,s=g,u=f;break}if(v===u){y=!0,u=g,s=f;break}v=v.sibling}if(!y)throw Error(l(189))}}if(s.alternate!==u)throw Error(l(190))}if(s.tag!==3)throw Error(l(188));return s.stateNode.current===s?e:n}function p(e){var n=e.tag;if(n===5||n===26||n===27||n===6)return e;for(e=e.child;e!==null;){if(n=p(e),n!==null)return n;e=e.sibling}return null}var m=Object.assign,O=Symbol.for("react.element"),S=Symbol.for("react.transitional.element"),b=Symbol.for("react.portal"),T=Symbol.for("react.fragment"),z=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),q=Symbol.for("react.provider"),$=Symbol.for("react.consumer"),j=Symbol.for("react.context"),W=Symbol.for("react.forward_ref"),X=Symbol.for("react.suspense"),J=Symbol.for("react.suspense_list"),Y=Symbol.for("react.memo"),Ot=Symbol.for("react.lazy"),gt=Symbol.for("react.activity"),Et=Symbol.for("react.memo_cache_sentinel"),ot=Symbol.iterator;function F(e){return e===null||typeof e!="object"?null:(e=ot&&e[ot]||e["@@iterator"],typeof e=="function"?e:null)}var xt=Symbol.for("react.client.reference");function Qt(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===xt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case T:return"Fragment";case C:return"Profiler";case z:return"StrictMode";case X:return"Suspense";case J:return"SuspenseList";case gt:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case b:return"Portal";case j:return(e.displayName||"Context")+".Provider";case $:return(e._context.displayName||"Context")+".Consumer";case W:var n=e.render;return e=e.displayName,e||(e=n.displayName||n.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Y:return n=e.displayName||null,n!==null?n:Qt(e.type)||"Memo";case Ot:n=e._payload,e=e._init;try{return Qt(e(n))}catch{}}return null}var vt=Array.isArray,D=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,L=i.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,it={pending:!1,data:null,method:null,action:null},St=[],w=-1;function U(e){return{current:e}}function V(e){0>w||(e.current=St[w],St[w]=null,w--)}function P(e,n){w++,St[w]=e.current,e.current=n}var tt=U(null),ft=U(null),at=U(null),me=U(null);function Vt(e,n){switch(P(at,n),P(ft,e),P(tt,null),n.nodeType){case 9:case 11:e=(e=n.documentElement)&&(e=e.namespaceURI)?am(e):0;break;default:if(e=n.tagName,n=n.namespaceURI)n=am(n),e=rm(n,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}V(tt),P(tt,e)}function Fi(){V(tt),V(ft),V(at)}function Go(e){e.memoizedState!==null&&P(me,e);var n=tt.current,s=rm(n,e.type);n!==s&&(P(ft,e),P(tt,s))}function Da(e){ft.current===e&&(V(tt),V(ft)),me.current===e&&(V(me),ea._currentValue=it)}var $o=Object.prototype.hasOwnProperty,Wo=r.unstable_scheduleCallback,Yo=r.unstable_cancelCallback,B1=r.unstable_shouldYield,X1=r.unstable_requestPaint,oi=r.unstable_now,P1=r.unstable_getCurrentPriorityLevel,Pc=r.unstable_ImmediatePriority,Vc=r.unstable_UserBlockingPriority,qa=r.unstable_NormalPriority,V1=r.unstable_LowPriority,Hc=r.unstable_IdlePriority,H1=r.log,j1=r.unstable_setDisableYieldValue,us=null,De=null;function Ii(e){if(typeof H1=="function"&&j1(e),De&&typeof De.setStrictMode=="function")try{De.setStrictMode(us,e)}catch{}}var qe=Math.clz32?Math.clz32:$1,L1=Math.log,G1=Math.LN2;function $1(e){return e>>>=0,e===0?32:31-(L1(e)/G1|0)|0}var _a=256,Na=4194304;function Dn(e){var n=e&42;if(n!==0)return n;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ua(e,n,s){var u=e.pendingLanes;if(u===0)return 0;var f=0,g=e.suspendedLanes,y=e.pingedLanes;e=e.warmLanes;var v=u&134217727;return v!==0?(u=v&~g,u!==0?f=Dn(u):(y&=v,y!==0?f=Dn(y):s||(s=v&~e,s!==0&&(f=Dn(s))))):(v=u&~g,v!==0?f=Dn(v):y!==0?f=Dn(y):s||(s=u&~e,s!==0&&(f=Dn(s)))),f===0?0:n!==0&&n!==f&&(n&g)===0&&(g=f&-f,s=n&-n,g>=s||g===32&&(s&4194048)!==0)?n:f}function hs(e,n){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&n)===0}function W1(e,n){switch(e){case 1:case 2:case 4:case 8:case 64:return n+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return n+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function jc(){var e=_a;return _a<<=1,(_a&4194048)===0&&(_a=256),e}function Lc(){var e=Na;return Na<<=1,(Na&62914560)===0&&(Na=4194304),e}function Zo(e){for(var n=[],s=0;31>s;s++)n.push(e);return n}function fs(e,n){e.pendingLanes|=n,n!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Y1(e,n,s,u,f,g){var y=e.pendingLanes;e.pendingLanes=s,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=s,e.entangledLanes&=s,e.errorRecoveryDisabledLanes&=s,e.shellSuspendCounter=0;var v=e.entanglements,x=e.expirationTimes,R=e.hiddenUpdates;for(s=y&~s;0<s;){var _=31-qe(s),B=1<<_;v[_]=0,x[_]=-1;var E=R[_];if(E!==null)for(R[_]=null,_=0;_<E.length;_++){var M=E[_];M!==null&&(M.lane&=-536870913)}s&=~B}u!==0&&Gc(e,u,0),g!==0&&f===0&&e.tag!==0&&(e.suspendedLanes|=g&~(y&~n))}function Gc(e,n,s){e.pendingLanes|=n,e.suspendedLanes&=~n;var u=31-qe(n);e.entangledLanes|=n,e.entanglements[u]=e.entanglements[u]|1073741824|s&4194090}function $c(e,n){var s=e.entangledLanes|=n;for(e=e.entanglements;s;){var u=31-qe(s),f=1<<u;f&n|e[u]&n&&(e[u]|=n),s&=~f}}function Ko(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Jo(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Wc(){var e=L.p;return e!==0?e:(e=window.event,e===void 0?32:km(e.type))}function Z1(e,n){var s=L.p;try{return L.p=e,n()}finally{L.p=s}}var tn=Math.random().toString(36).slice(2),Oe="__reactFiber$"+tn,Qe="__reactProps$"+tn,ol="__reactContainer$"+tn,Fo="__reactEvents$"+tn,K1="__reactListeners$"+tn,J1="__reactHandles$"+tn,Yc="__reactResources$"+tn,cs="__reactMarker$"+tn;function Io(e){delete e[Oe],delete e[Qe],delete e[Fo],delete e[K1],delete e[J1]}function ul(e){var n=e[Oe];if(n)return n;for(var s=e.parentNode;s;){if(n=s[ol]||s[Oe]){if(s=n.alternate,n.child!==null||s!==null&&s.child!==null)for(e=fm(e);e!==null;){if(s=e[Oe])return s;e=fm(e)}return n}e=s,s=e.parentNode}return null}function hl(e){if(e=e[Oe]||e[ol]){var n=e.tag;if(n===5||n===6||n===13||n===26||n===27||n===3)return e}return null}function ds(e){var n=e.tag;if(n===5||n===26||n===27||n===6)return e.stateNode;throw Error(l(33))}function fl(e){var n=e[Yc];return n||(n=e[Yc]={hoistableStyles:new Map,hoistableScripts:new Map}),n}function le(e){e[cs]=!0}var Zc=new Set,Kc={};function qn(e,n){cl(e,n),cl(e+"Capture",n)}function cl(e,n){for(Kc[e]=n,e=0;e<n.length;e++)Zc.add(n[e])}var F1=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Jc={},Fc={};function I1(e){return $o.call(Fc,e)?!0:$o.call(Jc,e)?!1:F1.test(e)?Fc[e]=!0:(Jc[e]=!0,!1)}function Ba(e,n,s){if(I1(n))if(s===null)e.removeAttribute(n);else{switch(typeof s){case"undefined":case"function":case"symbol":e.removeAttribute(n);return;case"boolean":var u=n.toLowerCase().slice(0,5);if(u!=="data-"&&u!=="aria-"){e.removeAttribute(n);return}}e.setAttribute(n,""+s)}}function Xa(e,n,s){if(s===null)e.removeAttribute(n);else{switch(typeof s){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(n);return}e.setAttribute(n,""+s)}}function Qi(e,n,s,u){if(u===null)e.removeAttribute(s);else{switch(typeof u){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(s);return}e.setAttributeNS(n,s,""+u)}}var tu,Ic;function dl(e){if(tu===void 0)try{throw Error()}catch(s){var n=s.stack.trim().match(/\n( *(at )?)/);tu=n&&n[1]||"",Ic=-1<s.stack.indexOf(`
    at`)?" (<anonymous>)":-1<s.stack.indexOf("@")?"@unknown:0:0":""}return`
`+tu+e+Ic}var eu=!1;function iu(e,n){if(!e||eu)return"";eu=!0;var s=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var u={DetermineComponentFrameRoot:function(){try{if(n){var B=function(){throw Error()};if(Object.defineProperty(B.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(B,[])}catch(M){var E=M}Reflect.construct(e,[],B)}else{try{B.call()}catch(M){E=M}e.call(B.prototype)}}else{try{throw Error()}catch(M){E=M}(B=e())&&typeof B.catch=="function"&&B.catch(function(){})}}catch(M){if(M&&E&&typeof M.stack=="string")return[M.stack,E.stack]}return[null,null]}};u.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var f=Object.getOwnPropertyDescriptor(u.DetermineComponentFrameRoot,"name");f&&f.configurable&&Object.defineProperty(u.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var g=u.DetermineComponentFrameRoot(),y=g[0],v=g[1];if(y&&v){var x=y.split(`
`),R=v.split(`
`);for(f=u=0;u<x.length&&!x[u].includes("DetermineComponentFrameRoot");)u++;for(;f<R.length&&!R[f].includes("DetermineComponentFrameRoot");)f++;if(u===x.length||f===R.length)for(u=x.length-1,f=R.length-1;1<=u&&0<=f&&x[u]!==R[f];)f--;for(;1<=u&&0<=f;u--,f--)if(x[u]!==R[f]){if(u!==1||f!==1)do if(u--,f--,0>f||x[u]!==R[f]){var _=`
`+x[u].replace(" at new "," at ");return e.displayName&&_.includes("<anonymous>")&&(_=_.replace("<anonymous>",e.displayName)),_}while(1<=u&&0<=f);break}}}finally{eu=!1,Error.prepareStackTrace=s}return(s=e?e.displayName||e.name:"")?dl(s):""}function ty(e){switch(e.tag){case 26:case 27:case 5:return dl(e.type);case 16:return dl("Lazy");case 13:return dl("Suspense");case 19:return dl("SuspenseList");case 0:case 15:return iu(e.type,!1);case 11:return iu(e.type.render,!1);case 1:return iu(e.type,!0);case 31:return dl("Activity");default:return""}}function td(e){try{var n="";do n+=ty(e),e=e.return;while(e);return n}catch(s){return`
Error generating stack: `+s.message+`
`+s.stack}}function Ge(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function ed(e){var n=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(n==="checkbox"||n==="radio")}function ey(e){var n=ed(e)?"checked":"value",s=Object.getOwnPropertyDescriptor(e.constructor.prototype,n),u=""+e[n];if(!e.hasOwnProperty(n)&&typeof s<"u"&&typeof s.get=="function"&&typeof s.set=="function"){var f=s.get,g=s.set;return Object.defineProperty(e,n,{configurable:!0,get:function(){return f.call(this)},set:function(y){u=""+y,g.call(this,y)}}),Object.defineProperty(e,n,{enumerable:s.enumerable}),{getValue:function(){return u},setValue:function(y){u=""+y},stopTracking:function(){e._valueTracker=null,delete e[n]}}}}function Pa(e){e._valueTracker||(e._valueTracker=ey(e))}function id(e){if(!e)return!1;var n=e._valueTracker;if(!n)return!0;var s=n.getValue(),u="";return e&&(u=ed(e)?e.checked?"true":"false":e.value),e=u,e!==s?(n.setValue(e),!0):!1}function Va(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var iy=/[\n"\\]/g;function $e(e){return e.replace(iy,function(n){return"\\"+n.charCodeAt(0).toString(16)+" "})}function nu(e,n,s,u,f,g,y,v){e.name="",y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"?e.type=y:e.removeAttribute("type"),n!=null?y==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+Ge(n)):e.value!==""+Ge(n)&&(e.value=""+Ge(n)):y!=="submit"&&y!=="reset"||e.removeAttribute("value"),n!=null?lu(e,y,Ge(n)):s!=null?lu(e,y,Ge(s)):u!=null&&e.removeAttribute("value"),f==null&&g!=null&&(e.defaultChecked=!!g),f!=null&&(e.checked=f&&typeof f!="function"&&typeof f!="symbol"),v!=null&&typeof v!="function"&&typeof v!="symbol"&&typeof v!="boolean"?e.name=""+Ge(v):e.removeAttribute("name")}function nd(e,n,s,u,f,g,y,v){if(g!=null&&typeof g!="function"&&typeof g!="symbol"&&typeof g!="boolean"&&(e.type=g),n!=null||s!=null){if(!(g!=="submit"&&g!=="reset"||n!=null))return;s=s!=null?""+Ge(s):"",n=n!=null?""+Ge(n):s,v||n===e.value||(e.value=n),e.defaultValue=n}u=u??f,u=typeof u!="function"&&typeof u!="symbol"&&!!u,e.checked=v?e.checked:!!u,e.defaultChecked=!!u,y!=null&&typeof y!="function"&&typeof y!="symbol"&&typeof y!="boolean"&&(e.name=y)}function lu(e,n,s){n==="number"&&Va(e.ownerDocument)===e||e.defaultValue===""+s||(e.defaultValue=""+s)}function gl(e,n,s,u){if(e=e.options,n){n={};for(var f=0;f<s.length;f++)n["$"+s[f]]=!0;for(s=0;s<e.length;s++)f=n.hasOwnProperty("$"+e[s].value),e[s].selected!==f&&(e[s].selected=f),f&&u&&(e[s].defaultSelected=!0)}else{for(s=""+Ge(s),n=null,f=0;f<e.length;f++){if(e[f].value===s){e[f].selected=!0,u&&(e[f].defaultSelected=!0);return}n!==null||e[f].disabled||(n=e[f])}n!==null&&(n.selected=!0)}}function ld(e,n,s){if(n!=null&&(n=""+Ge(n),n!==e.value&&(e.value=n),s==null)){e.defaultValue!==n&&(e.defaultValue=n);return}e.defaultValue=s!=null?""+Ge(s):""}function sd(e,n,s,u){if(n==null){if(u!=null){if(s!=null)throw Error(l(92));if(vt(u)){if(1<u.length)throw Error(l(93));u=u[0]}s=u}s==null&&(s=""),n=s}s=Ge(n),e.defaultValue=s,u=e.textContent,u===s&&u!==""&&u!==null&&(e.value=u)}function pl(e,n){if(n){var s=e.firstChild;if(s&&s===e.lastChild&&s.nodeType===3){s.nodeValue=n;return}}e.textContent=n}var ny=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function ad(e,n,s){var u=n.indexOf("--")===0;s==null||typeof s=="boolean"||s===""?u?e.setProperty(n,""):n==="float"?e.cssFloat="":e[n]="":u?e.setProperty(n,s):typeof s!="number"||s===0||ny.has(n)?n==="float"?e.cssFloat=s:e[n]=(""+s).trim():e[n]=s+"px"}function rd(e,n,s){if(n!=null&&typeof n!="object")throw Error(l(62));if(e=e.style,s!=null){for(var u in s)!s.hasOwnProperty(u)||n!=null&&n.hasOwnProperty(u)||(u.indexOf("--")===0?e.setProperty(u,""):u==="float"?e.cssFloat="":e[u]="");for(var f in n)u=n[f],n.hasOwnProperty(f)&&s[f]!==u&&ad(e,f,u)}else for(var g in n)n.hasOwnProperty(g)&&ad(e,g,n[g])}function su(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var ly=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),sy=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Ha(e){return sy.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var au=null;function ru(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ml=null,Ol=null;function od(e){var n=hl(e);if(n&&(e=n.stateNode)){var s=e[Qe]||null;t:switch(e=n.stateNode,n.type){case"input":if(nu(e,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name),n=s.name,s.type==="radio"&&n!=null){for(s=e;s.parentNode;)s=s.parentNode;for(s=s.querySelectorAll('input[name="'+$e(""+n)+'"][type="radio"]'),n=0;n<s.length;n++){var u=s[n];if(u!==e&&u.form===e.form){var f=u[Qe]||null;if(!f)throw Error(l(90));nu(u,f.value,f.defaultValue,f.defaultValue,f.checked,f.defaultChecked,f.type,f.name)}}for(n=0;n<s.length;n++)u=s[n],u.form===e.form&&id(u)}break t;case"textarea":ld(e,s.value,s.defaultValue);break t;case"select":n=s.value,n!=null&&gl(e,!!s.multiple,n,!1)}}}var ou=!1;function ud(e,n,s){if(ou)return e(n,s);ou=!0;try{var u=e(n);return u}finally{if(ou=!1,(ml!==null||Ol!==null)&&(Ar(),ml&&(n=ml,e=Ol,Ol=ml=null,od(n),e)))for(n=0;n<e.length;n++)od(e[n])}}function gs(e,n){var s=e.stateNode;if(s===null)return null;var u=s[Qe]||null;if(u===null)return null;s=u[n];t:switch(n){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(u=!u.disabled)||(e=e.type,u=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!u;break t;default:e=!1}if(e)return null;if(s&&typeof s!="function")throw Error(l(231,n,typeof s));return s}var ki=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),uu=!1;if(ki)try{var ps={};Object.defineProperty(ps,"passive",{get:function(){uu=!0}}),window.addEventListener("test",ps,ps),window.removeEventListener("test",ps,ps)}catch{uu=!1}var en=null,hu=null,ja=null;function hd(){if(ja)return ja;var e,n=hu,s=n.length,u,f="value"in en?en.value:en.textContent,g=f.length;for(e=0;e<s&&n[e]===f[e];e++);var y=s-e;for(u=1;u<=y&&n[s-u]===f[g-u];u++);return ja=f.slice(e,1<u?1-u:void 0)}function La(e){var n=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&n===13&&(e=13)):e=n,e===10&&(e=13),32<=e||e===13?e:0}function Ga(){return!0}function fd(){return!1}function ke(e){function n(s,u,f,g,y){this._reactName=s,this._targetInst=f,this.type=u,this.nativeEvent=g,this.target=y,this.currentTarget=null;for(var v in e)e.hasOwnProperty(v)&&(s=e[v],this[v]=s?s(g):g[v]);return this.isDefaultPrevented=(g.defaultPrevented!=null?g.defaultPrevented:g.returnValue===!1)?Ga:fd,this.isPropagationStopped=fd,this}return m(n.prototype,{preventDefault:function(){this.defaultPrevented=!0;var s=this.nativeEvent;s&&(s.preventDefault?s.preventDefault():typeof s.returnValue!="unknown"&&(s.returnValue=!1),this.isDefaultPrevented=Ga)},stopPropagation:function(){var s=this.nativeEvent;s&&(s.stopPropagation?s.stopPropagation():typeof s.cancelBubble!="unknown"&&(s.cancelBubble=!0),this.isPropagationStopped=Ga)},persist:function(){},isPersistent:Ga}),n}var _n={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},$a=ke(_n),ms=m({},_n,{view:0,detail:0}),ay=ke(ms),fu,cu,Os,Wa=m({},ms,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Os&&(Os&&e.type==="mousemove"?(fu=e.screenX-Os.screenX,cu=e.screenY-Os.screenY):cu=fu=0,Os=e),fu)},movementY:function(e){return"movementY"in e?e.movementY:cu}}),cd=ke(Wa),ry=m({},Wa,{dataTransfer:0}),oy=ke(ry),uy=m({},ms,{relatedTarget:0}),du=ke(uy),hy=m({},_n,{animationName:0,elapsedTime:0,pseudoElement:0}),fy=ke(hy),cy=m({},_n,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),dy=ke(cy),gy=m({},_n,{data:0}),dd=ke(gy),py={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},my={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Oy={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function yy(e){var n=this.nativeEvent;return n.getModifierState?n.getModifierState(e):(e=Oy[e])?!!n[e]:!1}function gu(){return yy}var Sy=m({},ms,{key:function(e){if(e.key){var n=py[e.key]||e.key;if(n!=="Unidentified")return n}return e.type==="keypress"?(e=La(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?my[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gu,charCode:function(e){return e.type==="keypress"?La(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?La(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),vy=ke(Sy),by=m({},Wa,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),gd=ke(by),xy=m({},ms,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gu}),Ty=ke(xy),wy=m({},_n,{propertyName:0,elapsedTime:0,pseudoElement:0}),Qy=ke(wy),ky=m({},Wa,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ay=ke(ky),Ry=m({},_n,{newState:0,oldState:0}),Ey=ke(Ry),zy=[9,13,27,32],pu=ki&&"CompositionEvent"in window,ys=null;ki&&"documentMode"in document&&(ys=document.documentMode);var My=ki&&"TextEvent"in window&&!ys,pd=ki&&(!pu||ys&&8<ys&&11>=ys),md=" ",Od=!1;function yd(e,n){switch(e){case"keyup":return zy.indexOf(n.keyCode)!==-1;case"keydown":return n.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Sd(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var yl=!1;function Cy(e,n){switch(e){case"compositionend":return Sd(n);case"keypress":return n.which!==32?null:(Od=!0,md);case"textInput":return e=n.data,e===md&&Od?null:e;default:return null}}function Dy(e,n){if(yl)return e==="compositionend"||!pu&&yd(e,n)?(e=hd(),ja=hu=en=null,yl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(n.ctrlKey||n.altKey||n.metaKey)||n.ctrlKey&&n.altKey){if(n.char&&1<n.char.length)return n.char;if(n.which)return String.fromCharCode(n.which)}return null;case"compositionend":return pd&&n.locale!=="ko"?null:n.data;default:return null}}var qy={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function vd(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n==="input"?!!qy[e.type]:n==="textarea"}function bd(e,n,s,u){ml?Ol?Ol.push(u):Ol=[u]:ml=u,n=Dr(n,"onChange"),0<n.length&&(s=new $a("onChange","change",null,s,u),e.push({event:s,listeners:n}))}var Ss=null,vs=null;function _y(e){em(e,0)}function Ya(e){var n=ds(e);if(id(n))return e}function xd(e,n){if(e==="change")return n}var Td=!1;if(ki){var mu;if(ki){var Ou="oninput"in document;if(!Ou){var wd=document.createElement("div");wd.setAttribute("oninput","return;"),Ou=typeof wd.oninput=="function"}mu=Ou}else mu=!1;Td=mu&&(!document.documentMode||9<document.documentMode)}function Qd(){Ss&&(Ss.detachEvent("onpropertychange",kd),vs=Ss=null)}function kd(e){if(e.propertyName==="value"&&Ya(vs)){var n=[];bd(n,vs,e,ru(e)),ud(_y,n)}}function Ny(e,n,s){e==="focusin"?(Qd(),Ss=n,vs=s,Ss.attachEvent("onpropertychange",kd)):e==="focusout"&&Qd()}function Uy(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ya(vs)}function By(e,n){if(e==="click")return Ya(n)}function Xy(e,n){if(e==="input"||e==="change")return Ya(n)}function Py(e,n){return e===n&&(e!==0||1/e===1/n)||e!==e&&n!==n}var _e=typeof Object.is=="function"?Object.is:Py;function bs(e,n){if(_e(e,n))return!0;if(typeof e!="object"||e===null||typeof n!="object"||n===null)return!1;var s=Object.keys(e),u=Object.keys(n);if(s.length!==u.length)return!1;for(u=0;u<s.length;u++){var f=s[u];if(!$o.call(n,f)||!_e(e[f],n[f]))return!1}return!0}function Ad(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Rd(e,n){var s=Ad(e);e=0;for(var u;s;){if(s.nodeType===3){if(u=e+s.textContent.length,e<=n&&u>=n)return{node:s,offset:n-e};e=u}t:{for(;s;){if(s.nextSibling){s=s.nextSibling;break t}s=s.parentNode}s=void 0}s=Ad(s)}}function Ed(e,n){return e&&n?e===n?!0:e&&e.nodeType===3?!1:n&&n.nodeType===3?Ed(e,n.parentNode):"contains"in e?e.contains(n):e.compareDocumentPosition?!!(e.compareDocumentPosition(n)&16):!1:!1}function zd(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var n=Va(e.document);n instanceof e.HTMLIFrameElement;){try{var s=typeof n.contentWindow.location.href=="string"}catch{s=!1}if(s)e=n.contentWindow;else break;n=Va(e.document)}return n}function yu(e){var n=e&&e.nodeName&&e.nodeName.toLowerCase();return n&&(n==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||n==="textarea"||e.contentEditable==="true")}var Vy=ki&&"documentMode"in document&&11>=document.documentMode,Sl=null,Su=null,xs=null,vu=!1;function Md(e,n,s){var u=s.window===s?s.document:s.nodeType===9?s:s.ownerDocument;vu||Sl==null||Sl!==Va(u)||(u=Sl,"selectionStart"in u&&yu(u)?u={start:u.selectionStart,end:u.selectionEnd}:(u=(u.ownerDocument&&u.ownerDocument.defaultView||window).getSelection(),u={anchorNode:u.anchorNode,anchorOffset:u.anchorOffset,focusNode:u.focusNode,focusOffset:u.focusOffset}),xs&&bs(xs,u)||(xs=u,u=Dr(Su,"onSelect"),0<u.length&&(n=new $a("onSelect","select",null,n,s),e.push({event:n,listeners:u}),n.target=Sl)))}function Nn(e,n){var s={};return s[e.toLowerCase()]=n.toLowerCase(),s["Webkit"+e]="webkit"+n,s["Moz"+e]="moz"+n,s}var vl={animationend:Nn("Animation","AnimationEnd"),animationiteration:Nn("Animation","AnimationIteration"),animationstart:Nn("Animation","AnimationStart"),transitionrun:Nn("Transition","TransitionRun"),transitionstart:Nn("Transition","TransitionStart"),transitioncancel:Nn("Transition","TransitionCancel"),transitionend:Nn("Transition","TransitionEnd")},bu={},Cd={};ki&&(Cd=document.createElement("div").style,"AnimationEvent"in window||(delete vl.animationend.animation,delete vl.animationiteration.animation,delete vl.animationstart.animation),"TransitionEvent"in window||delete vl.transitionend.transition);function Un(e){if(bu[e])return bu[e];if(!vl[e])return e;var n=vl[e],s;for(s in n)if(n.hasOwnProperty(s)&&s in Cd)return bu[e]=n[s];return e}var Dd=Un("animationend"),qd=Un("animationiteration"),_d=Un("animationstart"),Hy=Un("transitionrun"),jy=Un("transitionstart"),Ly=Un("transitioncancel"),Nd=Un("transitionend"),Ud=new Map,xu="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");xu.push("scrollEnd");function ni(e,n){Ud.set(e,n),qn(n,[e])}var Bd=new WeakMap;function We(e,n){if(typeof e=="object"&&e!==null){var s=Bd.get(e);return s!==void 0?s:(n={value:e,source:n,stack:td(n)},Bd.set(e,n),n)}return{value:e,source:n,stack:td(n)}}var Ye=[],bl=0,Tu=0;function Za(){for(var e=bl,n=Tu=bl=0;n<e;){var s=Ye[n];Ye[n++]=null;var u=Ye[n];Ye[n++]=null;var f=Ye[n];Ye[n++]=null;var g=Ye[n];if(Ye[n++]=null,u!==null&&f!==null){var y=u.pending;y===null?f.next=f:(f.next=y.next,y.next=f),u.pending=f}g!==0&&Xd(s,f,g)}}function Ka(e,n,s,u){Ye[bl++]=e,Ye[bl++]=n,Ye[bl++]=s,Ye[bl++]=u,Tu|=u,e.lanes|=u,e=e.alternate,e!==null&&(e.lanes|=u)}function wu(e,n,s,u){return Ka(e,n,s,u),Ja(e)}function xl(e,n){return Ka(e,null,null,n),Ja(e)}function Xd(e,n,s){e.lanes|=s;var u=e.alternate;u!==null&&(u.lanes|=s);for(var f=!1,g=e.return;g!==null;)g.childLanes|=s,u=g.alternate,u!==null&&(u.childLanes|=s),g.tag===22&&(e=g.stateNode,e===null||e._visibility&1||(f=!0)),e=g,g=g.return;return e.tag===3?(g=e.stateNode,f&&n!==null&&(f=31-qe(s),e=g.hiddenUpdates,u=e[f],u===null?e[f]=[n]:u.push(n),n.lane=s|536870912),g):null}function Ja(e){if(50<Ws)throw Ws=0,zh=null,Error(l(185));for(var n=e.return;n!==null;)e=n,n=e.return;return e.tag===3?e.stateNode:null}var Tl={};function Gy(e,n,s,u){this.tag=e,this.key=s,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=n,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=u,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ne(e,n,s,u){return new Gy(e,n,s,u)}function Qu(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ai(e,n){var s=e.alternate;return s===null?(s=Ne(e.tag,n,e.key,e.mode),s.elementType=e.elementType,s.type=e.type,s.stateNode=e.stateNode,s.alternate=e,e.alternate=s):(s.pendingProps=n,s.type=e.type,s.flags=0,s.subtreeFlags=0,s.deletions=null),s.flags=e.flags&65011712,s.childLanes=e.childLanes,s.lanes=e.lanes,s.child=e.child,s.memoizedProps=e.memoizedProps,s.memoizedState=e.memoizedState,s.updateQueue=e.updateQueue,n=e.dependencies,s.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext},s.sibling=e.sibling,s.index=e.index,s.ref=e.ref,s.refCleanup=e.refCleanup,s}function Pd(e,n){e.flags&=65011714;var s=e.alternate;return s===null?(e.childLanes=0,e.lanes=n,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=s.childLanes,e.lanes=s.lanes,e.child=s.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=s.memoizedProps,e.memoizedState=s.memoizedState,e.updateQueue=s.updateQueue,e.type=s.type,n=s.dependencies,e.dependencies=n===null?null:{lanes:n.lanes,firstContext:n.firstContext}),e}function Fa(e,n,s,u,f,g){var y=0;if(u=e,typeof e=="function")Qu(e)&&(y=1);else if(typeof e=="string")y=WS(e,s,tt.current)?26:e==="html"||e==="head"||e==="body"?27:5;else t:switch(e){case gt:return e=Ne(31,s,n,f),e.elementType=gt,e.lanes=g,e;case T:return Bn(s.children,f,g,n);case z:y=8,f|=24;break;case C:return e=Ne(12,s,n,f|2),e.elementType=C,e.lanes=g,e;case X:return e=Ne(13,s,n,f),e.elementType=X,e.lanes=g,e;case J:return e=Ne(19,s,n,f),e.elementType=J,e.lanes=g,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case q:case j:y=10;break t;case $:y=9;break t;case W:y=11;break t;case Y:y=14;break t;case Ot:y=16,u=null;break t}y=29,s=Error(l(130,e===null?"null":typeof e,"")),u=null}return n=Ne(y,s,n,f),n.elementType=e,n.type=u,n.lanes=g,n}function Bn(e,n,s,u){return e=Ne(7,e,u,n),e.lanes=s,e}function ku(e,n,s){return e=Ne(6,e,null,n),e.lanes=s,e}function Au(e,n,s){return n=Ne(4,e.children!==null?e.children:[],e.key,n),n.lanes=s,n.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},n}var wl=[],Ql=0,Ia=null,tr=0,Ze=[],Ke=0,Xn=null,Ri=1,Ei="";function Pn(e,n){wl[Ql++]=tr,wl[Ql++]=Ia,Ia=e,tr=n}function Vd(e,n,s){Ze[Ke++]=Ri,Ze[Ke++]=Ei,Ze[Ke++]=Xn,Xn=e;var u=Ri;e=Ei;var f=32-qe(u)-1;u&=~(1<<f),s+=1;var g=32-qe(n)+f;if(30<g){var y=f-f%5;g=(u&(1<<y)-1).toString(32),u>>=y,f-=y,Ri=1<<32-qe(n)+f|s<<f|u,Ei=g+e}else Ri=1<<g|s<<f|u,Ei=e}function Ru(e){e.return!==null&&(Pn(e,1),Vd(e,1,0))}function Eu(e){for(;e===Ia;)Ia=wl[--Ql],wl[Ql]=null,tr=wl[--Ql],wl[Ql]=null;for(;e===Xn;)Xn=Ze[--Ke],Ze[Ke]=null,Ei=Ze[--Ke],Ze[Ke]=null,Ri=Ze[--Ke],Ze[Ke]=null}var xe=null,Lt=null,kt=!1,Vn=null,ui=!1,zu=Error(l(519));function Hn(e){var n=Error(l(418,""));throw Qs(We(n,e)),zu}function Hd(e){var n=e.stateNode,s=e.type,u=e.memoizedProps;switch(n[Oe]=e,n[Qe]=u,s){case"dialog":mt("cancel",n),mt("close",n);break;case"iframe":case"object":case"embed":mt("load",n);break;case"video":case"audio":for(s=0;s<Zs.length;s++)mt(Zs[s],n);break;case"source":mt("error",n);break;case"img":case"image":case"link":mt("error",n),mt("load",n);break;case"details":mt("toggle",n);break;case"input":mt("invalid",n),nd(n,u.value,u.defaultValue,u.checked,u.defaultChecked,u.type,u.name,!0),Pa(n);break;case"select":mt("invalid",n);break;case"textarea":mt("invalid",n),sd(n,u.value,u.defaultValue,u.children),Pa(n)}s=u.children,typeof s!="string"&&typeof s!="number"&&typeof s!="bigint"||n.textContent===""+s||u.suppressHydrationWarning===!0||sm(n.textContent,s)?(u.popover!=null&&(mt("beforetoggle",n),mt("toggle",n)),u.onScroll!=null&&mt("scroll",n),u.onScrollEnd!=null&&mt("scrollend",n),u.onClick!=null&&(n.onclick=qr),n=!0):n=!1,n||Hn(e)}function jd(e){for(xe=e.return;xe;)switch(xe.tag){case 5:case 13:ui=!1;return;case 27:case 3:ui=!0;return;default:xe=xe.return}}function Ts(e){if(e!==xe)return!1;if(!kt)return jd(e),kt=!0,!1;var n=e.tag,s;if((s=n!==3&&n!==27)&&((s=n===5)&&(s=e.type,s=!(s!=="form"&&s!=="button")||$h(e.type,e.memoizedProps)),s=!s),s&&Lt&&Hn(e),jd(e),n===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(l(317));t:{for(e=e.nextSibling,n=0;e;){if(e.nodeType===8)if(s=e.data,s==="/$"){if(n===0){Lt=si(e.nextSibling);break t}n--}else s!=="$"&&s!=="$!"&&s!=="$?"||n++;e=e.nextSibling}Lt=null}}else n===27?(n=Lt,yn(e.type)?(e=Kh,Kh=null,Lt=e):Lt=n):Lt=xe?si(e.stateNode.nextSibling):null;return!0}function ws(){Lt=xe=null,kt=!1}function Ld(){var e=Vn;return e!==null&&(Ee===null?Ee=e:Ee.push.apply(Ee,e),Vn=null),e}function Qs(e){Vn===null?Vn=[e]:Vn.push(e)}var Mu=U(null),jn=null,zi=null;function nn(e,n,s){P(Mu,n._currentValue),n._currentValue=s}function Mi(e){e._currentValue=Mu.current,V(Mu)}function Cu(e,n,s){for(;e!==null;){var u=e.alternate;if((e.childLanes&n)!==n?(e.childLanes|=n,u!==null&&(u.childLanes|=n)):u!==null&&(u.childLanes&n)!==n&&(u.childLanes|=n),e===s)break;e=e.return}}function Du(e,n,s,u){var f=e.child;for(f!==null&&(f.return=e);f!==null;){var g=f.dependencies;if(g!==null){var y=f.child;g=g.firstContext;t:for(;g!==null;){var v=g;g=f;for(var x=0;x<n.length;x++)if(v.context===n[x]){g.lanes|=s,v=g.alternate,v!==null&&(v.lanes|=s),Cu(g.return,s,e),u||(y=null);break t}g=v.next}}else if(f.tag===18){if(y=f.return,y===null)throw Error(l(341));y.lanes|=s,g=y.alternate,g!==null&&(g.lanes|=s),Cu(y,s,e),y=null}else y=f.child;if(y!==null)y.return=f;else for(y=f;y!==null;){if(y===e){y=null;break}if(f=y.sibling,f!==null){f.return=y.return,y=f;break}y=y.return}f=y}}function ks(e,n,s,u){e=null;for(var f=n,g=!1;f!==null;){if(!g){if((f.flags&524288)!==0)g=!0;else if((f.flags&262144)!==0)break}if(f.tag===10){var y=f.alternate;if(y===null)throw Error(l(387));if(y=y.memoizedProps,y!==null){var v=f.type;_e(f.pendingProps.value,y.value)||(e!==null?e.push(v):e=[v])}}else if(f===me.current){if(y=f.alternate,y===null)throw Error(l(387));y.memoizedState.memoizedState!==f.memoizedState.memoizedState&&(e!==null?e.push(ea):e=[ea])}f=f.return}e!==null&&Du(n,e,s,u),n.flags|=262144}function er(e){for(e=e.firstContext;e!==null;){if(!_e(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ln(e){jn=e,zi=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function ye(e){return Gd(jn,e)}function ir(e,n){return jn===null&&Ln(e),Gd(e,n)}function Gd(e,n){var s=n._currentValue;if(n={context:n,memoizedValue:s,next:null},zi===null){if(e===null)throw Error(l(308));zi=n,e.dependencies={lanes:0,firstContext:n},e.flags|=524288}else zi=zi.next=n;return s}var $y=typeof AbortController<"u"?AbortController:function(){var e=[],n=this.signal={aborted:!1,addEventListener:function(s,u){e.push(u)}};this.abort=function(){n.aborted=!0,e.forEach(function(s){return s()})}},Wy=r.unstable_scheduleCallback,Yy=r.unstable_NormalPriority,te={$$typeof:j,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function qu(){return{controller:new $y,data:new Map,refCount:0}}function As(e){e.refCount--,e.refCount===0&&Wy(Yy,function(){e.controller.abort()})}var Rs=null,_u=0,kl=0,Al=null;function Zy(e,n){if(Rs===null){var s=Rs=[];_u=0,kl=Uh(),Al={status:"pending",value:void 0,then:function(u){s.push(u)}}}return _u++,n.then($d,$d),n}function $d(){if(--_u===0&&Rs!==null){Al!==null&&(Al.status="fulfilled");var e=Rs;Rs=null,kl=0,Al=null;for(var n=0;n<e.length;n++)(0,e[n])()}}function Ky(e,n){var s=[],u={status:"pending",value:null,reason:null,then:function(f){s.push(f)}};return e.then(function(){u.status="fulfilled",u.value=n;for(var f=0;f<s.length;f++)(0,s[f])(n)},function(f){for(u.status="rejected",u.reason=f,f=0;f<s.length;f++)(0,s[f])(void 0)}),u}var Wd=D.S;D.S=function(e,n){typeof n=="object"&&n!==null&&typeof n.then=="function"&&Zy(e,n),Wd!==null&&Wd(e,n)};var Gn=U(null);function Nu(){var e=Gn.current;return e!==null?e:Bt.pooledCache}function nr(e,n){n===null?P(Gn,Gn.current):P(Gn,n.pool)}function Yd(){var e=Nu();return e===null?null:{parent:te._currentValue,pool:e}}var Es=Error(l(460)),Zd=Error(l(474)),lr=Error(l(542)),Uu={then:function(){}};function Kd(e){return e=e.status,e==="fulfilled"||e==="rejected"}function sr(){}function Jd(e,n,s){switch(s=e[s],s===void 0?e.push(n):s!==n&&(n.then(sr,sr),n=s),n.status){case"fulfilled":return n.value;case"rejected":throw e=n.reason,Id(e),e;default:if(typeof n.status=="string")n.then(sr,sr);else{if(e=Bt,e!==null&&100<e.shellSuspendCounter)throw Error(l(482));e=n,e.status="pending",e.then(function(u){if(n.status==="pending"){var f=n;f.status="fulfilled",f.value=u}},function(u){if(n.status==="pending"){var f=n;f.status="rejected",f.reason=u}})}switch(n.status){case"fulfilled":return n.value;case"rejected":throw e=n.reason,Id(e),e}throw zs=n,Es}}var zs=null;function Fd(){if(zs===null)throw Error(l(459));var e=zs;return zs=null,e}function Id(e){if(e===Es||e===lr)throw Error(l(483))}var ln=!1;function Bu(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Xu(e,n){e=e.updateQueue,n.updateQueue===e&&(n.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function sn(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function an(e,n,s){var u=e.updateQueue;if(u===null)return null;if(u=u.shared,(zt&2)!==0){var f=u.pending;return f===null?n.next=n:(n.next=f.next,f.next=n),u.pending=n,n=Ja(e),Xd(e,null,s),n}return Ka(e,u,n,s),Ja(e)}function Ms(e,n,s){if(n=n.updateQueue,n!==null&&(n=n.shared,(s&4194048)!==0)){var u=n.lanes;u&=e.pendingLanes,s|=u,n.lanes=s,$c(e,s)}}function Pu(e,n){var s=e.updateQueue,u=e.alternate;if(u!==null&&(u=u.updateQueue,s===u)){var f=null,g=null;if(s=s.firstBaseUpdate,s!==null){do{var y={lane:s.lane,tag:s.tag,payload:s.payload,callback:null,next:null};g===null?f=g=y:g=g.next=y,s=s.next}while(s!==null);g===null?f=g=n:g=g.next=n}else f=g=n;s={baseState:u.baseState,firstBaseUpdate:f,lastBaseUpdate:g,shared:u.shared,callbacks:u.callbacks},e.updateQueue=s;return}e=s.lastBaseUpdate,e===null?s.firstBaseUpdate=n:e.next=n,s.lastBaseUpdate=n}var Vu=!1;function Cs(){if(Vu){var e=Al;if(e!==null)throw e}}function Ds(e,n,s,u){Vu=!1;var f=e.updateQueue;ln=!1;var g=f.firstBaseUpdate,y=f.lastBaseUpdate,v=f.shared.pending;if(v!==null){f.shared.pending=null;var x=v,R=x.next;x.next=null,y===null?g=R:y.next=R,y=x;var _=e.alternate;_!==null&&(_=_.updateQueue,v=_.lastBaseUpdate,v!==y&&(v===null?_.firstBaseUpdate=R:v.next=R,_.lastBaseUpdate=x))}if(g!==null){var B=f.baseState;y=0,_=R=x=null,v=g;do{var E=v.lane&-536870913,M=E!==v.lane;if(M?(bt&E)===E:(u&E)===E){E!==0&&E===kl&&(Vu=!0),_!==null&&(_=_.next={lane:0,tag:v.tag,payload:v.payload,callback:null,next:null});t:{var rt=e,nt=v;E=n;var qt=s;switch(nt.tag){case 1:if(rt=nt.payload,typeof rt=="function"){B=rt.call(qt,B,E);break t}B=rt;break t;case 3:rt.flags=rt.flags&-65537|128;case 0:if(rt=nt.payload,E=typeof rt=="function"?rt.call(qt,B,E):rt,E==null)break t;B=m({},B,E);break t;case 2:ln=!0}}E=v.callback,E!==null&&(e.flags|=64,M&&(e.flags|=8192),M=f.callbacks,M===null?f.callbacks=[E]:M.push(E))}else M={lane:E,tag:v.tag,payload:v.payload,callback:v.callback,next:null},_===null?(R=_=M,x=B):_=_.next=M,y|=E;if(v=v.next,v===null){if(v=f.shared.pending,v===null)break;M=v,v=M.next,M.next=null,f.lastBaseUpdate=M,f.shared.pending=null}}while(!0);_===null&&(x=B),f.baseState=x,f.firstBaseUpdate=R,f.lastBaseUpdate=_,g===null&&(f.shared.lanes=0),gn|=y,e.lanes=y,e.memoizedState=B}}function tg(e,n){if(typeof e!="function")throw Error(l(191,e));e.call(n)}function eg(e,n){var s=e.callbacks;if(s!==null)for(e.callbacks=null,e=0;e<s.length;e++)tg(s[e],n)}var Rl=U(null),ar=U(0);function ig(e,n){e=Bi,P(ar,e),P(Rl,n),Bi=e|n.baseLanes}function Hu(){P(ar,Bi),P(Rl,Rl.current)}function ju(){Bi=ar.current,V(Rl),V(ar)}var rn=0,ct=null,Ct=null,Kt=null,rr=!1,El=!1,$n=!1,or=0,qs=0,zl=null,Jy=0;function Wt(){throw Error(l(321))}function Lu(e,n){if(n===null)return!1;for(var s=0;s<n.length&&s<e.length;s++)if(!_e(e[s],n[s]))return!1;return!0}function Gu(e,n,s,u,f,g){return rn=g,ct=n,n.memoizedState=null,n.updateQueue=null,n.lanes=0,D.H=e===null||e.memoizedState===null?Xg:Pg,$n=!1,g=s(u,f),$n=!1,El&&(g=lg(n,s,u,f)),ng(e),g}function ng(e){D.H=gr;var n=Ct!==null&&Ct.next!==null;if(rn=0,Kt=Ct=ct=null,rr=!1,qs=0,zl=null,n)throw Error(l(300));e===null||se||(e=e.dependencies,e!==null&&er(e)&&(se=!0))}function lg(e,n,s,u){ct=e;var f=0;do{if(El&&(zl=null),qs=0,El=!1,25<=f)throw Error(l(301));if(f+=1,Kt=Ct=null,e.updateQueue!=null){var g=e.updateQueue;g.lastEffect=null,g.events=null,g.stores=null,g.memoCache!=null&&(g.memoCache.index=0)}D.H=lS,g=n(s,u)}while(El);return g}function Fy(){var e=D.H,n=e.useState()[0];return n=typeof n.then=="function"?_s(n):n,e=e.useState()[0],(Ct!==null?Ct.memoizedState:null)!==e&&(ct.flags|=1024),n}function $u(){var e=or!==0;return or=0,e}function Wu(e,n,s){n.updateQueue=e.updateQueue,n.flags&=-2053,e.lanes&=~s}function Yu(e){if(rr){for(e=e.memoizedState;e!==null;){var n=e.queue;n!==null&&(n.pending=null),e=e.next}rr=!1}rn=0,Kt=Ct=ct=null,El=!1,qs=or=0,zl=null}function Ae(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Kt===null?ct.memoizedState=Kt=e:Kt=Kt.next=e,Kt}function Jt(){if(Ct===null){var e=ct.alternate;e=e!==null?e.memoizedState:null}else e=Ct.next;var n=Kt===null?ct.memoizedState:Kt.next;if(n!==null)Kt=n,Ct=e;else{if(e===null)throw ct.alternate===null?Error(l(467)):Error(l(310));Ct=e,e={memoizedState:Ct.memoizedState,baseState:Ct.baseState,baseQueue:Ct.baseQueue,queue:Ct.queue,next:null},Kt===null?ct.memoizedState=Kt=e:Kt=Kt.next=e}return Kt}function Zu(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function _s(e){var n=qs;return qs+=1,zl===null&&(zl=[]),e=Jd(zl,e,n),n=ct,(Kt===null?n.memoizedState:Kt.next)===null&&(n=n.alternate,D.H=n===null||n.memoizedState===null?Xg:Pg),e}function ur(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return _s(e);if(e.$$typeof===j)return ye(e)}throw Error(l(438,String(e)))}function Ku(e){var n=null,s=ct.updateQueue;if(s!==null&&(n=s.memoCache),n==null){var u=ct.alternate;u!==null&&(u=u.updateQueue,u!==null&&(u=u.memoCache,u!=null&&(n={data:u.data.map(function(f){return f.slice()}),index:0})))}if(n==null&&(n={data:[],index:0}),s===null&&(s=Zu(),ct.updateQueue=s),s.memoCache=n,s=n.data[n.index],s===void 0)for(s=n.data[n.index]=Array(e),u=0;u<e;u++)s[u]=Et;return n.index++,s}function Ci(e,n){return typeof n=="function"?n(e):n}function hr(e){var n=Jt();return Ju(n,Ct,e)}function Ju(e,n,s){var u=e.queue;if(u===null)throw Error(l(311));u.lastRenderedReducer=s;var f=e.baseQueue,g=u.pending;if(g!==null){if(f!==null){var y=f.next;f.next=g.next,g.next=y}n.baseQueue=f=g,u.pending=null}if(g=e.baseState,f===null)e.memoizedState=g;else{n=f.next;var v=y=null,x=null,R=n,_=!1;do{var B=R.lane&-536870913;if(B!==R.lane?(bt&B)===B:(rn&B)===B){var E=R.revertLane;if(E===0)x!==null&&(x=x.next={lane:0,revertLane:0,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null}),B===kl&&(_=!0);else if((rn&E)===E){R=R.next,E===kl&&(_=!0);continue}else B={lane:0,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},x===null?(v=x=B,y=g):x=x.next=B,ct.lanes|=E,gn|=E;B=R.action,$n&&s(g,B),g=R.hasEagerState?R.eagerState:s(g,B)}else E={lane:B,revertLane:R.revertLane,action:R.action,hasEagerState:R.hasEagerState,eagerState:R.eagerState,next:null},x===null?(v=x=E,y=g):x=x.next=E,ct.lanes|=B,gn|=B;R=R.next}while(R!==null&&R!==n);if(x===null?y=g:x.next=v,!_e(g,e.memoizedState)&&(se=!0,_&&(s=Al,s!==null)))throw s;e.memoizedState=g,e.baseState=y,e.baseQueue=x,u.lastRenderedState=g}return f===null&&(u.lanes=0),[e.memoizedState,u.dispatch]}function Fu(e){var n=Jt(),s=n.queue;if(s===null)throw Error(l(311));s.lastRenderedReducer=e;var u=s.dispatch,f=s.pending,g=n.memoizedState;if(f!==null){s.pending=null;var y=f=f.next;do g=e(g,y.action),y=y.next;while(y!==f);_e(g,n.memoizedState)||(se=!0),n.memoizedState=g,n.baseQueue===null&&(n.baseState=g),s.lastRenderedState=g}return[g,u]}function sg(e,n,s){var u=ct,f=Jt(),g=kt;if(g){if(s===void 0)throw Error(l(407));s=s()}else s=n();var y=!_e((Ct||f).memoizedState,s);y&&(f.memoizedState=s,se=!0),f=f.queue;var v=og.bind(null,u,f,e);if(Ns(2048,8,v,[e]),f.getSnapshot!==n||y||Kt!==null&&Kt.memoizedState.tag&1){if(u.flags|=2048,Ml(9,fr(),rg.bind(null,u,f,s,n),null),Bt===null)throw Error(l(349));g||(rn&124)!==0||ag(u,n,s)}return s}function ag(e,n,s){e.flags|=16384,e={getSnapshot:n,value:s},n=ct.updateQueue,n===null?(n=Zu(),ct.updateQueue=n,n.stores=[e]):(s=n.stores,s===null?n.stores=[e]:s.push(e))}function rg(e,n,s,u){n.value=s,n.getSnapshot=u,ug(n)&&hg(e)}function og(e,n,s){return s(function(){ug(n)&&hg(e)})}function ug(e){var n=e.getSnapshot;e=e.value;try{var s=n();return!_e(e,s)}catch{return!0}}function hg(e){var n=xl(e,2);n!==null&&Ve(n,e,2)}function Iu(e){var n=Ae();if(typeof e=="function"){var s=e;if(e=s(),$n){Ii(!0);try{s()}finally{Ii(!1)}}}return n.memoizedState=n.baseState=e,n.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ci,lastRenderedState:e},n}function fg(e,n,s,u){return e.baseState=s,Ju(e,Ct,typeof u=="function"?u:Ci)}function Iy(e,n,s,u,f){if(dr(e))throw Error(l(485));if(e=n.action,e!==null){var g={payload:f,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(y){g.listeners.push(y)}};D.T!==null?s(!0):g.isTransition=!1,u(g),s=n.pending,s===null?(g.next=n.pending=g,cg(n,g)):(g.next=s.next,n.pending=s.next=g)}}function cg(e,n){var s=n.action,u=n.payload,f=e.state;if(n.isTransition){var g=D.T,y={};D.T=y;try{var v=s(f,u),x=D.S;x!==null&&x(y,v),dg(e,n,v)}catch(R){th(e,n,R)}finally{D.T=g}}else try{g=s(f,u),dg(e,n,g)}catch(R){th(e,n,R)}}function dg(e,n,s){s!==null&&typeof s=="object"&&typeof s.then=="function"?s.then(function(u){gg(e,n,u)},function(u){return th(e,n,u)}):gg(e,n,s)}function gg(e,n,s){n.status="fulfilled",n.value=s,pg(n),e.state=s,n=e.pending,n!==null&&(s=n.next,s===n?e.pending=null:(s=s.next,n.next=s,cg(e,s)))}function th(e,n,s){var u=e.pending;if(e.pending=null,u!==null){u=u.next;do n.status="rejected",n.reason=s,pg(n),n=n.next;while(n!==u)}e.action=null}function pg(e){e=e.listeners;for(var n=0;n<e.length;n++)(0,e[n])()}function mg(e,n){return n}function Og(e,n){if(kt){var s=Bt.formState;if(s!==null){t:{var u=ct;if(kt){if(Lt){e:{for(var f=Lt,g=ui;f.nodeType!==8;){if(!g){f=null;break e}if(f=si(f.nextSibling),f===null){f=null;break e}}g=f.data,f=g==="F!"||g==="F"?f:null}if(f){Lt=si(f.nextSibling),u=f.data==="F!";break t}}Hn(u)}u=!1}u&&(n=s[0])}}return s=Ae(),s.memoizedState=s.baseState=n,u={pending:null,lanes:0,dispatch:null,lastRenderedReducer:mg,lastRenderedState:n},s.queue=u,s=Ng.bind(null,ct,u),u.dispatch=s,u=Iu(!1),g=sh.bind(null,ct,!1,u.queue),u=Ae(),f={state:n,dispatch:null,action:e,pending:null},u.queue=f,s=Iy.bind(null,ct,f,g,s),f.dispatch=s,u.memoizedState=e,[n,s,!1]}function yg(e){var n=Jt();return Sg(n,Ct,e)}function Sg(e,n,s){if(n=Ju(e,n,mg)[0],e=hr(Ci)[0],typeof n=="object"&&n!==null&&typeof n.then=="function")try{var u=_s(n)}catch(y){throw y===Es?lr:y}else u=n;n=Jt();var f=n.queue,g=f.dispatch;return s!==n.memoizedState&&(ct.flags|=2048,Ml(9,fr(),tS.bind(null,f,s),null)),[u,g,e]}function tS(e,n){e.action=n}function vg(e){var n=Jt(),s=Ct;if(s!==null)return Sg(n,s,e);Jt(),n=n.memoizedState,s=Jt();var u=s.queue.dispatch;return s.memoizedState=e,[n,u,!1]}function Ml(e,n,s,u){return e={tag:e,create:s,deps:u,inst:n,next:null},n=ct.updateQueue,n===null&&(n=Zu(),ct.updateQueue=n),s=n.lastEffect,s===null?n.lastEffect=e.next=e:(u=s.next,s.next=e,e.next=u,n.lastEffect=e),e}function fr(){return{destroy:void 0,resource:void 0}}function bg(){return Jt().memoizedState}function cr(e,n,s,u){var f=Ae();u=u===void 0?null:u,ct.flags|=e,f.memoizedState=Ml(1|n,fr(),s,u)}function Ns(e,n,s,u){var f=Jt();u=u===void 0?null:u;var g=f.memoizedState.inst;Ct!==null&&u!==null&&Lu(u,Ct.memoizedState.deps)?f.memoizedState=Ml(n,g,s,u):(ct.flags|=e,f.memoizedState=Ml(1|n,g,s,u))}function xg(e,n){cr(8390656,8,e,n)}function Tg(e,n){Ns(2048,8,e,n)}function wg(e,n){return Ns(4,2,e,n)}function Qg(e,n){return Ns(4,4,e,n)}function kg(e,n){if(typeof n=="function"){e=e();var s=n(e);return function(){typeof s=="function"?s():n(null)}}if(n!=null)return e=e(),n.current=e,function(){n.current=null}}function Ag(e,n,s){s=s!=null?s.concat([e]):null,Ns(4,4,kg.bind(null,n,e),s)}function eh(){}function Rg(e,n){var s=Jt();n=n===void 0?null:n;var u=s.memoizedState;return n!==null&&Lu(n,u[1])?u[0]:(s.memoizedState=[e,n],e)}function Eg(e,n){var s=Jt();n=n===void 0?null:n;var u=s.memoizedState;if(n!==null&&Lu(n,u[1]))return u[0];if(u=e(),$n){Ii(!0);try{e()}finally{Ii(!1)}}return s.memoizedState=[u,n],u}function ih(e,n,s){return s===void 0||(rn&1073741824)!==0?e.memoizedState=n:(e.memoizedState=s,e=Cp(),ct.lanes|=e,gn|=e,s)}function zg(e,n,s,u){return _e(s,n)?s:Rl.current!==null?(e=ih(e,s,u),_e(e,n)||(se=!0),e):(rn&42)===0?(se=!0,e.memoizedState=s):(e=Cp(),ct.lanes|=e,gn|=e,n)}function Mg(e,n,s,u,f){var g=L.p;L.p=g!==0&&8>g?g:8;var y=D.T,v={};D.T=v,sh(e,!1,n,s);try{var x=f(),R=D.S;if(R!==null&&R(v,x),x!==null&&typeof x=="object"&&typeof x.then=="function"){var _=Ky(x,u);Us(e,n,_,Pe(e))}else Us(e,n,u,Pe(e))}catch(B){Us(e,n,{then:function(){},status:"rejected",reason:B},Pe())}finally{L.p=g,D.T=y}}function eS(){}function nh(e,n,s,u){if(e.tag!==5)throw Error(l(476));var f=Cg(e).queue;Mg(e,f,n,it,s===null?eS:function(){return Dg(e),s(u)})}function Cg(e){var n=e.memoizedState;if(n!==null)return n;n={memoizedState:it,baseState:it,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ci,lastRenderedState:it},next:null};var s={};return n.next={memoizedState:s,baseState:s,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ci,lastRenderedState:s},next:null},e.memoizedState=n,e=e.alternate,e!==null&&(e.memoizedState=n),n}function Dg(e){var n=Cg(e).next.queue;Us(e,n,{},Pe())}function lh(){return ye(ea)}function qg(){return Jt().memoizedState}function _g(){return Jt().memoizedState}function iS(e){for(var n=e.return;n!==null;){switch(n.tag){case 24:case 3:var s=Pe();e=sn(s);var u=an(n,e,s);u!==null&&(Ve(u,n,s),Ms(u,n,s)),n={cache:qu()},e.payload=n;return}n=n.return}}function nS(e,n,s){var u=Pe();s={lane:u,revertLane:0,action:s,hasEagerState:!1,eagerState:null,next:null},dr(e)?Ug(n,s):(s=wu(e,n,s,u),s!==null&&(Ve(s,e,u),Bg(s,n,u)))}function Ng(e,n,s){var u=Pe();Us(e,n,s,u)}function Us(e,n,s,u){var f={lane:u,revertLane:0,action:s,hasEagerState:!1,eagerState:null,next:null};if(dr(e))Ug(n,f);else{var g=e.alternate;if(e.lanes===0&&(g===null||g.lanes===0)&&(g=n.lastRenderedReducer,g!==null))try{var y=n.lastRenderedState,v=g(y,s);if(f.hasEagerState=!0,f.eagerState=v,_e(v,y))return Ka(e,n,f,0),Bt===null&&Za(),!1}catch{}finally{}if(s=wu(e,n,f,u),s!==null)return Ve(s,e,u),Bg(s,n,u),!0}return!1}function sh(e,n,s,u){if(u={lane:2,revertLane:Uh(),action:u,hasEagerState:!1,eagerState:null,next:null},dr(e)){if(n)throw Error(l(479))}else n=wu(e,s,u,2),n!==null&&Ve(n,e,2)}function dr(e){var n=e.alternate;return e===ct||n!==null&&n===ct}function Ug(e,n){El=rr=!0;var s=e.pending;s===null?n.next=n:(n.next=s.next,s.next=n),e.pending=n}function Bg(e,n,s){if((s&4194048)!==0){var u=n.lanes;u&=e.pendingLanes,s|=u,n.lanes=s,$c(e,s)}}var gr={readContext:ye,use:ur,useCallback:Wt,useContext:Wt,useEffect:Wt,useImperativeHandle:Wt,useLayoutEffect:Wt,useInsertionEffect:Wt,useMemo:Wt,useReducer:Wt,useRef:Wt,useState:Wt,useDebugValue:Wt,useDeferredValue:Wt,useTransition:Wt,useSyncExternalStore:Wt,useId:Wt,useHostTransitionStatus:Wt,useFormState:Wt,useActionState:Wt,useOptimistic:Wt,useMemoCache:Wt,useCacheRefresh:Wt},Xg={readContext:ye,use:ur,useCallback:function(e,n){return Ae().memoizedState=[e,n===void 0?null:n],e},useContext:ye,useEffect:xg,useImperativeHandle:function(e,n,s){s=s!=null?s.concat([e]):null,cr(4194308,4,kg.bind(null,n,e),s)},useLayoutEffect:function(e,n){return cr(4194308,4,e,n)},useInsertionEffect:function(e,n){cr(4,2,e,n)},useMemo:function(e,n){var s=Ae();n=n===void 0?null:n;var u=e();if($n){Ii(!0);try{e()}finally{Ii(!1)}}return s.memoizedState=[u,n],u},useReducer:function(e,n,s){var u=Ae();if(s!==void 0){var f=s(n);if($n){Ii(!0);try{s(n)}finally{Ii(!1)}}}else f=n;return u.memoizedState=u.baseState=f,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:f},u.queue=e,e=e.dispatch=nS.bind(null,ct,e),[u.memoizedState,e]},useRef:function(e){var n=Ae();return e={current:e},n.memoizedState=e},useState:function(e){e=Iu(e);var n=e.queue,s=Ng.bind(null,ct,n);return n.dispatch=s,[e.memoizedState,s]},useDebugValue:eh,useDeferredValue:function(e,n){var s=Ae();return ih(s,e,n)},useTransition:function(){var e=Iu(!1);return e=Mg.bind(null,ct,e.queue,!0,!1),Ae().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,n,s){var u=ct,f=Ae();if(kt){if(s===void 0)throw Error(l(407));s=s()}else{if(s=n(),Bt===null)throw Error(l(349));(bt&124)!==0||ag(u,n,s)}f.memoizedState=s;var g={value:s,getSnapshot:n};return f.queue=g,xg(og.bind(null,u,g,e),[e]),u.flags|=2048,Ml(9,fr(),rg.bind(null,u,g,s,n),null),s},useId:function(){var e=Ae(),n=Bt.identifierPrefix;if(kt){var s=Ei,u=Ri;s=(u&~(1<<32-qe(u)-1)).toString(32)+s,n="«"+n+"R"+s,s=or++,0<s&&(n+="H"+s.toString(32)),n+="»"}else s=Jy++,n="«"+n+"r"+s.toString(32)+"»";return e.memoizedState=n},useHostTransitionStatus:lh,useFormState:Og,useActionState:Og,useOptimistic:function(e){var n=Ae();n.memoizedState=n.baseState=e;var s={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return n.queue=s,n=sh.bind(null,ct,!0,s),s.dispatch=n,[e,n]},useMemoCache:Ku,useCacheRefresh:function(){return Ae().memoizedState=iS.bind(null,ct)}},Pg={readContext:ye,use:ur,useCallback:Rg,useContext:ye,useEffect:Tg,useImperativeHandle:Ag,useInsertionEffect:wg,useLayoutEffect:Qg,useMemo:Eg,useReducer:hr,useRef:bg,useState:function(){return hr(Ci)},useDebugValue:eh,useDeferredValue:function(e,n){var s=Jt();return zg(s,Ct.memoizedState,e,n)},useTransition:function(){var e=hr(Ci)[0],n=Jt().memoizedState;return[typeof e=="boolean"?e:_s(e),n]},useSyncExternalStore:sg,useId:qg,useHostTransitionStatus:lh,useFormState:yg,useActionState:yg,useOptimistic:function(e,n){var s=Jt();return fg(s,Ct,e,n)},useMemoCache:Ku,useCacheRefresh:_g},lS={readContext:ye,use:ur,useCallback:Rg,useContext:ye,useEffect:Tg,useImperativeHandle:Ag,useInsertionEffect:wg,useLayoutEffect:Qg,useMemo:Eg,useReducer:Fu,useRef:bg,useState:function(){return Fu(Ci)},useDebugValue:eh,useDeferredValue:function(e,n){var s=Jt();return Ct===null?ih(s,e,n):zg(s,Ct.memoizedState,e,n)},useTransition:function(){var e=Fu(Ci)[0],n=Jt().memoizedState;return[typeof e=="boolean"?e:_s(e),n]},useSyncExternalStore:sg,useId:qg,useHostTransitionStatus:lh,useFormState:vg,useActionState:vg,useOptimistic:function(e,n){var s=Jt();return Ct!==null?fg(s,Ct,e,n):(s.baseState=e,[e,s.queue.dispatch])},useMemoCache:Ku,useCacheRefresh:_g},Cl=null,Bs=0;function pr(e){var n=Bs;return Bs+=1,Cl===null&&(Cl=[]),Jd(Cl,e,n)}function Xs(e,n){n=n.props.ref,e.ref=n!==void 0?n:null}function mr(e,n){throw n.$$typeof===O?Error(l(525)):(e=Object.prototype.toString.call(n),Error(l(31,e==="[object Object]"?"object with keys {"+Object.keys(n).join(", ")+"}":e)))}function Vg(e){var n=e._init;return n(e._payload)}function Hg(e){function n(k,Q){if(e){var A=k.deletions;A===null?(k.deletions=[Q],k.flags|=16):A.push(Q)}}function s(k,Q){if(!e)return null;for(;Q!==null;)n(k,Q),Q=Q.sibling;return null}function u(k){for(var Q=new Map;k!==null;)k.key!==null?Q.set(k.key,k):Q.set(k.index,k),k=k.sibling;return Q}function f(k,Q){return k=Ai(k,Q),k.index=0,k.sibling=null,k}function g(k,Q,A){return k.index=A,e?(A=k.alternate,A!==null?(A=A.index,A<Q?(k.flags|=67108866,Q):A):(k.flags|=67108866,Q)):(k.flags|=1048576,Q)}function y(k){return e&&k.alternate===null&&(k.flags|=67108866),k}function v(k,Q,A,N){return Q===null||Q.tag!==6?(Q=ku(A,k.mode,N),Q.return=k,Q):(Q=f(Q,A),Q.return=k,Q)}function x(k,Q,A,N){var K=A.type;return K===T?_(k,Q,A.props.children,N,A.key):Q!==null&&(Q.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===Ot&&Vg(K)===Q.type)?(Q=f(Q,A.props),Xs(Q,A),Q.return=k,Q):(Q=Fa(A.type,A.key,A.props,null,k.mode,N),Xs(Q,A),Q.return=k,Q)}function R(k,Q,A,N){return Q===null||Q.tag!==4||Q.stateNode.containerInfo!==A.containerInfo||Q.stateNode.implementation!==A.implementation?(Q=Au(A,k.mode,N),Q.return=k,Q):(Q=f(Q,A.children||[]),Q.return=k,Q)}function _(k,Q,A,N,K){return Q===null||Q.tag!==7?(Q=Bn(A,k.mode,N,K),Q.return=k,Q):(Q=f(Q,A),Q.return=k,Q)}function B(k,Q,A){if(typeof Q=="string"&&Q!==""||typeof Q=="number"||typeof Q=="bigint")return Q=ku(""+Q,k.mode,A),Q.return=k,Q;if(typeof Q=="object"&&Q!==null){switch(Q.$$typeof){case S:return A=Fa(Q.type,Q.key,Q.props,null,k.mode,A),Xs(A,Q),A.return=k,A;case b:return Q=Au(Q,k.mode,A),Q.return=k,Q;case Ot:var N=Q._init;return Q=N(Q._payload),B(k,Q,A)}if(vt(Q)||F(Q))return Q=Bn(Q,k.mode,A,null),Q.return=k,Q;if(typeof Q.then=="function")return B(k,pr(Q),A);if(Q.$$typeof===j)return B(k,ir(k,Q),A);mr(k,Q)}return null}function E(k,Q,A,N){var K=Q!==null?Q.key:null;if(typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint")return K!==null?null:v(k,Q,""+A,N);if(typeof A=="object"&&A!==null){switch(A.$$typeof){case S:return A.key===K?x(k,Q,A,N):null;case b:return A.key===K?R(k,Q,A,N):null;case Ot:return K=A._init,A=K(A._payload),E(k,Q,A,N)}if(vt(A)||F(A))return K!==null?null:_(k,Q,A,N,null);if(typeof A.then=="function")return E(k,Q,pr(A),N);if(A.$$typeof===j)return E(k,Q,ir(k,A),N);mr(k,A)}return null}function M(k,Q,A,N,K){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return k=k.get(A)||null,v(Q,k,""+N,K);if(typeof N=="object"&&N!==null){switch(N.$$typeof){case S:return k=k.get(N.key===null?A:N.key)||null,x(Q,k,N,K);case b:return k=k.get(N.key===null?A:N.key)||null,R(Q,k,N,K);case Ot:var dt=N._init;return N=dt(N._payload),M(k,Q,A,N,K)}if(vt(N)||F(N))return k=k.get(A)||null,_(Q,k,N,K,null);if(typeof N.then=="function")return M(k,Q,A,pr(N),K);if(N.$$typeof===j)return M(k,Q,A,ir(Q,N),K);mr(Q,N)}return null}function rt(k,Q,A,N){for(var K=null,dt=null,et=Q,lt=Q=0,re=null;et!==null&&lt<A.length;lt++){et.index>lt?(re=et,et=null):re=et.sibling;var Tt=E(k,et,A[lt],N);if(Tt===null){et===null&&(et=re);break}e&&et&&Tt.alternate===null&&n(k,et),Q=g(Tt,Q,lt),dt===null?K=Tt:dt.sibling=Tt,dt=Tt,et=re}if(lt===A.length)return s(k,et),kt&&Pn(k,lt),K;if(et===null){for(;lt<A.length;lt++)et=B(k,A[lt],N),et!==null&&(Q=g(et,Q,lt),dt===null?K=et:dt.sibling=et,dt=et);return kt&&Pn(k,lt),K}for(et=u(et);lt<A.length;lt++)re=M(et,k,lt,A[lt],N),re!==null&&(e&&re.alternate!==null&&et.delete(re.key===null?lt:re.key),Q=g(re,Q,lt),dt===null?K=re:dt.sibling=re,dt=re);return e&&et.forEach(function(Tn){return n(k,Tn)}),kt&&Pn(k,lt),K}function nt(k,Q,A,N){if(A==null)throw Error(l(151));for(var K=null,dt=null,et=Q,lt=Q=0,re=null,Tt=A.next();et!==null&&!Tt.done;lt++,Tt=A.next()){et.index>lt?(re=et,et=null):re=et.sibling;var Tn=E(k,et,Tt.value,N);if(Tn===null){et===null&&(et=re);break}e&&et&&Tn.alternate===null&&n(k,et),Q=g(Tn,Q,lt),dt===null?K=Tn:dt.sibling=Tn,dt=Tn,et=re}if(Tt.done)return s(k,et),kt&&Pn(k,lt),K;if(et===null){for(;!Tt.done;lt++,Tt=A.next())Tt=B(k,Tt.value,N),Tt!==null&&(Q=g(Tt,Q,lt),dt===null?K=Tt:dt.sibling=Tt,dt=Tt);return kt&&Pn(k,lt),K}for(et=u(et);!Tt.done;lt++,Tt=A.next())Tt=M(et,k,lt,Tt.value,N),Tt!==null&&(e&&Tt.alternate!==null&&et.delete(Tt.key===null?lt:Tt.key),Q=g(Tt,Q,lt),dt===null?K=Tt:dt.sibling=Tt,dt=Tt);return e&&et.forEach(function(sv){return n(k,sv)}),kt&&Pn(k,lt),K}function qt(k,Q,A,N){if(typeof A=="object"&&A!==null&&A.type===T&&A.key===null&&(A=A.props.children),typeof A=="object"&&A!==null){switch(A.$$typeof){case S:t:{for(var K=A.key;Q!==null;){if(Q.key===K){if(K=A.type,K===T){if(Q.tag===7){s(k,Q.sibling),N=f(Q,A.props.children),N.return=k,k=N;break t}}else if(Q.elementType===K||typeof K=="object"&&K!==null&&K.$$typeof===Ot&&Vg(K)===Q.type){s(k,Q.sibling),N=f(Q,A.props),Xs(N,A),N.return=k,k=N;break t}s(k,Q);break}else n(k,Q);Q=Q.sibling}A.type===T?(N=Bn(A.props.children,k.mode,N,A.key),N.return=k,k=N):(N=Fa(A.type,A.key,A.props,null,k.mode,N),Xs(N,A),N.return=k,k=N)}return y(k);case b:t:{for(K=A.key;Q!==null;){if(Q.key===K)if(Q.tag===4&&Q.stateNode.containerInfo===A.containerInfo&&Q.stateNode.implementation===A.implementation){s(k,Q.sibling),N=f(Q,A.children||[]),N.return=k,k=N;break t}else{s(k,Q);break}else n(k,Q);Q=Q.sibling}N=Au(A,k.mode,N),N.return=k,k=N}return y(k);case Ot:return K=A._init,A=K(A._payload),qt(k,Q,A,N)}if(vt(A))return rt(k,Q,A,N);if(F(A)){if(K=F(A),typeof K!="function")throw Error(l(150));return A=K.call(A),nt(k,Q,A,N)}if(typeof A.then=="function")return qt(k,Q,pr(A),N);if(A.$$typeof===j)return qt(k,Q,ir(k,A),N);mr(k,A)}return typeof A=="string"&&A!==""||typeof A=="number"||typeof A=="bigint"?(A=""+A,Q!==null&&Q.tag===6?(s(k,Q.sibling),N=f(Q,A),N.return=k,k=N):(s(k,Q),N=ku(A,k.mode,N),N.return=k,k=N),y(k)):s(k,Q)}return function(k,Q,A,N){try{Bs=0;var K=qt(k,Q,A,N);return Cl=null,K}catch(et){if(et===Es||et===lr)throw et;var dt=Ne(29,et,null,k.mode);return dt.lanes=N,dt.return=k,dt}finally{}}}var Dl=Hg(!0),jg=Hg(!1),Je=U(null),hi=null;function on(e){var n=e.alternate;P(ee,ee.current&1),P(Je,e),hi===null&&(n===null||Rl.current!==null||n.memoizedState!==null)&&(hi=e)}function Lg(e){if(e.tag===22){if(P(ee,ee.current),P(Je,e),hi===null){var n=e.alternate;n!==null&&n.memoizedState!==null&&(hi=e)}}else un()}function un(){P(ee,ee.current),P(Je,Je.current)}function Di(e){V(Je),hi===e&&(hi=null),V(ee)}var ee=U(0);function Or(e){for(var n=e;n!==null;){if(n.tag===13){var s=n.memoizedState;if(s!==null&&(s=s.dehydrated,s===null||s.data==="$?"||Zh(s)))return n}else if(n.tag===19&&n.memoizedProps.revealOrder!==void 0){if((n.flags&128)!==0)return n}else if(n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return null;n=n.return}n.sibling.return=n.return,n=n.sibling}return null}function ah(e,n,s,u){n=e.memoizedState,s=s(u,n),s=s==null?n:m({},n,s),e.memoizedState=s,e.lanes===0&&(e.updateQueue.baseState=s)}var rh={enqueueSetState:function(e,n,s){e=e._reactInternals;var u=Pe(),f=sn(u);f.payload=n,s!=null&&(f.callback=s),n=an(e,f,u),n!==null&&(Ve(n,e,u),Ms(n,e,u))},enqueueReplaceState:function(e,n,s){e=e._reactInternals;var u=Pe(),f=sn(u);f.tag=1,f.payload=n,s!=null&&(f.callback=s),n=an(e,f,u),n!==null&&(Ve(n,e,u),Ms(n,e,u))},enqueueForceUpdate:function(e,n){e=e._reactInternals;var s=Pe(),u=sn(s);u.tag=2,n!=null&&(u.callback=n),n=an(e,u,s),n!==null&&(Ve(n,e,s),Ms(n,e,s))}};function Gg(e,n,s,u,f,g,y){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(u,g,y):n.prototype&&n.prototype.isPureReactComponent?!bs(s,u)||!bs(f,g):!0}function $g(e,n,s,u){e=n.state,typeof n.componentWillReceiveProps=="function"&&n.componentWillReceiveProps(s,u),typeof n.UNSAFE_componentWillReceiveProps=="function"&&n.UNSAFE_componentWillReceiveProps(s,u),n.state!==e&&rh.enqueueReplaceState(n,n.state,null)}function Wn(e,n){var s=n;if("ref"in n){s={};for(var u in n)u!=="ref"&&(s[u]=n[u])}if(e=e.defaultProps){s===n&&(s=m({},s));for(var f in e)s[f]===void 0&&(s[f]=e[f])}return s}var yr=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var n=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(n))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function Wg(e){yr(e)}function Yg(e){console.error(e)}function Zg(e){yr(e)}function Sr(e,n){try{var s=e.onUncaughtError;s(n.value,{componentStack:n.stack})}catch(u){setTimeout(function(){throw u})}}function Kg(e,n,s){try{var u=e.onCaughtError;u(s.value,{componentStack:s.stack,errorBoundary:n.tag===1?n.stateNode:null})}catch(f){setTimeout(function(){throw f})}}function oh(e,n,s){return s=sn(s),s.tag=3,s.payload={element:null},s.callback=function(){Sr(e,n)},s}function Jg(e){return e=sn(e),e.tag=3,e}function Fg(e,n,s,u){var f=s.type.getDerivedStateFromError;if(typeof f=="function"){var g=u.value;e.payload=function(){return f(g)},e.callback=function(){Kg(n,s,u)}}var y=s.stateNode;y!==null&&typeof y.componentDidCatch=="function"&&(e.callback=function(){Kg(n,s,u),typeof f!="function"&&(pn===null?pn=new Set([this]):pn.add(this));var v=u.stack;this.componentDidCatch(u.value,{componentStack:v!==null?v:""})})}function sS(e,n,s,u,f){if(s.flags|=32768,u!==null&&typeof u=="object"&&typeof u.then=="function"){if(n=s.alternate,n!==null&&ks(n,s,f,!0),s=Je.current,s!==null){switch(s.tag){case 13:return hi===null?Ch():s.alternate===null&&Gt===0&&(Gt=3),s.flags&=-257,s.flags|=65536,s.lanes=f,u===Uu?s.flags|=16384:(n=s.updateQueue,n===null?s.updateQueue=new Set([u]):n.add(u),qh(e,u,f)),!1;case 22:return s.flags|=65536,u===Uu?s.flags|=16384:(n=s.updateQueue,n===null?(n={transitions:null,markerInstances:null,retryQueue:new Set([u])},s.updateQueue=n):(s=n.retryQueue,s===null?n.retryQueue=new Set([u]):s.add(u)),qh(e,u,f)),!1}throw Error(l(435,s.tag))}return qh(e,u,f),Ch(),!1}if(kt)return n=Je.current,n!==null?((n.flags&65536)===0&&(n.flags|=256),n.flags|=65536,n.lanes=f,u!==zu&&(e=Error(l(422),{cause:u}),Qs(We(e,s)))):(u!==zu&&(n=Error(l(423),{cause:u}),Qs(We(n,s))),e=e.current.alternate,e.flags|=65536,f&=-f,e.lanes|=f,u=We(u,s),f=oh(e.stateNode,u,f),Pu(e,f),Gt!==4&&(Gt=2)),!1;var g=Error(l(520),{cause:u});if(g=We(g,s),$s===null?$s=[g]:$s.push(g),Gt!==4&&(Gt=2),n===null)return!0;u=We(u,s),s=n;do{switch(s.tag){case 3:return s.flags|=65536,e=f&-f,s.lanes|=e,e=oh(s.stateNode,u,e),Pu(s,e),!1;case 1:if(n=s.type,g=s.stateNode,(s.flags&128)===0&&(typeof n.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(pn===null||!pn.has(g))))return s.flags|=65536,f&=-f,s.lanes|=f,f=Jg(f),Fg(f,e,s,u),Pu(s,f),!1}s=s.return}while(s!==null);return!1}var Ig=Error(l(461)),se=!1;function ue(e,n,s,u){n.child=e===null?jg(n,null,s,u):Dl(n,e.child,s,u)}function tp(e,n,s,u,f){s=s.render;var g=n.ref;if("ref"in u){var y={};for(var v in u)v!=="ref"&&(y[v]=u[v])}else y=u;return Ln(n),u=Gu(e,n,s,y,g,f),v=$u(),e!==null&&!se?(Wu(e,n,f),qi(e,n,f)):(kt&&v&&Ru(n),n.flags|=1,ue(e,n,u,f),n.child)}function ep(e,n,s,u,f){if(e===null){var g=s.type;return typeof g=="function"&&!Qu(g)&&g.defaultProps===void 0&&s.compare===null?(n.tag=15,n.type=g,ip(e,n,g,u,f)):(e=Fa(s.type,null,u,n,n.mode,f),e.ref=n.ref,e.return=n,n.child=e)}if(g=e.child,!mh(e,f)){var y=g.memoizedProps;if(s=s.compare,s=s!==null?s:bs,s(y,u)&&e.ref===n.ref)return qi(e,n,f)}return n.flags|=1,e=Ai(g,u),e.ref=n.ref,e.return=n,n.child=e}function ip(e,n,s,u,f){if(e!==null){var g=e.memoizedProps;if(bs(g,u)&&e.ref===n.ref)if(se=!1,n.pendingProps=u=g,mh(e,f))(e.flags&131072)!==0&&(se=!0);else return n.lanes=e.lanes,qi(e,n,f)}return uh(e,n,s,u,f)}function np(e,n,s){var u=n.pendingProps,f=u.children,g=e!==null?e.memoizedState:null;if(u.mode==="hidden"){if((n.flags&128)!==0){if(u=g!==null?g.baseLanes|s:s,e!==null){for(f=n.child=e.child,g=0;f!==null;)g=g|f.lanes|f.childLanes,f=f.sibling;n.childLanes=g&~u}else n.childLanes=0,n.child=null;return lp(e,n,u,s)}if((s&536870912)!==0)n.memoizedState={baseLanes:0,cachePool:null},e!==null&&nr(n,g!==null?g.cachePool:null),g!==null?ig(n,g):Hu(),Lg(n);else return n.lanes=n.childLanes=536870912,lp(e,n,g!==null?g.baseLanes|s:s,s)}else g!==null?(nr(n,g.cachePool),ig(n,g),un(),n.memoizedState=null):(e!==null&&nr(n,null),Hu(),un());return ue(e,n,f,s),n.child}function lp(e,n,s,u){var f=Nu();return f=f===null?null:{parent:te._currentValue,pool:f},n.memoizedState={baseLanes:s,cachePool:f},e!==null&&nr(n,null),Hu(),Lg(n),e!==null&&ks(e,n,u,!0),null}function vr(e,n){var s=n.ref;if(s===null)e!==null&&e.ref!==null&&(n.flags|=4194816);else{if(typeof s!="function"&&typeof s!="object")throw Error(l(284));(e===null||e.ref!==s)&&(n.flags|=4194816)}}function uh(e,n,s,u,f){return Ln(n),s=Gu(e,n,s,u,void 0,f),u=$u(),e!==null&&!se?(Wu(e,n,f),qi(e,n,f)):(kt&&u&&Ru(n),n.flags|=1,ue(e,n,s,f),n.child)}function sp(e,n,s,u,f,g){return Ln(n),n.updateQueue=null,s=lg(n,u,s,f),ng(e),u=$u(),e!==null&&!se?(Wu(e,n,g),qi(e,n,g)):(kt&&u&&Ru(n),n.flags|=1,ue(e,n,s,g),n.child)}function ap(e,n,s,u,f){if(Ln(n),n.stateNode===null){var g=Tl,y=s.contextType;typeof y=="object"&&y!==null&&(g=ye(y)),g=new s(u,g),n.memoizedState=g.state!==null&&g.state!==void 0?g.state:null,g.updater=rh,n.stateNode=g,g._reactInternals=n,g=n.stateNode,g.props=u,g.state=n.memoizedState,g.refs={},Bu(n),y=s.contextType,g.context=typeof y=="object"&&y!==null?ye(y):Tl,g.state=n.memoizedState,y=s.getDerivedStateFromProps,typeof y=="function"&&(ah(n,s,y,u),g.state=n.memoizedState),typeof s.getDerivedStateFromProps=="function"||typeof g.getSnapshotBeforeUpdate=="function"||typeof g.UNSAFE_componentWillMount!="function"&&typeof g.componentWillMount!="function"||(y=g.state,typeof g.componentWillMount=="function"&&g.componentWillMount(),typeof g.UNSAFE_componentWillMount=="function"&&g.UNSAFE_componentWillMount(),y!==g.state&&rh.enqueueReplaceState(g,g.state,null),Ds(n,u,g,f),Cs(),g.state=n.memoizedState),typeof g.componentDidMount=="function"&&(n.flags|=4194308),u=!0}else if(e===null){g=n.stateNode;var v=n.memoizedProps,x=Wn(s,v);g.props=x;var R=g.context,_=s.contextType;y=Tl,typeof _=="object"&&_!==null&&(y=ye(_));var B=s.getDerivedStateFromProps;_=typeof B=="function"||typeof g.getSnapshotBeforeUpdate=="function",v=n.pendingProps!==v,_||typeof g.UNSAFE_componentWillReceiveProps!="function"&&typeof g.componentWillReceiveProps!="function"||(v||R!==y)&&$g(n,g,u,y),ln=!1;var E=n.memoizedState;g.state=E,Ds(n,u,g,f),Cs(),R=n.memoizedState,v||E!==R||ln?(typeof B=="function"&&(ah(n,s,B,u),R=n.memoizedState),(x=ln||Gg(n,s,x,u,E,R,y))?(_||typeof g.UNSAFE_componentWillMount!="function"&&typeof g.componentWillMount!="function"||(typeof g.componentWillMount=="function"&&g.componentWillMount(),typeof g.UNSAFE_componentWillMount=="function"&&g.UNSAFE_componentWillMount()),typeof g.componentDidMount=="function"&&(n.flags|=4194308)):(typeof g.componentDidMount=="function"&&(n.flags|=4194308),n.memoizedProps=u,n.memoizedState=R),g.props=u,g.state=R,g.context=y,u=x):(typeof g.componentDidMount=="function"&&(n.flags|=4194308),u=!1)}else{g=n.stateNode,Xu(e,n),y=n.memoizedProps,_=Wn(s,y),g.props=_,B=n.pendingProps,E=g.context,R=s.contextType,x=Tl,typeof R=="object"&&R!==null&&(x=ye(R)),v=s.getDerivedStateFromProps,(R=typeof v=="function"||typeof g.getSnapshotBeforeUpdate=="function")||typeof g.UNSAFE_componentWillReceiveProps!="function"&&typeof g.componentWillReceiveProps!="function"||(y!==B||E!==x)&&$g(n,g,u,x),ln=!1,E=n.memoizedState,g.state=E,Ds(n,u,g,f),Cs();var M=n.memoizedState;y!==B||E!==M||ln||e!==null&&e.dependencies!==null&&er(e.dependencies)?(typeof v=="function"&&(ah(n,s,v,u),M=n.memoizedState),(_=ln||Gg(n,s,_,u,E,M,x)||e!==null&&e.dependencies!==null&&er(e.dependencies))?(R||typeof g.UNSAFE_componentWillUpdate!="function"&&typeof g.componentWillUpdate!="function"||(typeof g.componentWillUpdate=="function"&&g.componentWillUpdate(u,M,x),typeof g.UNSAFE_componentWillUpdate=="function"&&g.UNSAFE_componentWillUpdate(u,M,x)),typeof g.componentDidUpdate=="function"&&(n.flags|=4),typeof g.getSnapshotBeforeUpdate=="function"&&(n.flags|=1024)):(typeof g.componentDidUpdate!="function"||y===e.memoizedProps&&E===e.memoizedState||(n.flags|=4),typeof g.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&E===e.memoizedState||(n.flags|=1024),n.memoizedProps=u,n.memoizedState=M),g.props=u,g.state=M,g.context=x,u=_):(typeof g.componentDidUpdate!="function"||y===e.memoizedProps&&E===e.memoizedState||(n.flags|=4),typeof g.getSnapshotBeforeUpdate!="function"||y===e.memoizedProps&&E===e.memoizedState||(n.flags|=1024),u=!1)}return g=u,vr(e,n),u=(n.flags&128)!==0,g||u?(g=n.stateNode,s=u&&typeof s.getDerivedStateFromError!="function"?null:g.render(),n.flags|=1,e!==null&&u?(n.child=Dl(n,e.child,null,f),n.child=Dl(n,null,s,f)):ue(e,n,s,f),n.memoizedState=g.state,e=n.child):e=qi(e,n,f),e}function rp(e,n,s,u){return ws(),n.flags|=256,ue(e,n,s,u),n.child}var hh={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function fh(e){return{baseLanes:e,cachePool:Yd()}}function ch(e,n,s){return e=e!==null?e.childLanes&~s:0,n&&(e|=Fe),e}function op(e,n,s){var u=n.pendingProps,f=!1,g=(n.flags&128)!==0,y;if((y=g)||(y=e!==null&&e.memoizedState===null?!1:(ee.current&2)!==0),y&&(f=!0,n.flags&=-129),y=(n.flags&32)!==0,n.flags&=-33,e===null){if(kt){if(f?on(n):un(),kt){var v=Lt,x;if(x=v){t:{for(x=v,v=ui;x.nodeType!==8;){if(!v){v=null;break t}if(x=si(x.nextSibling),x===null){v=null;break t}}v=x}v!==null?(n.memoizedState={dehydrated:v,treeContext:Xn!==null?{id:Ri,overflow:Ei}:null,retryLane:536870912,hydrationErrors:null},x=Ne(18,null,null,0),x.stateNode=v,x.return=n,n.child=x,xe=n,Lt=null,x=!0):x=!1}x||Hn(n)}if(v=n.memoizedState,v!==null&&(v=v.dehydrated,v!==null))return Zh(v)?n.lanes=32:n.lanes=536870912,null;Di(n)}return v=u.children,u=u.fallback,f?(un(),f=n.mode,v=br({mode:"hidden",children:v},f),u=Bn(u,f,s,null),v.return=n,u.return=n,v.sibling=u,n.child=v,f=n.child,f.memoizedState=fh(s),f.childLanes=ch(e,y,s),n.memoizedState=hh,u):(on(n),dh(n,v))}if(x=e.memoizedState,x!==null&&(v=x.dehydrated,v!==null)){if(g)n.flags&256?(on(n),n.flags&=-257,n=gh(e,n,s)):n.memoizedState!==null?(un(),n.child=e.child,n.flags|=128,n=null):(un(),f=u.fallback,v=n.mode,u=br({mode:"visible",children:u.children},v),f=Bn(f,v,s,null),f.flags|=2,u.return=n,f.return=n,u.sibling=f,n.child=u,Dl(n,e.child,null,s),u=n.child,u.memoizedState=fh(s),u.childLanes=ch(e,y,s),n.memoizedState=hh,n=f);else if(on(n),Zh(v)){if(y=v.nextSibling&&v.nextSibling.dataset,y)var R=y.dgst;y=R,u=Error(l(419)),u.stack="",u.digest=y,Qs({value:u,source:null,stack:null}),n=gh(e,n,s)}else if(se||ks(e,n,s,!1),y=(s&e.childLanes)!==0,se||y){if(y=Bt,y!==null&&(u=s&-s,u=(u&42)!==0?1:Ko(u),u=(u&(y.suspendedLanes|s))!==0?0:u,u!==0&&u!==x.retryLane))throw x.retryLane=u,xl(e,u),Ve(y,e,u),Ig;v.data==="$?"||Ch(),n=gh(e,n,s)}else v.data==="$?"?(n.flags|=192,n.child=e.child,n=null):(e=x.treeContext,Lt=si(v.nextSibling),xe=n,kt=!0,Vn=null,ui=!1,e!==null&&(Ze[Ke++]=Ri,Ze[Ke++]=Ei,Ze[Ke++]=Xn,Ri=e.id,Ei=e.overflow,Xn=n),n=dh(n,u.children),n.flags|=4096);return n}return f?(un(),f=u.fallback,v=n.mode,x=e.child,R=x.sibling,u=Ai(x,{mode:"hidden",children:u.children}),u.subtreeFlags=x.subtreeFlags&65011712,R!==null?f=Ai(R,f):(f=Bn(f,v,s,null),f.flags|=2),f.return=n,u.return=n,u.sibling=f,n.child=u,u=f,f=n.child,v=e.child.memoizedState,v===null?v=fh(s):(x=v.cachePool,x!==null?(R=te._currentValue,x=x.parent!==R?{parent:R,pool:R}:x):x=Yd(),v={baseLanes:v.baseLanes|s,cachePool:x}),f.memoizedState=v,f.childLanes=ch(e,y,s),n.memoizedState=hh,u):(on(n),s=e.child,e=s.sibling,s=Ai(s,{mode:"visible",children:u.children}),s.return=n,s.sibling=null,e!==null&&(y=n.deletions,y===null?(n.deletions=[e],n.flags|=16):y.push(e)),n.child=s,n.memoizedState=null,s)}function dh(e,n){return n=br({mode:"visible",children:n},e.mode),n.return=e,e.child=n}function br(e,n){return e=Ne(22,e,null,n),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function gh(e,n,s){return Dl(n,e.child,null,s),e=dh(n,n.pendingProps.children),e.flags|=2,n.memoizedState=null,e}function up(e,n,s){e.lanes|=n;var u=e.alternate;u!==null&&(u.lanes|=n),Cu(e.return,n,s)}function ph(e,n,s,u,f){var g=e.memoizedState;g===null?e.memoizedState={isBackwards:n,rendering:null,renderingStartTime:0,last:u,tail:s,tailMode:f}:(g.isBackwards=n,g.rendering=null,g.renderingStartTime=0,g.last=u,g.tail=s,g.tailMode=f)}function hp(e,n,s){var u=n.pendingProps,f=u.revealOrder,g=u.tail;if(ue(e,n,u.children,s),u=ee.current,(u&2)!==0)u=u&1|2,n.flags|=128;else{if(e!==null&&(e.flags&128)!==0)t:for(e=n.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&up(e,s,n);else if(e.tag===19)up(e,s,n);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===n)break t;for(;e.sibling===null;){if(e.return===null||e.return===n)break t;e=e.return}e.sibling.return=e.return,e=e.sibling}u&=1}switch(P(ee,u),f){case"forwards":for(s=n.child,f=null;s!==null;)e=s.alternate,e!==null&&Or(e)===null&&(f=s),s=s.sibling;s=f,s===null?(f=n.child,n.child=null):(f=s.sibling,s.sibling=null),ph(n,!1,f,s,g);break;case"backwards":for(s=null,f=n.child,n.child=null;f!==null;){if(e=f.alternate,e!==null&&Or(e)===null){n.child=f;break}e=f.sibling,f.sibling=s,s=f,f=e}ph(n,!0,s,null,g);break;case"together":ph(n,!1,null,null,void 0);break;default:n.memoizedState=null}return n.child}function qi(e,n,s){if(e!==null&&(n.dependencies=e.dependencies),gn|=n.lanes,(s&n.childLanes)===0)if(e!==null){if(ks(e,n,s,!1),(s&n.childLanes)===0)return null}else return null;if(e!==null&&n.child!==e.child)throw Error(l(153));if(n.child!==null){for(e=n.child,s=Ai(e,e.pendingProps),n.child=s,s.return=n;e.sibling!==null;)e=e.sibling,s=s.sibling=Ai(e,e.pendingProps),s.return=n;s.sibling=null}return n.child}function mh(e,n){return(e.lanes&n)!==0?!0:(e=e.dependencies,!!(e!==null&&er(e)))}function aS(e,n,s){switch(n.tag){case 3:Vt(n,n.stateNode.containerInfo),nn(n,te,e.memoizedState.cache),ws();break;case 27:case 5:Go(n);break;case 4:Vt(n,n.stateNode.containerInfo);break;case 10:nn(n,n.type,n.memoizedProps.value);break;case 13:var u=n.memoizedState;if(u!==null)return u.dehydrated!==null?(on(n),n.flags|=128,null):(s&n.child.childLanes)!==0?op(e,n,s):(on(n),e=qi(e,n,s),e!==null?e.sibling:null);on(n);break;case 19:var f=(e.flags&128)!==0;if(u=(s&n.childLanes)!==0,u||(ks(e,n,s,!1),u=(s&n.childLanes)!==0),f){if(u)return hp(e,n,s);n.flags|=128}if(f=n.memoizedState,f!==null&&(f.rendering=null,f.tail=null,f.lastEffect=null),P(ee,ee.current),u)break;return null;case 22:case 23:return n.lanes=0,np(e,n,s);case 24:nn(n,te,e.memoizedState.cache)}return qi(e,n,s)}function fp(e,n,s){if(e!==null)if(e.memoizedProps!==n.pendingProps)se=!0;else{if(!mh(e,s)&&(n.flags&128)===0)return se=!1,aS(e,n,s);se=(e.flags&131072)!==0}else se=!1,kt&&(n.flags&1048576)!==0&&Vd(n,tr,n.index);switch(n.lanes=0,n.tag){case 16:t:{e=n.pendingProps;var u=n.elementType,f=u._init;if(u=f(u._payload),n.type=u,typeof u=="function")Qu(u)?(e=Wn(u,e),n.tag=1,n=ap(null,n,u,e,s)):(n.tag=0,n=uh(null,n,u,e,s));else{if(u!=null){if(f=u.$$typeof,f===W){n.tag=11,n=tp(null,n,u,e,s);break t}else if(f===Y){n.tag=14,n=ep(null,n,u,e,s);break t}}throw n=Qt(u)||u,Error(l(306,n,""))}}return n;case 0:return uh(e,n,n.type,n.pendingProps,s);case 1:return u=n.type,f=Wn(u,n.pendingProps),ap(e,n,u,f,s);case 3:t:{if(Vt(n,n.stateNode.containerInfo),e===null)throw Error(l(387));u=n.pendingProps;var g=n.memoizedState;f=g.element,Xu(e,n),Ds(n,u,null,s);var y=n.memoizedState;if(u=y.cache,nn(n,te,u),u!==g.cache&&Du(n,[te],s,!0),Cs(),u=y.element,g.isDehydrated)if(g={element:u,isDehydrated:!1,cache:y.cache},n.updateQueue.baseState=g,n.memoizedState=g,n.flags&256){n=rp(e,n,u,s);break t}else if(u!==f){f=We(Error(l(424)),n),Qs(f),n=rp(e,n,u,s);break t}else{switch(e=n.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(Lt=si(e.firstChild),xe=n,kt=!0,Vn=null,ui=!0,s=jg(n,null,u,s),n.child=s;s;)s.flags=s.flags&-3|4096,s=s.sibling}else{if(ws(),u===f){n=qi(e,n,s);break t}ue(e,n,u,s)}n=n.child}return n;case 26:return vr(e,n),e===null?(s=pm(n.type,null,n.pendingProps,null))?n.memoizedState=s:kt||(s=n.type,e=n.pendingProps,u=_r(at.current).createElement(s),u[Oe]=n,u[Qe]=e,fe(u,s,e),le(u),n.stateNode=u):n.memoizedState=pm(n.type,e.memoizedProps,n.pendingProps,e.memoizedState),null;case 27:return Go(n),e===null&&kt&&(u=n.stateNode=cm(n.type,n.pendingProps,at.current),xe=n,ui=!0,f=Lt,yn(n.type)?(Kh=f,Lt=si(u.firstChild)):Lt=f),ue(e,n,n.pendingProps.children,s),vr(e,n),e===null&&(n.flags|=4194304),n.child;case 5:return e===null&&kt&&((f=u=Lt)&&(u=qS(u,n.type,n.pendingProps,ui),u!==null?(n.stateNode=u,xe=n,Lt=si(u.firstChild),ui=!1,f=!0):f=!1),f||Hn(n)),Go(n),f=n.type,g=n.pendingProps,y=e!==null?e.memoizedProps:null,u=g.children,$h(f,g)?u=null:y!==null&&$h(f,y)&&(n.flags|=32),n.memoizedState!==null&&(f=Gu(e,n,Fy,null,null,s),ea._currentValue=f),vr(e,n),ue(e,n,u,s),n.child;case 6:return e===null&&kt&&((e=s=Lt)&&(s=_S(s,n.pendingProps,ui),s!==null?(n.stateNode=s,xe=n,Lt=null,e=!0):e=!1),e||Hn(n)),null;case 13:return op(e,n,s);case 4:return Vt(n,n.stateNode.containerInfo),u=n.pendingProps,e===null?n.child=Dl(n,null,u,s):ue(e,n,u,s),n.child;case 11:return tp(e,n,n.type,n.pendingProps,s);case 7:return ue(e,n,n.pendingProps,s),n.child;case 8:return ue(e,n,n.pendingProps.children,s),n.child;case 12:return ue(e,n,n.pendingProps.children,s),n.child;case 10:return u=n.pendingProps,nn(n,n.type,u.value),ue(e,n,u.children,s),n.child;case 9:return f=n.type._context,u=n.pendingProps.children,Ln(n),f=ye(f),u=u(f),n.flags|=1,ue(e,n,u,s),n.child;case 14:return ep(e,n,n.type,n.pendingProps,s);case 15:return ip(e,n,n.type,n.pendingProps,s);case 19:return hp(e,n,s);case 31:return u=n.pendingProps,s=n.mode,u={mode:u.mode,children:u.children},e===null?(s=br(u,s),s.ref=n.ref,n.child=s,s.return=n,n=s):(s=Ai(e.child,u),s.ref=n.ref,n.child=s,s.return=n,n=s),n;case 22:return np(e,n,s);case 24:return Ln(n),u=ye(te),e===null?(f=Nu(),f===null&&(f=Bt,g=qu(),f.pooledCache=g,g.refCount++,g!==null&&(f.pooledCacheLanes|=s),f=g),n.memoizedState={parent:u,cache:f},Bu(n),nn(n,te,f)):((e.lanes&s)!==0&&(Xu(e,n),Ds(n,null,null,s),Cs()),f=e.memoizedState,g=n.memoizedState,f.parent!==u?(f={parent:u,cache:u},n.memoizedState=f,n.lanes===0&&(n.memoizedState=n.updateQueue.baseState=f),nn(n,te,u)):(u=g.cache,nn(n,te,u),u!==f.cache&&Du(n,[te],s,!0))),ue(e,n,n.pendingProps.children,s),n.child;case 29:throw n.pendingProps}throw Error(l(156,n.tag))}function _i(e){e.flags|=4}function cp(e,n){if(n.type!=="stylesheet"||(n.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!vm(n)){if(n=Je.current,n!==null&&((bt&4194048)===bt?hi!==null:(bt&62914560)!==bt&&(bt&536870912)===0||n!==hi))throw zs=Uu,Zd;e.flags|=8192}}function xr(e,n){n!==null&&(e.flags|=4),e.flags&16384&&(n=e.tag!==22?Lc():536870912,e.lanes|=n,Ul|=n)}function Ps(e,n){if(!kt)switch(e.tailMode){case"hidden":n=e.tail;for(var s=null;n!==null;)n.alternate!==null&&(s=n),n=n.sibling;s===null?e.tail=null:s.sibling=null;break;case"collapsed":s=e.tail;for(var u=null;s!==null;)s.alternate!==null&&(u=s),s=s.sibling;u===null?n||e.tail===null?e.tail=null:e.tail.sibling=null:u.sibling=null}}function jt(e){var n=e.alternate!==null&&e.alternate.child===e.child,s=0,u=0;if(n)for(var f=e.child;f!==null;)s|=f.lanes|f.childLanes,u|=f.subtreeFlags&65011712,u|=f.flags&65011712,f.return=e,f=f.sibling;else for(f=e.child;f!==null;)s|=f.lanes|f.childLanes,u|=f.subtreeFlags,u|=f.flags,f.return=e,f=f.sibling;return e.subtreeFlags|=u,e.childLanes=s,n}function rS(e,n,s){var u=n.pendingProps;switch(Eu(n),n.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return jt(n),null;case 1:return jt(n),null;case 3:return s=n.stateNode,u=null,e!==null&&(u=e.memoizedState.cache),n.memoizedState.cache!==u&&(n.flags|=2048),Mi(te),Fi(),s.pendingContext&&(s.context=s.pendingContext,s.pendingContext=null),(e===null||e.child===null)&&(Ts(n)?_i(n):e===null||e.memoizedState.isDehydrated&&(n.flags&256)===0||(n.flags|=1024,Ld())),jt(n),null;case 26:return s=n.memoizedState,e===null?(_i(n),s!==null?(jt(n),cp(n,s)):(jt(n),n.flags&=-16777217)):s?s!==e.memoizedState?(_i(n),jt(n),cp(n,s)):(jt(n),n.flags&=-16777217):(e.memoizedProps!==u&&_i(n),jt(n),n.flags&=-16777217),null;case 27:Da(n),s=at.current;var f=n.type;if(e!==null&&n.stateNode!=null)e.memoizedProps!==u&&_i(n);else{if(!u){if(n.stateNode===null)throw Error(l(166));return jt(n),null}e=tt.current,Ts(n)?Hd(n):(e=cm(f,u,s),n.stateNode=e,_i(n))}return jt(n),null;case 5:if(Da(n),s=n.type,e!==null&&n.stateNode!=null)e.memoizedProps!==u&&_i(n);else{if(!u){if(n.stateNode===null)throw Error(l(166));return jt(n),null}if(e=tt.current,Ts(n))Hd(n);else{switch(f=_r(at.current),e){case 1:e=f.createElementNS("http://www.w3.org/2000/svg",s);break;case 2:e=f.createElementNS("http://www.w3.org/1998/Math/MathML",s);break;default:switch(s){case"svg":e=f.createElementNS("http://www.w3.org/2000/svg",s);break;case"math":e=f.createElementNS("http://www.w3.org/1998/Math/MathML",s);break;case"script":e=f.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof u.is=="string"?f.createElement("select",{is:u.is}):f.createElement("select"),u.multiple?e.multiple=!0:u.size&&(e.size=u.size);break;default:e=typeof u.is=="string"?f.createElement(s,{is:u.is}):f.createElement(s)}}e[Oe]=n,e[Qe]=u;t:for(f=n.child;f!==null;){if(f.tag===5||f.tag===6)e.appendChild(f.stateNode);else if(f.tag!==4&&f.tag!==27&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===n)break t;for(;f.sibling===null;){if(f.return===null||f.return===n)break t;f=f.return}f.sibling.return=f.return,f=f.sibling}n.stateNode=e;t:switch(fe(e,s,u),s){case"button":case"input":case"select":case"textarea":e=!!u.autoFocus;break t;case"img":e=!0;break t;default:e=!1}e&&_i(n)}}return jt(n),n.flags&=-16777217,null;case 6:if(e&&n.stateNode!=null)e.memoizedProps!==u&&_i(n);else{if(typeof u!="string"&&n.stateNode===null)throw Error(l(166));if(e=at.current,Ts(n)){if(e=n.stateNode,s=n.memoizedProps,u=null,f=xe,f!==null)switch(f.tag){case 27:case 5:u=f.memoizedProps}e[Oe]=n,e=!!(e.nodeValue===s||u!==null&&u.suppressHydrationWarning===!0||sm(e.nodeValue,s)),e||Hn(n)}else e=_r(e).createTextNode(u),e[Oe]=n,n.stateNode=e}return jt(n),null;case 13:if(u=n.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(f=Ts(n),u!==null&&u.dehydrated!==null){if(e===null){if(!f)throw Error(l(318));if(f=n.memoizedState,f=f!==null?f.dehydrated:null,!f)throw Error(l(317));f[Oe]=n}else ws(),(n.flags&128)===0&&(n.memoizedState=null),n.flags|=4;jt(n),f=!1}else f=Ld(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=f),f=!0;if(!f)return n.flags&256?(Di(n),n):(Di(n),null)}if(Di(n),(n.flags&128)!==0)return n.lanes=s,n;if(s=u!==null,e=e!==null&&e.memoizedState!==null,s){u=n.child,f=null,u.alternate!==null&&u.alternate.memoizedState!==null&&u.alternate.memoizedState.cachePool!==null&&(f=u.alternate.memoizedState.cachePool.pool);var g=null;u.memoizedState!==null&&u.memoizedState.cachePool!==null&&(g=u.memoizedState.cachePool.pool),g!==f&&(u.flags|=2048)}return s!==e&&s&&(n.child.flags|=8192),xr(n,n.updateQueue),jt(n),null;case 4:return Fi(),e===null&&Vh(n.stateNode.containerInfo),jt(n),null;case 10:return Mi(n.type),jt(n),null;case 19:if(V(ee),f=n.memoizedState,f===null)return jt(n),null;if(u=(n.flags&128)!==0,g=f.rendering,g===null)if(u)Ps(f,!1);else{if(Gt!==0||e!==null&&(e.flags&128)!==0)for(e=n.child;e!==null;){if(g=Or(e),g!==null){for(n.flags|=128,Ps(f,!1),e=g.updateQueue,n.updateQueue=e,xr(n,e),n.subtreeFlags=0,e=s,s=n.child;s!==null;)Pd(s,e),s=s.sibling;return P(ee,ee.current&1|2),n.child}e=e.sibling}f.tail!==null&&oi()>Qr&&(n.flags|=128,u=!0,Ps(f,!1),n.lanes=4194304)}else{if(!u)if(e=Or(g),e!==null){if(n.flags|=128,u=!0,e=e.updateQueue,n.updateQueue=e,xr(n,e),Ps(f,!0),f.tail===null&&f.tailMode==="hidden"&&!g.alternate&&!kt)return jt(n),null}else 2*oi()-f.renderingStartTime>Qr&&s!==536870912&&(n.flags|=128,u=!0,Ps(f,!1),n.lanes=4194304);f.isBackwards?(g.sibling=n.child,n.child=g):(e=f.last,e!==null?e.sibling=g:n.child=g,f.last=g)}return f.tail!==null?(n=f.tail,f.rendering=n,f.tail=n.sibling,f.renderingStartTime=oi(),n.sibling=null,e=ee.current,P(ee,u?e&1|2:e&1),n):(jt(n),null);case 22:case 23:return Di(n),ju(),u=n.memoizedState!==null,e!==null?e.memoizedState!==null!==u&&(n.flags|=8192):u&&(n.flags|=8192),u?(s&536870912)!==0&&(n.flags&128)===0&&(jt(n),n.subtreeFlags&6&&(n.flags|=8192)):jt(n),s=n.updateQueue,s!==null&&xr(n,s.retryQueue),s=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(s=e.memoizedState.cachePool.pool),u=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(u=n.memoizedState.cachePool.pool),u!==s&&(n.flags|=2048),e!==null&&V(Gn),null;case 24:return s=null,e!==null&&(s=e.memoizedState.cache),n.memoizedState.cache!==s&&(n.flags|=2048),Mi(te),jt(n),null;case 25:return null;case 30:return null}throw Error(l(156,n.tag))}function oS(e,n){switch(Eu(n),n.tag){case 1:return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 3:return Mi(te),Fi(),e=n.flags,(e&65536)!==0&&(e&128)===0?(n.flags=e&-65537|128,n):null;case 26:case 27:case 5:return Da(n),null;case 13:if(Di(n),e=n.memoizedState,e!==null&&e.dehydrated!==null){if(n.alternate===null)throw Error(l(340));ws()}return e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 19:return V(ee),null;case 4:return Fi(),null;case 10:return Mi(n.type),null;case 22:case 23:return Di(n),ju(),e!==null&&V(Gn),e=n.flags,e&65536?(n.flags=e&-65537|128,n):null;case 24:return Mi(te),null;case 25:return null;default:return null}}function dp(e,n){switch(Eu(n),n.tag){case 3:Mi(te),Fi();break;case 26:case 27:case 5:Da(n);break;case 4:Fi();break;case 13:Di(n);break;case 19:V(ee);break;case 10:Mi(n.type);break;case 22:case 23:Di(n),ju(),e!==null&&V(Gn);break;case 24:Mi(te)}}function Vs(e,n){try{var s=n.updateQueue,u=s!==null?s.lastEffect:null;if(u!==null){var f=u.next;s=f;do{if((s.tag&e)===e){u=void 0;var g=s.create,y=s.inst;u=g(),y.destroy=u}s=s.next}while(s!==f)}}catch(v){_t(n,n.return,v)}}function hn(e,n,s){try{var u=n.updateQueue,f=u!==null?u.lastEffect:null;if(f!==null){var g=f.next;u=g;do{if((u.tag&e)===e){var y=u.inst,v=y.destroy;if(v!==void 0){y.destroy=void 0,f=n;var x=s,R=v;try{R()}catch(_){_t(f,x,_)}}}u=u.next}while(u!==g)}}catch(_){_t(n,n.return,_)}}function gp(e){var n=e.updateQueue;if(n!==null){var s=e.stateNode;try{eg(n,s)}catch(u){_t(e,e.return,u)}}}function pp(e,n,s){s.props=Wn(e.type,e.memoizedProps),s.state=e.memoizedState;try{s.componentWillUnmount()}catch(u){_t(e,n,u)}}function Hs(e,n){try{var s=e.ref;if(s!==null){switch(e.tag){case 26:case 27:case 5:var u=e.stateNode;break;case 30:u=e.stateNode;break;default:u=e.stateNode}typeof s=="function"?e.refCleanup=s(u):s.current=u}}catch(f){_t(e,n,f)}}function fi(e,n){var s=e.ref,u=e.refCleanup;if(s!==null)if(typeof u=="function")try{u()}catch(f){_t(e,n,f)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof s=="function")try{s(null)}catch(f){_t(e,n,f)}else s.current=null}function mp(e){var n=e.type,s=e.memoizedProps,u=e.stateNode;try{t:switch(n){case"button":case"input":case"select":case"textarea":s.autoFocus&&u.focus();break t;case"img":s.src?u.src=s.src:s.srcSet&&(u.srcset=s.srcSet)}}catch(f){_t(e,e.return,f)}}function Oh(e,n,s){try{var u=e.stateNode;ES(u,e.type,s,n),u[Qe]=n}catch(f){_t(e,e.return,f)}}function Op(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&yn(e.type)||e.tag===4}function yh(e){t:for(;;){for(;e.sibling===null;){if(e.return===null||Op(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&yn(e.type)||e.flags&2||e.child===null||e.tag===4)continue t;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Sh(e,n,s){var u=e.tag;if(u===5||u===6)e=e.stateNode,n?(s.nodeType===9?s.body:s.nodeName==="HTML"?s.ownerDocument.body:s).insertBefore(e,n):(n=s.nodeType===9?s.body:s.nodeName==="HTML"?s.ownerDocument.body:s,n.appendChild(e),s=s._reactRootContainer,s!=null||n.onclick!==null||(n.onclick=qr));else if(u!==4&&(u===27&&yn(e.type)&&(s=e.stateNode,n=null),e=e.child,e!==null))for(Sh(e,n,s),e=e.sibling;e!==null;)Sh(e,n,s),e=e.sibling}function Tr(e,n,s){var u=e.tag;if(u===5||u===6)e=e.stateNode,n?s.insertBefore(e,n):s.appendChild(e);else if(u!==4&&(u===27&&yn(e.type)&&(s=e.stateNode),e=e.child,e!==null))for(Tr(e,n,s),e=e.sibling;e!==null;)Tr(e,n,s),e=e.sibling}function yp(e){var n=e.stateNode,s=e.memoizedProps;try{for(var u=e.type,f=n.attributes;f.length;)n.removeAttributeNode(f[0]);fe(n,u,s),n[Oe]=e,n[Qe]=s}catch(g){_t(e,e.return,g)}}var Ni=!1,Yt=!1,vh=!1,Sp=typeof WeakSet=="function"?WeakSet:Set,ae=null;function uS(e,n){if(e=e.containerInfo,Lh=Vr,e=zd(e),yu(e)){if("selectionStart"in e)var s={start:e.selectionStart,end:e.selectionEnd};else t:{s=(s=e.ownerDocument)&&s.defaultView||window;var u=s.getSelection&&s.getSelection();if(u&&u.rangeCount!==0){s=u.anchorNode;var f=u.anchorOffset,g=u.focusNode;u=u.focusOffset;try{s.nodeType,g.nodeType}catch{s=null;break t}var y=0,v=-1,x=-1,R=0,_=0,B=e,E=null;e:for(;;){for(var M;B!==s||f!==0&&B.nodeType!==3||(v=y+f),B!==g||u!==0&&B.nodeType!==3||(x=y+u),B.nodeType===3&&(y+=B.nodeValue.length),(M=B.firstChild)!==null;)E=B,B=M;for(;;){if(B===e)break e;if(E===s&&++R===f&&(v=y),E===g&&++_===u&&(x=y),(M=B.nextSibling)!==null)break;B=E,E=B.parentNode}B=M}s=v===-1||x===-1?null:{start:v,end:x}}else s=null}s=s||{start:0,end:0}}else s=null;for(Gh={focusedElem:e,selectionRange:s},Vr=!1,ae=n;ae!==null;)if(n=ae,e=n.child,(n.subtreeFlags&1024)!==0&&e!==null)e.return=n,ae=e;else for(;ae!==null;){switch(n=ae,g=n.alternate,e=n.flags,n.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&g!==null){e=void 0,s=n,f=g.memoizedProps,g=g.memoizedState,u=s.stateNode;try{var rt=Wn(s.type,f,s.elementType===s.type);e=u.getSnapshotBeforeUpdate(rt,g),u.__reactInternalSnapshotBeforeUpdate=e}catch(nt){_t(s,s.return,nt)}}break;case 3:if((e&1024)!==0){if(e=n.stateNode.containerInfo,s=e.nodeType,s===9)Yh(e);else if(s===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Yh(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(l(163))}if(e=n.sibling,e!==null){e.return=n.return,ae=e;break}ae=n.return}}function vp(e,n,s){var u=s.flags;switch(s.tag){case 0:case 11:case 15:fn(e,s),u&4&&Vs(5,s);break;case 1:if(fn(e,s),u&4)if(e=s.stateNode,n===null)try{e.componentDidMount()}catch(y){_t(s,s.return,y)}else{var f=Wn(s.type,n.memoizedProps);n=n.memoizedState;try{e.componentDidUpdate(f,n,e.__reactInternalSnapshotBeforeUpdate)}catch(y){_t(s,s.return,y)}}u&64&&gp(s),u&512&&Hs(s,s.return);break;case 3:if(fn(e,s),u&64&&(e=s.updateQueue,e!==null)){if(n=null,s.child!==null)switch(s.child.tag){case 27:case 5:n=s.child.stateNode;break;case 1:n=s.child.stateNode}try{eg(e,n)}catch(y){_t(s,s.return,y)}}break;case 27:n===null&&u&4&&yp(s);case 26:case 5:fn(e,s),n===null&&u&4&&mp(s),u&512&&Hs(s,s.return);break;case 12:fn(e,s);break;case 13:fn(e,s),u&4&&Tp(e,s),u&64&&(e=s.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(s=yS.bind(null,s),NS(e,s))));break;case 22:if(u=s.memoizedState!==null||Ni,!u){n=n!==null&&n.memoizedState!==null||Yt,f=Ni;var g=Yt;Ni=u,(Yt=n)&&!g?cn(e,s,(s.subtreeFlags&8772)!==0):fn(e,s),Ni=f,Yt=g}break;case 30:break;default:fn(e,s)}}function bp(e){var n=e.alternate;n!==null&&(e.alternate=null,bp(n)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(n=e.stateNode,n!==null&&Io(n)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Ht=null,Re=!1;function Ui(e,n,s){for(s=s.child;s!==null;)xp(e,n,s),s=s.sibling}function xp(e,n,s){if(De&&typeof De.onCommitFiberUnmount=="function")try{De.onCommitFiberUnmount(us,s)}catch{}switch(s.tag){case 26:Yt||fi(s,n),Ui(e,n,s),s.memoizedState?s.memoizedState.count--:s.stateNode&&(s=s.stateNode,s.parentNode.removeChild(s));break;case 27:Yt||fi(s,n);var u=Ht,f=Re;yn(s.type)&&(Ht=s.stateNode,Re=!1),Ui(e,n,s),Js(s.stateNode),Ht=u,Re=f;break;case 5:Yt||fi(s,n);case 6:if(u=Ht,f=Re,Ht=null,Ui(e,n,s),Ht=u,Re=f,Ht!==null)if(Re)try{(Ht.nodeType===9?Ht.body:Ht.nodeName==="HTML"?Ht.ownerDocument.body:Ht).removeChild(s.stateNode)}catch(g){_t(s,n,g)}else try{Ht.removeChild(s.stateNode)}catch(g){_t(s,n,g)}break;case 18:Ht!==null&&(Re?(e=Ht,hm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,s.stateNode),sa(e)):hm(Ht,s.stateNode));break;case 4:u=Ht,f=Re,Ht=s.stateNode.containerInfo,Re=!0,Ui(e,n,s),Ht=u,Re=f;break;case 0:case 11:case 14:case 15:Yt||hn(2,s,n),Yt||hn(4,s,n),Ui(e,n,s);break;case 1:Yt||(fi(s,n),u=s.stateNode,typeof u.componentWillUnmount=="function"&&pp(s,n,u)),Ui(e,n,s);break;case 21:Ui(e,n,s);break;case 22:Yt=(u=Yt)||s.memoizedState!==null,Ui(e,n,s),Yt=u;break;default:Ui(e,n,s)}}function Tp(e,n){if(n.memoizedState===null&&(e=n.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{sa(e)}catch(s){_t(n,n.return,s)}}function hS(e){switch(e.tag){case 13:case 19:var n=e.stateNode;return n===null&&(n=e.stateNode=new Sp),n;case 22:return e=e.stateNode,n=e._retryCache,n===null&&(n=e._retryCache=new Sp),n;default:throw Error(l(435,e.tag))}}function bh(e,n){var s=hS(e);n.forEach(function(u){var f=SS.bind(null,e,u);s.has(u)||(s.add(u),u.then(f,f))})}function Ue(e,n){var s=n.deletions;if(s!==null)for(var u=0;u<s.length;u++){var f=s[u],g=e,y=n,v=y;t:for(;v!==null;){switch(v.tag){case 27:if(yn(v.type)){Ht=v.stateNode,Re=!1;break t}break;case 5:Ht=v.stateNode,Re=!1;break t;case 3:case 4:Ht=v.stateNode.containerInfo,Re=!0;break t}v=v.return}if(Ht===null)throw Error(l(160));xp(g,y,f),Ht=null,Re=!1,g=f.alternate,g!==null&&(g.return=null),f.return=null}if(n.subtreeFlags&13878)for(n=n.child;n!==null;)wp(n,e),n=n.sibling}var li=null;function wp(e,n){var s=e.alternate,u=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ue(n,e),Be(e),u&4&&(hn(3,e,e.return),Vs(3,e),hn(5,e,e.return));break;case 1:Ue(n,e),Be(e),u&512&&(Yt||s===null||fi(s,s.return)),u&64&&Ni&&(e=e.updateQueue,e!==null&&(u=e.callbacks,u!==null&&(s=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=s===null?u:s.concat(u))));break;case 26:var f=li;if(Ue(n,e),Be(e),u&512&&(Yt||s===null||fi(s,s.return)),u&4){var g=s!==null?s.memoizedState:null;if(u=e.memoizedState,s===null)if(u===null)if(e.stateNode===null){t:{u=e.type,s=e.memoizedProps,f=f.ownerDocument||f;e:switch(u){case"title":g=f.getElementsByTagName("title")[0],(!g||g[cs]||g[Oe]||g.namespaceURI==="http://www.w3.org/2000/svg"||g.hasAttribute("itemprop"))&&(g=f.createElement(u),f.head.insertBefore(g,f.querySelector("head > title"))),fe(g,u,s),g[Oe]=e,le(g),u=g;break t;case"link":var y=ym("link","href",f).get(u+(s.href||""));if(y){for(var v=0;v<y.length;v++)if(g=y[v],g.getAttribute("href")===(s.href==null||s.href===""?null:s.href)&&g.getAttribute("rel")===(s.rel==null?null:s.rel)&&g.getAttribute("title")===(s.title==null?null:s.title)&&g.getAttribute("crossorigin")===(s.crossOrigin==null?null:s.crossOrigin)){y.splice(v,1);break e}}g=f.createElement(u),fe(g,u,s),f.head.appendChild(g);break;case"meta":if(y=ym("meta","content",f).get(u+(s.content||""))){for(v=0;v<y.length;v++)if(g=y[v],g.getAttribute("content")===(s.content==null?null:""+s.content)&&g.getAttribute("name")===(s.name==null?null:s.name)&&g.getAttribute("property")===(s.property==null?null:s.property)&&g.getAttribute("http-equiv")===(s.httpEquiv==null?null:s.httpEquiv)&&g.getAttribute("charset")===(s.charSet==null?null:s.charSet)){y.splice(v,1);break e}}g=f.createElement(u),fe(g,u,s),f.head.appendChild(g);break;default:throw Error(l(468,u))}g[Oe]=e,le(g),u=g}e.stateNode=u}else Sm(f,e.type,e.stateNode);else e.stateNode=Om(f,u,e.memoizedProps);else g!==u?(g===null?s.stateNode!==null&&(s=s.stateNode,s.parentNode.removeChild(s)):g.count--,u===null?Sm(f,e.type,e.stateNode):Om(f,u,e.memoizedProps)):u===null&&e.stateNode!==null&&Oh(e,e.memoizedProps,s.memoizedProps)}break;case 27:Ue(n,e),Be(e),u&512&&(Yt||s===null||fi(s,s.return)),s!==null&&u&4&&Oh(e,e.memoizedProps,s.memoizedProps);break;case 5:if(Ue(n,e),Be(e),u&512&&(Yt||s===null||fi(s,s.return)),e.flags&32){f=e.stateNode;try{pl(f,"")}catch(M){_t(e,e.return,M)}}u&4&&e.stateNode!=null&&(f=e.memoizedProps,Oh(e,f,s!==null?s.memoizedProps:f)),u&1024&&(vh=!0);break;case 6:if(Ue(n,e),Be(e),u&4){if(e.stateNode===null)throw Error(l(162));u=e.memoizedProps,s=e.stateNode;try{s.nodeValue=u}catch(M){_t(e,e.return,M)}}break;case 3:if(Br=null,f=li,li=Nr(n.containerInfo),Ue(n,e),li=f,Be(e),u&4&&s!==null&&s.memoizedState.isDehydrated)try{sa(n.containerInfo)}catch(M){_t(e,e.return,M)}vh&&(vh=!1,Qp(e));break;case 4:u=li,li=Nr(e.stateNode.containerInfo),Ue(n,e),Be(e),li=u;break;case 12:Ue(n,e),Be(e);break;case 13:Ue(n,e),Be(e),e.child.flags&8192&&e.memoizedState!==null!=(s!==null&&s.memoizedState!==null)&&(Ah=oi()),u&4&&(u=e.updateQueue,u!==null&&(e.updateQueue=null,bh(e,u)));break;case 22:f=e.memoizedState!==null;var x=s!==null&&s.memoizedState!==null,R=Ni,_=Yt;if(Ni=R||f,Yt=_||x,Ue(n,e),Yt=_,Ni=R,Be(e),u&8192)t:for(n=e.stateNode,n._visibility=f?n._visibility&-2:n._visibility|1,f&&(s===null||x||Ni||Yt||Yn(e)),s=null,n=e;;){if(n.tag===5||n.tag===26){if(s===null){x=s=n;try{if(g=x.stateNode,f)y=g.style,typeof y.setProperty=="function"?y.setProperty("display","none","important"):y.display="none";else{v=x.stateNode;var B=x.memoizedProps.style,E=B!=null&&B.hasOwnProperty("display")?B.display:null;v.style.display=E==null||typeof E=="boolean"?"":(""+E).trim()}}catch(M){_t(x,x.return,M)}}}else if(n.tag===6){if(s===null){x=n;try{x.stateNode.nodeValue=f?"":x.memoizedProps}catch(M){_t(x,x.return,M)}}}else if((n.tag!==22&&n.tag!==23||n.memoizedState===null||n===e)&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;s===n&&(s=null),n=n.return}s===n&&(s=null),n.sibling.return=n.return,n=n.sibling}u&4&&(u=e.updateQueue,u!==null&&(s=u.retryQueue,s!==null&&(u.retryQueue=null,bh(e,s))));break;case 19:Ue(n,e),Be(e),u&4&&(u=e.updateQueue,u!==null&&(e.updateQueue=null,bh(e,u)));break;case 30:break;case 21:break;default:Ue(n,e),Be(e)}}function Be(e){var n=e.flags;if(n&2){try{for(var s,u=e.return;u!==null;){if(Op(u)){s=u;break}u=u.return}if(s==null)throw Error(l(160));switch(s.tag){case 27:var f=s.stateNode,g=yh(e);Tr(e,g,f);break;case 5:var y=s.stateNode;s.flags&32&&(pl(y,""),s.flags&=-33);var v=yh(e);Tr(e,v,y);break;case 3:case 4:var x=s.stateNode.containerInfo,R=yh(e);Sh(e,R,x);break;default:throw Error(l(161))}}catch(_){_t(e,e.return,_)}e.flags&=-3}n&4096&&(e.flags&=-4097)}function Qp(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var n=e;Qp(n),n.tag===5&&n.flags&1024&&n.stateNode.reset(),e=e.sibling}}function fn(e,n){if(n.subtreeFlags&8772)for(n=n.child;n!==null;)vp(e,n.alternate,n),n=n.sibling}function Yn(e){for(e=e.child;e!==null;){var n=e;switch(n.tag){case 0:case 11:case 14:case 15:hn(4,n,n.return),Yn(n);break;case 1:fi(n,n.return);var s=n.stateNode;typeof s.componentWillUnmount=="function"&&pp(n,n.return,s),Yn(n);break;case 27:Js(n.stateNode);case 26:case 5:fi(n,n.return),Yn(n);break;case 22:n.memoizedState===null&&Yn(n);break;case 30:Yn(n);break;default:Yn(n)}e=e.sibling}}function cn(e,n,s){for(s=s&&(n.subtreeFlags&8772)!==0,n=n.child;n!==null;){var u=n.alternate,f=e,g=n,y=g.flags;switch(g.tag){case 0:case 11:case 15:cn(f,g,s),Vs(4,g);break;case 1:if(cn(f,g,s),u=g,f=u.stateNode,typeof f.componentDidMount=="function")try{f.componentDidMount()}catch(R){_t(u,u.return,R)}if(u=g,f=u.updateQueue,f!==null){var v=u.stateNode;try{var x=f.shared.hiddenCallbacks;if(x!==null)for(f.shared.hiddenCallbacks=null,f=0;f<x.length;f++)tg(x[f],v)}catch(R){_t(u,u.return,R)}}s&&y&64&&gp(g),Hs(g,g.return);break;case 27:yp(g);case 26:case 5:cn(f,g,s),s&&u===null&&y&4&&mp(g),Hs(g,g.return);break;case 12:cn(f,g,s);break;case 13:cn(f,g,s),s&&y&4&&Tp(f,g);break;case 22:g.memoizedState===null&&cn(f,g,s),Hs(g,g.return);break;case 30:break;default:cn(f,g,s)}n=n.sibling}}function xh(e,n){var s=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(s=e.memoizedState.cachePool.pool),e=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(e=n.memoizedState.cachePool.pool),e!==s&&(e!=null&&e.refCount++,s!=null&&As(s))}function Th(e,n){e=null,n.alternate!==null&&(e=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==e&&(n.refCount++,e!=null&&As(e))}function ci(e,n,s,u){if(n.subtreeFlags&10256)for(n=n.child;n!==null;)kp(e,n,s,u),n=n.sibling}function kp(e,n,s,u){var f=n.flags;switch(n.tag){case 0:case 11:case 15:ci(e,n,s,u),f&2048&&Vs(9,n);break;case 1:ci(e,n,s,u);break;case 3:ci(e,n,s,u),f&2048&&(e=null,n.alternate!==null&&(e=n.alternate.memoizedState.cache),n=n.memoizedState.cache,n!==e&&(n.refCount++,e!=null&&As(e)));break;case 12:if(f&2048){ci(e,n,s,u),e=n.stateNode;try{var g=n.memoizedProps,y=g.id,v=g.onPostCommit;typeof v=="function"&&v(y,n.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(x){_t(n,n.return,x)}}else ci(e,n,s,u);break;case 13:ci(e,n,s,u);break;case 23:break;case 22:g=n.stateNode,y=n.alternate,n.memoizedState!==null?g._visibility&2?ci(e,n,s,u):js(e,n):g._visibility&2?ci(e,n,s,u):(g._visibility|=2,ql(e,n,s,u,(n.subtreeFlags&10256)!==0)),f&2048&&xh(y,n);break;case 24:ci(e,n,s,u),f&2048&&Th(n.alternate,n);break;default:ci(e,n,s,u)}}function ql(e,n,s,u,f){for(f=f&&(n.subtreeFlags&10256)!==0,n=n.child;n!==null;){var g=e,y=n,v=s,x=u,R=y.flags;switch(y.tag){case 0:case 11:case 15:ql(g,y,v,x,f),Vs(8,y);break;case 23:break;case 22:var _=y.stateNode;y.memoizedState!==null?_._visibility&2?ql(g,y,v,x,f):js(g,y):(_._visibility|=2,ql(g,y,v,x,f)),f&&R&2048&&xh(y.alternate,y);break;case 24:ql(g,y,v,x,f),f&&R&2048&&Th(y.alternate,y);break;default:ql(g,y,v,x,f)}n=n.sibling}}function js(e,n){if(n.subtreeFlags&10256)for(n=n.child;n!==null;){var s=e,u=n,f=u.flags;switch(u.tag){case 22:js(s,u),f&2048&&xh(u.alternate,u);break;case 24:js(s,u),f&2048&&Th(u.alternate,u);break;default:js(s,u)}n=n.sibling}}var Ls=8192;function _l(e){if(e.subtreeFlags&Ls)for(e=e.child;e!==null;)Ap(e),e=e.sibling}function Ap(e){switch(e.tag){case 26:_l(e),e.flags&Ls&&e.memoizedState!==null&&ZS(li,e.memoizedState,e.memoizedProps);break;case 5:_l(e);break;case 3:case 4:var n=li;li=Nr(e.stateNode.containerInfo),_l(e),li=n;break;case 22:e.memoizedState===null&&(n=e.alternate,n!==null&&n.memoizedState!==null?(n=Ls,Ls=16777216,_l(e),Ls=n):_l(e));break;default:_l(e)}}function Rp(e){var n=e.alternate;if(n!==null&&(e=n.child,e!==null)){n.child=null;do n=e.sibling,e.sibling=null,e=n;while(e!==null)}}function Gs(e){var n=e.deletions;if((e.flags&16)!==0){if(n!==null)for(var s=0;s<n.length;s++){var u=n[s];ae=u,zp(u,e)}Rp(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Ep(e),e=e.sibling}function Ep(e){switch(e.tag){case 0:case 11:case 15:Gs(e),e.flags&2048&&hn(9,e,e.return);break;case 3:Gs(e);break;case 12:Gs(e);break;case 22:var n=e.stateNode;e.memoizedState!==null&&n._visibility&2&&(e.return===null||e.return.tag!==13)?(n._visibility&=-3,wr(e)):Gs(e);break;default:Gs(e)}}function wr(e){var n=e.deletions;if((e.flags&16)!==0){if(n!==null)for(var s=0;s<n.length;s++){var u=n[s];ae=u,zp(u,e)}Rp(e)}for(e=e.child;e!==null;){switch(n=e,n.tag){case 0:case 11:case 15:hn(8,n,n.return),wr(n);break;case 22:s=n.stateNode,s._visibility&2&&(s._visibility&=-3,wr(n));break;default:wr(n)}e=e.sibling}}function zp(e,n){for(;ae!==null;){var s=ae;switch(s.tag){case 0:case 11:case 15:hn(8,s,n);break;case 23:case 22:if(s.memoizedState!==null&&s.memoizedState.cachePool!==null){var u=s.memoizedState.cachePool.pool;u!=null&&u.refCount++}break;case 24:As(s.memoizedState.cache)}if(u=s.child,u!==null)u.return=s,ae=u;else t:for(s=e;ae!==null;){u=ae;var f=u.sibling,g=u.return;if(bp(u),u===s){ae=null;break t}if(f!==null){f.return=g,ae=f;break t}ae=g}}}var fS={getCacheForType:function(e){var n=ye(te),s=n.data.get(e);return s===void 0&&(s=e(),n.data.set(e,s)),s}},cS=typeof WeakMap=="function"?WeakMap:Map,zt=0,Bt=null,pt=null,bt=0,Mt=0,Xe=null,dn=!1,Nl=!1,wh=!1,Bi=0,Gt=0,gn=0,Zn=0,Qh=0,Fe=0,Ul=0,$s=null,Ee=null,kh=!1,Ah=0,Qr=1/0,kr=null,pn=null,he=0,mn=null,Bl=null,Xl=0,Rh=0,Eh=null,Mp=null,Ws=0,zh=null;function Pe(){if((zt&2)!==0&&bt!==0)return bt&-bt;if(D.T!==null){var e=kl;return e!==0?e:Uh()}return Wc()}function Cp(){Fe===0&&(Fe=(bt&536870912)===0||kt?jc():536870912);var e=Je.current;return e!==null&&(e.flags|=32),Fe}function Ve(e,n,s){(e===Bt&&(Mt===2||Mt===9)||e.cancelPendingCommit!==null)&&(Pl(e,0),On(e,bt,Fe,!1)),fs(e,s),((zt&2)===0||e!==Bt)&&(e===Bt&&((zt&2)===0&&(Zn|=s),Gt===4&&On(e,bt,Fe,!1)),di(e))}function Dp(e,n,s){if((zt&6)!==0)throw Error(l(327));var u=!s&&(n&124)===0&&(n&e.expiredLanes)===0||hs(e,n),f=u?pS(e,n):Dh(e,n,!0),g=u;do{if(f===0){Nl&&!u&&On(e,n,0,!1);break}else{if(s=e.current.alternate,g&&!dS(s)){f=Dh(e,n,!1),g=!1;continue}if(f===2){if(g=n,e.errorRecoveryDisabledLanes&g)var y=0;else y=e.pendingLanes&-536870913,y=y!==0?y:y&536870912?536870912:0;if(y!==0){n=y;t:{var v=e;f=$s;var x=v.current.memoizedState.isDehydrated;if(x&&(Pl(v,y).flags|=256),y=Dh(v,y,!1),y!==2){if(wh&&!x){v.errorRecoveryDisabledLanes|=g,Zn|=g,f=4;break t}g=Ee,Ee=f,g!==null&&(Ee===null?Ee=g:Ee.push.apply(Ee,g))}f=y}if(g=!1,f!==2)continue}}if(f===1){Pl(e,0),On(e,n,0,!0);break}t:{switch(u=e,g=f,g){case 0:case 1:throw Error(l(345));case 4:if((n&4194048)!==n)break;case 6:On(u,n,Fe,!dn);break t;case 2:Ee=null;break;case 3:case 5:break;default:throw Error(l(329))}if((n&62914560)===n&&(f=Ah+300-oi(),10<f)){if(On(u,n,Fe,!dn),Ua(u,0,!0)!==0)break t;u.timeoutHandle=om(qp.bind(null,u,s,Ee,kr,kh,n,Fe,Zn,Ul,dn,g,2,-0,0),f);break t}qp(u,s,Ee,kr,kh,n,Fe,Zn,Ul,dn,g,0,-0,0)}}break}while(!0);di(e)}function qp(e,n,s,u,f,g,y,v,x,R,_,B,E,M){if(e.timeoutHandle=-1,B=n.subtreeFlags,(B&8192||(B&16785408)===16785408)&&(ta={stylesheets:null,count:0,unsuspend:YS},Ap(n),B=KS(),B!==null)){e.cancelPendingCommit=B(Vp.bind(null,e,n,g,s,u,f,y,v,x,_,1,E,M)),On(e,g,y,!R);return}Vp(e,n,g,s,u,f,y,v,x)}function dS(e){for(var n=e;;){var s=n.tag;if((s===0||s===11||s===15)&&n.flags&16384&&(s=n.updateQueue,s!==null&&(s=s.stores,s!==null)))for(var u=0;u<s.length;u++){var f=s[u],g=f.getSnapshot;f=f.value;try{if(!_e(g(),f))return!1}catch{return!1}}if(s=n.child,n.subtreeFlags&16384&&s!==null)s.return=n,n=s;else{if(n===e)break;for(;n.sibling===null;){if(n.return===null||n.return===e)return!0;n=n.return}n.sibling.return=n.return,n=n.sibling}}return!0}function On(e,n,s,u){n&=~Qh,n&=~Zn,e.suspendedLanes|=n,e.pingedLanes&=~n,u&&(e.warmLanes|=n),u=e.expirationTimes;for(var f=n;0<f;){var g=31-qe(f),y=1<<g;u[g]=-1,f&=~y}s!==0&&Gc(e,s,n)}function Ar(){return(zt&6)===0?(Ys(0),!1):!0}function Mh(){if(pt!==null){if(Mt===0)var e=pt.return;else e=pt,zi=jn=null,Yu(e),Cl=null,Bs=0,e=pt;for(;e!==null;)dp(e.alternate,e),e=e.return;pt=null}}function Pl(e,n){var s=e.timeoutHandle;s!==-1&&(e.timeoutHandle=-1,MS(s)),s=e.cancelPendingCommit,s!==null&&(e.cancelPendingCommit=null,s()),Mh(),Bt=e,pt=s=Ai(e.current,null),bt=n,Mt=0,Xe=null,dn=!1,Nl=hs(e,n),wh=!1,Ul=Fe=Qh=Zn=gn=Gt=0,Ee=$s=null,kh=!1,(n&8)!==0&&(n|=n&32);var u=e.entangledLanes;if(u!==0)for(e=e.entanglements,u&=n;0<u;){var f=31-qe(u),g=1<<f;n|=e[f],u&=~g}return Bi=n,Za(),s}function _p(e,n){ct=null,D.H=gr,n===Es||n===lr?(n=Fd(),Mt=3):n===Zd?(n=Fd(),Mt=4):Mt=n===Ig?8:n!==null&&typeof n=="object"&&typeof n.then=="function"?6:1,Xe=n,pt===null&&(Gt=1,Sr(e,We(n,e.current)))}function Np(){var e=D.H;return D.H=gr,e===null?gr:e}function Up(){var e=D.A;return D.A=fS,e}function Ch(){Gt=4,dn||(bt&4194048)!==bt&&Je.current!==null||(Nl=!0),(gn&134217727)===0&&(Zn&134217727)===0||Bt===null||On(Bt,bt,Fe,!1)}function Dh(e,n,s){var u=zt;zt|=2;var f=Np(),g=Up();(Bt!==e||bt!==n)&&(kr=null,Pl(e,n)),n=!1;var y=Gt;t:do try{if(Mt!==0&&pt!==null){var v=pt,x=Xe;switch(Mt){case 8:Mh(),y=6;break t;case 3:case 2:case 9:case 6:Je.current===null&&(n=!0);var R=Mt;if(Mt=0,Xe=null,Vl(e,v,x,R),s&&Nl){y=0;break t}break;default:R=Mt,Mt=0,Xe=null,Vl(e,v,x,R)}}gS(),y=Gt;break}catch(_){_p(e,_)}while(!0);return n&&e.shellSuspendCounter++,zi=jn=null,zt=u,D.H=f,D.A=g,pt===null&&(Bt=null,bt=0,Za()),y}function gS(){for(;pt!==null;)Bp(pt)}function pS(e,n){var s=zt;zt|=2;var u=Np(),f=Up();Bt!==e||bt!==n?(kr=null,Qr=oi()+500,Pl(e,n)):Nl=hs(e,n);t:do try{if(Mt!==0&&pt!==null){n=pt;var g=Xe;e:switch(Mt){case 1:Mt=0,Xe=null,Vl(e,n,g,1);break;case 2:case 9:if(Kd(g)){Mt=0,Xe=null,Xp(n);break}n=function(){Mt!==2&&Mt!==9||Bt!==e||(Mt=7),di(e)},g.then(n,n);break t;case 3:Mt=7;break t;case 4:Mt=5;break t;case 7:Kd(g)?(Mt=0,Xe=null,Xp(n)):(Mt=0,Xe=null,Vl(e,n,g,7));break;case 5:var y=null;switch(pt.tag){case 26:y=pt.memoizedState;case 5:case 27:var v=pt;if(!y||vm(y)){Mt=0,Xe=null;var x=v.sibling;if(x!==null)pt=x;else{var R=v.return;R!==null?(pt=R,Rr(R)):pt=null}break e}}Mt=0,Xe=null,Vl(e,n,g,5);break;case 6:Mt=0,Xe=null,Vl(e,n,g,6);break;case 8:Mh(),Gt=6;break t;default:throw Error(l(462))}}mS();break}catch(_){_p(e,_)}while(!0);return zi=jn=null,D.H=u,D.A=f,zt=s,pt!==null?0:(Bt=null,bt=0,Za(),Gt)}function mS(){for(;pt!==null&&!B1();)Bp(pt)}function Bp(e){var n=fp(e.alternate,e,Bi);e.memoizedProps=e.pendingProps,n===null?Rr(e):pt=n}function Xp(e){var n=e,s=n.alternate;switch(n.tag){case 15:case 0:n=sp(s,n,n.pendingProps,n.type,void 0,bt);break;case 11:n=sp(s,n,n.pendingProps,n.type.render,n.ref,bt);break;case 5:Yu(n);default:dp(s,n),n=pt=Pd(n,Bi),n=fp(s,n,Bi)}e.memoizedProps=e.pendingProps,n===null?Rr(e):pt=n}function Vl(e,n,s,u){zi=jn=null,Yu(n),Cl=null,Bs=0;var f=n.return;try{if(sS(e,f,n,s,bt)){Gt=1,Sr(e,We(s,e.current)),pt=null;return}}catch(g){if(f!==null)throw pt=f,g;Gt=1,Sr(e,We(s,e.current)),pt=null;return}n.flags&32768?(kt||u===1?e=!0:Nl||(bt&536870912)!==0?e=!1:(dn=e=!0,(u===2||u===9||u===3||u===6)&&(u=Je.current,u!==null&&u.tag===13&&(u.flags|=16384))),Pp(n,e)):Rr(n)}function Rr(e){var n=e;do{if((n.flags&32768)!==0){Pp(n,dn);return}e=n.return;var s=rS(n.alternate,n,Bi);if(s!==null){pt=s;return}if(n=n.sibling,n!==null){pt=n;return}pt=n=e}while(n!==null);Gt===0&&(Gt=5)}function Pp(e,n){do{var s=oS(e.alternate,e);if(s!==null){s.flags&=32767,pt=s;return}if(s=e.return,s!==null&&(s.flags|=32768,s.subtreeFlags=0,s.deletions=null),!n&&(e=e.sibling,e!==null)){pt=e;return}pt=e=s}while(e!==null);Gt=6,pt=null}function Vp(e,n,s,u,f,g,y,v,x){e.cancelPendingCommit=null;do Er();while(he!==0);if((zt&6)!==0)throw Error(l(327));if(n!==null){if(n===e.current)throw Error(l(177));if(g=n.lanes|n.childLanes,g|=Tu,Y1(e,s,g,y,v,x),e===Bt&&(pt=Bt=null,bt=0),Bl=n,mn=e,Xl=s,Rh=g,Eh=f,Mp=u,(n.subtreeFlags&10256)!==0||(n.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,vS(qa,function(){return $p(),null})):(e.callbackNode=null,e.callbackPriority=0),u=(n.flags&13878)!==0,(n.subtreeFlags&13878)!==0||u){u=D.T,D.T=null,f=L.p,L.p=2,y=zt,zt|=4;try{uS(e,n,s)}finally{zt=y,L.p=f,D.T=u}}he=1,Hp(),jp(),Lp()}}function Hp(){if(he===1){he=0;var e=mn,n=Bl,s=(n.flags&13878)!==0;if((n.subtreeFlags&13878)!==0||s){s=D.T,D.T=null;var u=L.p;L.p=2;var f=zt;zt|=4;try{wp(n,e);var g=Gh,y=zd(e.containerInfo),v=g.focusedElem,x=g.selectionRange;if(y!==v&&v&&v.ownerDocument&&Ed(v.ownerDocument.documentElement,v)){if(x!==null&&yu(v)){var R=x.start,_=x.end;if(_===void 0&&(_=R),"selectionStart"in v)v.selectionStart=R,v.selectionEnd=Math.min(_,v.value.length);else{var B=v.ownerDocument||document,E=B&&B.defaultView||window;if(E.getSelection){var M=E.getSelection(),rt=v.textContent.length,nt=Math.min(x.start,rt),qt=x.end===void 0?nt:Math.min(x.end,rt);!M.extend&&nt>qt&&(y=qt,qt=nt,nt=y);var k=Rd(v,nt),Q=Rd(v,qt);if(k&&Q&&(M.rangeCount!==1||M.anchorNode!==k.node||M.anchorOffset!==k.offset||M.focusNode!==Q.node||M.focusOffset!==Q.offset)){var A=B.createRange();A.setStart(k.node,k.offset),M.removeAllRanges(),nt>qt?(M.addRange(A),M.extend(Q.node,Q.offset)):(A.setEnd(Q.node,Q.offset),M.addRange(A))}}}}for(B=[],M=v;M=M.parentNode;)M.nodeType===1&&B.push({element:M,left:M.scrollLeft,top:M.scrollTop});for(typeof v.focus=="function"&&v.focus(),v=0;v<B.length;v++){var N=B[v];N.element.scrollLeft=N.left,N.element.scrollTop=N.top}}Vr=!!Lh,Gh=Lh=null}finally{zt=f,L.p=u,D.T=s}}e.current=n,he=2}}function jp(){if(he===2){he=0;var e=mn,n=Bl,s=(n.flags&8772)!==0;if((n.subtreeFlags&8772)!==0||s){s=D.T,D.T=null;var u=L.p;L.p=2;var f=zt;zt|=4;try{vp(e,n.alternate,n)}finally{zt=f,L.p=u,D.T=s}}he=3}}function Lp(){if(he===4||he===3){he=0,X1();var e=mn,n=Bl,s=Xl,u=Mp;(n.subtreeFlags&10256)!==0||(n.flags&10256)!==0?he=5:(he=0,Bl=mn=null,Gp(e,e.pendingLanes));var f=e.pendingLanes;if(f===0&&(pn=null),Jo(s),n=n.stateNode,De&&typeof De.onCommitFiberRoot=="function")try{De.onCommitFiberRoot(us,n,void 0,(n.current.flags&128)===128)}catch{}if(u!==null){n=D.T,f=L.p,L.p=2,D.T=null;try{for(var g=e.onRecoverableError,y=0;y<u.length;y++){var v=u[y];g(v.value,{componentStack:v.stack})}}finally{D.T=n,L.p=f}}(Xl&3)!==0&&Er(),di(e),f=e.pendingLanes,(s&4194090)!==0&&(f&42)!==0?e===zh?Ws++:(Ws=0,zh=e):Ws=0,Ys(0)}}function Gp(e,n){(e.pooledCacheLanes&=n)===0&&(n=e.pooledCache,n!=null&&(e.pooledCache=null,As(n)))}function Er(e){return Hp(),jp(),Lp(),$p()}function $p(){if(he!==5)return!1;var e=mn,n=Rh;Rh=0;var s=Jo(Xl),u=D.T,f=L.p;try{L.p=32>s?32:s,D.T=null,s=Eh,Eh=null;var g=mn,y=Xl;if(he=0,Bl=mn=null,Xl=0,(zt&6)!==0)throw Error(l(331));var v=zt;if(zt|=4,Ep(g.current),kp(g,g.current,y,s),zt=v,Ys(0,!1),De&&typeof De.onPostCommitFiberRoot=="function")try{De.onPostCommitFiberRoot(us,g)}catch{}return!0}finally{L.p=f,D.T=u,Gp(e,n)}}function Wp(e,n,s){n=We(s,n),n=oh(e.stateNode,n,2),e=an(e,n,2),e!==null&&(fs(e,2),di(e))}function _t(e,n,s){if(e.tag===3)Wp(e,e,s);else for(;n!==null;){if(n.tag===3){Wp(n,e,s);break}else if(n.tag===1){var u=n.stateNode;if(typeof n.type.getDerivedStateFromError=="function"||typeof u.componentDidCatch=="function"&&(pn===null||!pn.has(u))){e=We(s,e),s=Jg(2),u=an(n,s,2),u!==null&&(Fg(s,u,n,e),fs(u,2),di(u));break}}n=n.return}}function qh(e,n,s){var u=e.pingCache;if(u===null){u=e.pingCache=new cS;var f=new Set;u.set(n,f)}else f=u.get(n),f===void 0&&(f=new Set,u.set(n,f));f.has(s)||(wh=!0,f.add(s),e=OS.bind(null,e,n,s),n.then(e,e))}function OS(e,n,s){var u=e.pingCache;u!==null&&u.delete(n),e.pingedLanes|=e.suspendedLanes&s,e.warmLanes&=~s,Bt===e&&(bt&s)===s&&(Gt===4||Gt===3&&(bt&62914560)===bt&&300>oi()-Ah?(zt&2)===0&&Pl(e,0):Qh|=s,Ul===bt&&(Ul=0)),di(e)}function Yp(e,n){n===0&&(n=Lc()),e=xl(e,n),e!==null&&(fs(e,n),di(e))}function yS(e){var n=e.memoizedState,s=0;n!==null&&(s=n.retryLane),Yp(e,s)}function SS(e,n){var s=0;switch(e.tag){case 13:var u=e.stateNode,f=e.memoizedState;f!==null&&(s=f.retryLane);break;case 19:u=e.stateNode;break;case 22:u=e.stateNode._retryCache;break;default:throw Error(l(314))}u!==null&&u.delete(n),Yp(e,s)}function vS(e,n){return Wo(e,n)}var zr=null,Hl=null,_h=!1,Mr=!1,Nh=!1,Kn=0;function di(e){e!==Hl&&e.next===null&&(Hl===null?zr=Hl=e:Hl=Hl.next=e),Mr=!0,_h||(_h=!0,xS())}function Ys(e,n){if(!Nh&&Mr){Nh=!0;do for(var s=!1,u=zr;u!==null;){if(e!==0){var f=u.pendingLanes;if(f===0)var g=0;else{var y=u.suspendedLanes,v=u.pingedLanes;g=(1<<31-qe(42|e)+1)-1,g&=f&~(y&~v),g=g&201326741?g&201326741|1:g?g|2:0}g!==0&&(s=!0,Fp(u,g))}else g=bt,g=Ua(u,u===Bt?g:0,u.cancelPendingCommit!==null||u.timeoutHandle!==-1),(g&3)===0||hs(u,g)||(s=!0,Fp(u,g));u=u.next}while(s);Nh=!1}}function bS(){Zp()}function Zp(){Mr=_h=!1;var e=0;Kn!==0&&(zS()&&(e=Kn),Kn=0);for(var n=oi(),s=null,u=zr;u!==null;){var f=u.next,g=Kp(u,n);g===0?(u.next=null,s===null?zr=f:s.next=f,f===null&&(Hl=s)):(s=u,(e!==0||(g&3)!==0)&&(Mr=!0)),u=f}Ys(e)}function Kp(e,n){for(var s=e.suspendedLanes,u=e.pingedLanes,f=e.expirationTimes,g=e.pendingLanes&-62914561;0<g;){var y=31-qe(g),v=1<<y,x=f[y];x===-1?((v&s)===0||(v&u)!==0)&&(f[y]=W1(v,n)):x<=n&&(e.expiredLanes|=v),g&=~v}if(n=Bt,s=bt,s=Ua(e,e===n?s:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),u=e.callbackNode,s===0||e===n&&(Mt===2||Mt===9)||e.cancelPendingCommit!==null)return u!==null&&u!==null&&Yo(u),e.callbackNode=null,e.callbackPriority=0;if((s&3)===0||hs(e,s)){if(n=s&-s,n===e.callbackPriority)return n;switch(u!==null&&Yo(u),Jo(s)){case 2:case 8:s=Vc;break;case 32:s=qa;break;case 268435456:s=Hc;break;default:s=qa}return u=Jp.bind(null,e),s=Wo(s,u),e.callbackPriority=n,e.callbackNode=s,n}return u!==null&&u!==null&&Yo(u),e.callbackPriority=2,e.callbackNode=null,2}function Jp(e,n){if(he!==0&&he!==5)return e.callbackNode=null,e.callbackPriority=0,null;var s=e.callbackNode;if(Er()&&e.callbackNode!==s)return null;var u=bt;return u=Ua(e,e===Bt?u:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),u===0?null:(Dp(e,u,n),Kp(e,oi()),e.callbackNode!=null&&e.callbackNode===s?Jp.bind(null,e):null)}function Fp(e,n){if(Er())return null;Dp(e,n,!0)}function xS(){CS(function(){(zt&6)!==0?Wo(Pc,bS):Zp()})}function Uh(){return Kn===0&&(Kn=jc()),Kn}function Ip(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Ha(""+e)}function tm(e,n){var s=n.ownerDocument.createElement("input");return s.name=n.name,s.value=n.value,e.id&&s.setAttribute("form",e.id),n.parentNode.insertBefore(s,n),e=new FormData(e),s.parentNode.removeChild(s),e}function TS(e,n,s,u,f){if(n==="submit"&&s&&s.stateNode===f){var g=Ip((f[Qe]||null).action),y=u.submitter;y&&(n=(n=y[Qe]||null)?Ip(n.formAction):y.getAttribute("formAction"),n!==null&&(g=n,y=null));var v=new $a("action","action",null,u,f);e.push({event:v,listeners:[{instance:null,listener:function(){if(u.defaultPrevented){if(Kn!==0){var x=y?tm(f,y):new FormData(f);nh(s,{pending:!0,data:x,method:f.method,action:g},null,x)}}else typeof g=="function"&&(v.preventDefault(),x=y?tm(f,y):new FormData(f),nh(s,{pending:!0,data:x,method:f.method,action:g},g,x))},currentTarget:f}]})}}for(var Bh=0;Bh<xu.length;Bh++){var Xh=xu[Bh],wS=Xh.toLowerCase(),QS=Xh[0].toUpperCase()+Xh.slice(1);ni(wS,"on"+QS)}ni(Dd,"onAnimationEnd"),ni(qd,"onAnimationIteration"),ni(_d,"onAnimationStart"),ni("dblclick","onDoubleClick"),ni("focusin","onFocus"),ni("focusout","onBlur"),ni(Hy,"onTransitionRun"),ni(jy,"onTransitionStart"),ni(Ly,"onTransitionCancel"),ni(Nd,"onTransitionEnd"),cl("onMouseEnter",["mouseout","mouseover"]),cl("onMouseLeave",["mouseout","mouseover"]),cl("onPointerEnter",["pointerout","pointerover"]),cl("onPointerLeave",["pointerout","pointerover"]),qn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),qn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),qn("onBeforeInput",["compositionend","keypress","textInput","paste"]),qn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),qn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),qn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Zs="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),kS=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Zs));function em(e,n){n=(n&4)!==0;for(var s=0;s<e.length;s++){var u=e[s],f=u.event;u=u.listeners;t:{var g=void 0;if(n)for(var y=u.length-1;0<=y;y--){var v=u[y],x=v.instance,R=v.currentTarget;if(v=v.listener,x!==g&&f.isPropagationStopped())break t;g=v,f.currentTarget=R;try{g(f)}catch(_){yr(_)}f.currentTarget=null,g=x}else for(y=0;y<u.length;y++){if(v=u[y],x=v.instance,R=v.currentTarget,v=v.listener,x!==g&&f.isPropagationStopped())break t;g=v,f.currentTarget=R;try{g(f)}catch(_){yr(_)}f.currentTarget=null,g=x}}}}function mt(e,n){var s=n[Fo];s===void 0&&(s=n[Fo]=new Set);var u=e+"__bubble";s.has(u)||(im(n,e,2,!1),s.add(u))}function Ph(e,n,s){var u=0;n&&(u|=4),im(s,e,u,n)}var Cr="_reactListening"+Math.random().toString(36).slice(2);function Vh(e){if(!e[Cr]){e[Cr]=!0,Zc.forEach(function(s){s!=="selectionchange"&&(kS.has(s)||Ph(s,!1,e),Ph(s,!0,e))});var n=e.nodeType===9?e:e.ownerDocument;n===null||n[Cr]||(n[Cr]=!0,Ph("selectionchange",!1,n))}}function im(e,n,s,u){switch(km(n)){case 2:var f=IS;break;case 8:f=tv;break;default:f=ef}s=f.bind(null,n,s,e),f=void 0,!uu||n!=="touchstart"&&n!=="touchmove"&&n!=="wheel"||(f=!0),u?f!==void 0?e.addEventListener(n,s,{capture:!0,passive:f}):e.addEventListener(n,s,!0):f!==void 0?e.addEventListener(n,s,{passive:f}):e.addEventListener(n,s,!1)}function Hh(e,n,s,u,f){var g=u;if((n&1)===0&&(n&2)===0&&u!==null)t:for(;;){if(u===null)return;var y=u.tag;if(y===3||y===4){var v=u.stateNode.containerInfo;if(v===f)break;if(y===4)for(y=u.return;y!==null;){var x=y.tag;if((x===3||x===4)&&y.stateNode.containerInfo===f)return;y=y.return}for(;v!==null;){if(y=ul(v),y===null)return;if(x=y.tag,x===5||x===6||x===26||x===27){u=g=y;continue t}v=v.parentNode}}u=u.return}ud(function(){var R=g,_=ru(s),B=[];t:{var E=Ud.get(e);if(E!==void 0){var M=$a,rt=e;switch(e){case"keypress":if(La(s)===0)break t;case"keydown":case"keyup":M=vy;break;case"focusin":rt="focus",M=du;break;case"focusout":rt="blur",M=du;break;case"beforeblur":case"afterblur":M=du;break;case"click":if(s.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":M=cd;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":M=oy;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":M=Ty;break;case Dd:case qd:case _d:M=fy;break;case Nd:M=Qy;break;case"scroll":case"scrollend":M=ay;break;case"wheel":M=Ay;break;case"copy":case"cut":case"paste":M=dy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":M=gd;break;case"toggle":case"beforetoggle":M=Ey}var nt=(n&4)!==0,qt=!nt&&(e==="scroll"||e==="scrollend"),k=nt?E!==null?E+"Capture":null:E;nt=[];for(var Q=R,A;Q!==null;){var N=Q;if(A=N.stateNode,N=N.tag,N!==5&&N!==26&&N!==27||A===null||k===null||(N=gs(Q,k),N!=null&&nt.push(Ks(Q,N,A))),qt)break;Q=Q.return}0<nt.length&&(E=new M(E,rt,null,s,_),B.push({event:E,listeners:nt}))}}if((n&7)===0){t:{if(E=e==="mouseover"||e==="pointerover",M=e==="mouseout"||e==="pointerout",E&&s!==au&&(rt=s.relatedTarget||s.fromElement)&&(ul(rt)||rt[ol]))break t;if((M||E)&&(E=_.window===_?_:(E=_.ownerDocument)?E.defaultView||E.parentWindow:window,M?(rt=s.relatedTarget||s.toElement,M=R,rt=rt?ul(rt):null,rt!==null&&(qt=o(rt),nt=rt.tag,rt!==qt||nt!==5&&nt!==27&&nt!==6)&&(rt=null)):(M=null,rt=R),M!==rt)){if(nt=cd,N="onMouseLeave",k="onMouseEnter",Q="mouse",(e==="pointerout"||e==="pointerover")&&(nt=gd,N="onPointerLeave",k="onPointerEnter",Q="pointer"),qt=M==null?E:ds(M),A=rt==null?E:ds(rt),E=new nt(N,Q+"leave",M,s,_),E.target=qt,E.relatedTarget=A,N=null,ul(_)===R&&(nt=new nt(k,Q+"enter",rt,s,_),nt.target=A,nt.relatedTarget=qt,N=nt),qt=N,M&&rt)e:{for(nt=M,k=rt,Q=0,A=nt;A;A=jl(A))Q++;for(A=0,N=k;N;N=jl(N))A++;for(;0<Q-A;)nt=jl(nt),Q--;for(;0<A-Q;)k=jl(k),A--;for(;Q--;){if(nt===k||k!==null&&nt===k.alternate)break e;nt=jl(nt),k=jl(k)}nt=null}else nt=null;M!==null&&nm(B,E,M,nt,!1),rt!==null&&qt!==null&&nm(B,qt,rt,nt,!0)}}t:{if(E=R?ds(R):window,M=E.nodeName&&E.nodeName.toLowerCase(),M==="select"||M==="input"&&E.type==="file")var K=xd;else if(vd(E))if(Td)K=Xy;else{K=Uy;var dt=Ny}else M=E.nodeName,!M||M.toLowerCase()!=="input"||E.type!=="checkbox"&&E.type!=="radio"?R&&su(R.elementType)&&(K=xd):K=By;if(K&&(K=K(e,R))){bd(B,K,s,_);break t}dt&&dt(e,E,R),e==="focusout"&&R&&E.type==="number"&&R.memoizedProps.value!=null&&lu(E,"number",E.value)}switch(dt=R?ds(R):window,e){case"focusin":(vd(dt)||dt.contentEditable==="true")&&(Sl=dt,Su=R,xs=null);break;case"focusout":xs=Su=Sl=null;break;case"mousedown":vu=!0;break;case"contextmenu":case"mouseup":case"dragend":vu=!1,Md(B,s,_);break;case"selectionchange":if(Vy)break;case"keydown":case"keyup":Md(B,s,_)}var et;if(pu)t:{switch(e){case"compositionstart":var lt="onCompositionStart";break t;case"compositionend":lt="onCompositionEnd";break t;case"compositionupdate":lt="onCompositionUpdate";break t}lt=void 0}else yl?yd(e,s)&&(lt="onCompositionEnd"):e==="keydown"&&s.keyCode===229&&(lt="onCompositionStart");lt&&(pd&&s.locale!=="ko"&&(yl||lt!=="onCompositionStart"?lt==="onCompositionEnd"&&yl&&(et=hd()):(en=_,hu="value"in en?en.value:en.textContent,yl=!0)),dt=Dr(R,lt),0<dt.length&&(lt=new dd(lt,e,null,s,_),B.push({event:lt,listeners:dt}),et?lt.data=et:(et=Sd(s),et!==null&&(lt.data=et)))),(et=My?Cy(e,s):Dy(e,s))&&(lt=Dr(R,"onBeforeInput"),0<lt.length&&(dt=new dd("onBeforeInput","beforeinput",null,s,_),B.push({event:dt,listeners:lt}),dt.data=et)),TS(B,e,R,s,_)}em(B,n)})}function Ks(e,n,s){return{instance:e,listener:n,currentTarget:s}}function Dr(e,n){for(var s=n+"Capture",u=[];e!==null;){var f=e,g=f.stateNode;if(f=f.tag,f!==5&&f!==26&&f!==27||g===null||(f=gs(e,s),f!=null&&u.unshift(Ks(e,f,g)),f=gs(e,n),f!=null&&u.push(Ks(e,f,g))),e.tag===3)return u;e=e.return}return[]}function jl(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function nm(e,n,s,u,f){for(var g=n._reactName,y=[];s!==null&&s!==u;){var v=s,x=v.alternate,R=v.stateNode;if(v=v.tag,x!==null&&x===u)break;v!==5&&v!==26&&v!==27||R===null||(x=R,f?(R=gs(s,g),R!=null&&y.unshift(Ks(s,R,x))):f||(R=gs(s,g),R!=null&&y.push(Ks(s,R,x)))),s=s.return}y.length!==0&&e.push({event:n,listeners:y})}var AS=/\r\n?/g,RS=/\u0000|\uFFFD/g;function lm(e){return(typeof e=="string"?e:""+e).replace(AS,`
`).replace(RS,"")}function sm(e,n){return n=lm(n),lm(e)===n}function qr(){}function Dt(e,n,s,u,f,g){switch(s){case"children":typeof u=="string"?n==="body"||n==="textarea"&&u===""||pl(e,u):(typeof u=="number"||typeof u=="bigint")&&n!=="body"&&pl(e,""+u);break;case"className":Xa(e,"class",u);break;case"tabIndex":Xa(e,"tabindex",u);break;case"dir":case"role":case"viewBox":case"width":case"height":Xa(e,s,u);break;case"style":rd(e,u,g);break;case"data":if(n!=="object"){Xa(e,"data",u);break}case"src":case"href":if(u===""&&(n!=="a"||s!=="href")){e.removeAttribute(s);break}if(u==null||typeof u=="function"||typeof u=="symbol"||typeof u=="boolean"){e.removeAttribute(s);break}u=Ha(""+u),e.setAttribute(s,u);break;case"action":case"formAction":if(typeof u=="function"){e.setAttribute(s,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof g=="function"&&(s==="formAction"?(n!=="input"&&Dt(e,n,"name",f.name,f,null),Dt(e,n,"formEncType",f.formEncType,f,null),Dt(e,n,"formMethod",f.formMethod,f,null),Dt(e,n,"formTarget",f.formTarget,f,null)):(Dt(e,n,"encType",f.encType,f,null),Dt(e,n,"method",f.method,f,null),Dt(e,n,"target",f.target,f,null)));if(u==null||typeof u=="symbol"||typeof u=="boolean"){e.removeAttribute(s);break}u=Ha(""+u),e.setAttribute(s,u);break;case"onClick":u!=null&&(e.onclick=qr);break;case"onScroll":u!=null&&mt("scroll",e);break;case"onScrollEnd":u!=null&&mt("scrollend",e);break;case"dangerouslySetInnerHTML":if(u!=null){if(typeof u!="object"||!("__html"in u))throw Error(l(61));if(s=u.__html,s!=null){if(f.children!=null)throw Error(l(60));e.innerHTML=s}}break;case"multiple":e.multiple=u&&typeof u!="function"&&typeof u!="symbol";break;case"muted":e.muted=u&&typeof u!="function"&&typeof u!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(u==null||typeof u=="function"||typeof u=="boolean"||typeof u=="symbol"){e.removeAttribute("xlink:href");break}s=Ha(""+u),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",s);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":u!=null&&typeof u!="function"&&typeof u!="symbol"?e.setAttribute(s,""+u):e.removeAttribute(s);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":u&&typeof u!="function"&&typeof u!="symbol"?e.setAttribute(s,""):e.removeAttribute(s);break;case"capture":case"download":u===!0?e.setAttribute(s,""):u!==!1&&u!=null&&typeof u!="function"&&typeof u!="symbol"?e.setAttribute(s,u):e.removeAttribute(s);break;case"cols":case"rows":case"size":case"span":u!=null&&typeof u!="function"&&typeof u!="symbol"&&!isNaN(u)&&1<=u?e.setAttribute(s,u):e.removeAttribute(s);break;case"rowSpan":case"start":u==null||typeof u=="function"||typeof u=="symbol"||isNaN(u)?e.removeAttribute(s):e.setAttribute(s,u);break;case"popover":mt("beforetoggle",e),mt("toggle",e),Ba(e,"popover",u);break;case"xlinkActuate":Qi(e,"http://www.w3.org/1999/xlink","xlink:actuate",u);break;case"xlinkArcrole":Qi(e,"http://www.w3.org/1999/xlink","xlink:arcrole",u);break;case"xlinkRole":Qi(e,"http://www.w3.org/1999/xlink","xlink:role",u);break;case"xlinkShow":Qi(e,"http://www.w3.org/1999/xlink","xlink:show",u);break;case"xlinkTitle":Qi(e,"http://www.w3.org/1999/xlink","xlink:title",u);break;case"xlinkType":Qi(e,"http://www.w3.org/1999/xlink","xlink:type",u);break;case"xmlBase":Qi(e,"http://www.w3.org/XML/1998/namespace","xml:base",u);break;case"xmlLang":Qi(e,"http://www.w3.org/XML/1998/namespace","xml:lang",u);break;case"xmlSpace":Qi(e,"http://www.w3.org/XML/1998/namespace","xml:space",u);break;case"is":Ba(e,"is",u);break;case"innerText":case"textContent":break;default:(!(2<s.length)||s[0]!=="o"&&s[0]!=="O"||s[1]!=="n"&&s[1]!=="N")&&(s=ly.get(s)||s,Ba(e,s,u))}}function jh(e,n,s,u,f,g){switch(s){case"style":rd(e,u,g);break;case"dangerouslySetInnerHTML":if(u!=null){if(typeof u!="object"||!("__html"in u))throw Error(l(61));if(s=u.__html,s!=null){if(f.children!=null)throw Error(l(60));e.innerHTML=s}}break;case"children":typeof u=="string"?pl(e,u):(typeof u=="number"||typeof u=="bigint")&&pl(e,""+u);break;case"onScroll":u!=null&&mt("scroll",e);break;case"onScrollEnd":u!=null&&mt("scrollend",e);break;case"onClick":u!=null&&(e.onclick=qr);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Kc.hasOwnProperty(s))t:{if(s[0]==="o"&&s[1]==="n"&&(f=s.endsWith("Capture"),n=s.slice(2,f?s.length-7:void 0),g=e[Qe]||null,g=g!=null?g[s]:null,typeof g=="function"&&e.removeEventListener(n,g,f),typeof u=="function")){typeof g!="function"&&g!==null&&(s in e?e[s]=null:e.hasAttribute(s)&&e.removeAttribute(s)),e.addEventListener(n,u,f);break t}s in e?e[s]=u:u===!0?e.setAttribute(s,""):Ba(e,s,u)}}}function fe(e,n,s){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":mt("error",e),mt("load",e);var u=!1,f=!1,g;for(g in s)if(s.hasOwnProperty(g)){var y=s[g];if(y!=null)switch(g){case"src":u=!0;break;case"srcSet":f=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,n));default:Dt(e,n,g,y,s,null)}}f&&Dt(e,n,"srcSet",s.srcSet,s,null),u&&Dt(e,n,"src",s.src,s,null);return;case"input":mt("invalid",e);var v=g=y=f=null,x=null,R=null;for(u in s)if(s.hasOwnProperty(u)){var _=s[u];if(_!=null)switch(u){case"name":f=_;break;case"type":y=_;break;case"checked":x=_;break;case"defaultChecked":R=_;break;case"value":g=_;break;case"defaultValue":v=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(l(137,n));break;default:Dt(e,n,u,_,s,null)}}nd(e,g,v,x,R,y,f,!1),Pa(e);return;case"select":mt("invalid",e),u=y=g=null;for(f in s)if(s.hasOwnProperty(f)&&(v=s[f],v!=null))switch(f){case"value":g=v;break;case"defaultValue":y=v;break;case"multiple":u=v;default:Dt(e,n,f,v,s,null)}n=g,s=y,e.multiple=!!u,n!=null?gl(e,!!u,n,!1):s!=null&&gl(e,!!u,s,!0);return;case"textarea":mt("invalid",e),g=f=u=null;for(y in s)if(s.hasOwnProperty(y)&&(v=s[y],v!=null))switch(y){case"value":u=v;break;case"defaultValue":f=v;break;case"children":g=v;break;case"dangerouslySetInnerHTML":if(v!=null)throw Error(l(91));break;default:Dt(e,n,y,v,s,null)}sd(e,u,f,g),Pa(e);return;case"option":for(x in s)if(s.hasOwnProperty(x)&&(u=s[x],u!=null))switch(x){case"selected":e.selected=u&&typeof u!="function"&&typeof u!="symbol";break;default:Dt(e,n,x,u,s,null)}return;case"dialog":mt("beforetoggle",e),mt("toggle",e),mt("cancel",e),mt("close",e);break;case"iframe":case"object":mt("load",e);break;case"video":case"audio":for(u=0;u<Zs.length;u++)mt(Zs[u],e);break;case"image":mt("error",e),mt("load",e);break;case"details":mt("toggle",e);break;case"embed":case"source":case"link":mt("error",e),mt("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(R in s)if(s.hasOwnProperty(R)&&(u=s[R],u!=null))switch(R){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,n));default:Dt(e,n,R,u,s,null)}return;default:if(su(n)){for(_ in s)s.hasOwnProperty(_)&&(u=s[_],u!==void 0&&jh(e,n,_,u,s,void 0));return}}for(v in s)s.hasOwnProperty(v)&&(u=s[v],u!=null&&Dt(e,n,v,u,s,null))}function ES(e,n,s,u){switch(n){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var f=null,g=null,y=null,v=null,x=null,R=null,_=null;for(M in s){var B=s[M];if(s.hasOwnProperty(M)&&B!=null)switch(M){case"checked":break;case"value":break;case"defaultValue":x=B;default:u.hasOwnProperty(M)||Dt(e,n,M,null,u,B)}}for(var E in u){var M=u[E];if(B=s[E],u.hasOwnProperty(E)&&(M!=null||B!=null))switch(E){case"type":g=M;break;case"name":f=M;break;case"checked":R=M;break;case"defaultChecked":_=M;break;case"value":y=M;break;case"defaultValue":v=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(l(137,n));break;default:M!==B&&Dt(e,n,E,M,u,B)}}nu(e,y,v,x,R,_,g,f);return;case"select":M=y=v=E=null;for(g in s)if(x=s[g],s.hasOwnProperty(g)&&x!=null)switch(g){case"value":break;case"multiple":M=x;default:u.hasOwnProperty(g)||Dt(e,n,g,null,u,x)}for(f in u)if(g=u[f],x=s[f],u.hasOwnProperty(f)&&(g!=null||x!=null))switch(f){case"value":E=g;break;case"defaultValue":v=g;break;case"multiple":y=g;default:g!==x&&Dt(e,n,f,g,u,x)}n=v,s=y,u=M,E!=null?gl(e,!!s,E,!1):!!u!=!!s&&(n!=null?gl(e,!!s,n,!0):gl(e,!!s,s?[]:"",!1));return;case"textarea":M=E=null;for(v in s)if(f=s[v],s.hasOwnProperty(v)&&f!=null&&!u.hasOwnProperty(v))switch(v){case"value":break;case"children":break;default:Dt(e,n,v,null,u,f)}for(y in u)if(f=u[y],g=s[y],u.hasOwnProperty(y)&&(f!=null||g!=null))switch(y){case"value":E=f;break;case"defaultValue":M=f;break;case"children":break;case"dangerouslySetInnerHTML":if(f!=null)throw Error(l(91));break;default:f!==g&&Dt(e,n,y,f,u,g)}ld(e,E,M);return;case"option":for(var rt in s)if(E=s[rt],s.hasOwnProperty(rt)&&E!=null&&!u.hasOwnProperty(rt))switch(rt){case"selected":e.selected=!1;break;default:Dt(e,n,rt,null,u,E)}for(x in u)if(E=u[x],M=s[x],u.hasOwnProperty(x)&&E!==M&&(E!=null||M!=null))switch(x){case"selected":e.selected=E&&typeof E!="function"&&typeof E!="symbol";break;default:Dt(e,n,x,E,u,M)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var nt in s)E=s[nt],s.hasOwnProperty(nt)&&E!=null&&!u.hasOwnProperty(nt)&&Dt(e,n,nt,null,u,E);for(R in u)if(E=u[R],M=s[R],u.hasOwnProperty(R)&&E!==M&&(E!=null||M!=null))switch(R){case"children":case"dangerouslySetInnerHTML":if(E!=null)throw Error(l(137,n));break;default:Dt(e,n,R,E,u,M)}return;default:if(su(n)){for(var qt in s)E=s[qt],s.hasOwnProperty(qt)&&E!==void 0&&!u.hasOwnProperty(qt)&&jh(e,n,qt,void 0,u,E);for(_ in u)E=u[_],M=s[_],!u.hasOwnProperty(_)||E===M||E===void 0&&M===void 0||jh(e,n,_,E,u,M);return}}for(var k in s)E=s[k],s.hasOwnProperty(k)&&E!=null&&!u.hasOwnProperty(k)&&Dt(e,n,k,null,u,E);for(B in u)E=u[B],M=s[B],!u.hasOwnProperty(B)||E===M||E==null&&M==null||Dt(e,n,B,E,u,M)}var Lh=null,Gh=null;function _r(e){return e.nodeType===9?e:e.ownerDocument}function am(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function rm(e,n){if(e===0)switch(n){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&n==="foreignObject"?0:e}function $h(e,n){return e==="textarea"||e==="noscript"||typeof n.children=="string"||typeof n.children=="number"||typeof n.children=="bigint"||typeof n.dangerouslySetInnerHTML=="object"&&n.dangerouslySetInnerHTML!==null&&n.dangerouslySetInnerHTML.__html!=null}var Wh=null;function zS(){var e=window.event;return e&&e.type==="popstate"?e===Wh?!1:(Wh=e,!0):(Wh=null,!1)}var om=typeof setTimeout=="function"?setTimeout:void 0,MS=typeof clearTimeout=="function"?clearTimeout:void 0,um=typeof Promise=="function"?Promise:void 0,CS=typeof queueMicrotask=="function"?queueMicrotask:typeof um<"u"?function(e){return um.resolve(null).then(e).catch(DS)}:om;function DS(e){setTimeout(function(){throw e})}function yn(e){return e==="head"}function hm(e,n){var s=n,u=0,f=0;do{var g=s.nextSibling;if(e.removeChild(s),g&&g.nodeType===8)if(s=g.data,s==="/$"){if(0<u&&8>u){s=u;var y=e.ownerDocument;if(s&1&&Js(y.documentElement),s&2&&Js(y.body),s&4)for(s=y.head,Js(s),y=s.firstChild;y;){var v=y.nextSibling,x=y.nodeName;y[cs]||x==="SCRIPT"||x==="STYLE"||x==="LINK"&&y.rel.toLowerCase()==="stylesheet"||s.removeChild(y),y=v}}if(f===0){e.removeChild(g),sa(n);return}f--}else s==="$"||s==="$?"||s==="$!"?f++:u=s.charCodeAt(0)-48;else u=0;s=g}while(s);sa(n)}function Yh(e){var n=e.firstChild;for(n&&n.nodeType===10&&(n=n.nextSibling);n;){var s=n;switch(n=n.nextSibling,s.nodeName){case"HTML":case"HEAD":case"BODY":Yh(s),Io(s);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(s.rel.toLowerCase()==="stylesheet")continue}e.removeChild(s)}}function qS(e,n,s,u){for(;e.nodeType===1;){var f=s;if(e.nodeName.toLowerCase()!==n.toLowerCase()){if(!u&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(u){if(!e[cs])switch(n){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(g=e.getAttribute("rel"),g==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(g!==f.rel||e.getAttribute("href")!==(f.href==null||f.href===""?null:f.href)||e.getAttribute("crossorigin")!==(f.crossOrigin==null?null:f.crossOrigin)||e.getAttribute("title")!==(f.title==null?null:f.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(g=e.getAttribute("src"),(g!==(f.src==null?null:f.src)||e.getAttribute("type")!==(f.type==null?null:f.type)||e.getAttribute("crossorigin")!==(f.crossOrigin==null?null:f.crossOrigin))&&g&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(n==="input"&&e.type==="hidden"){var g=f.name==null?null:""+f.name;if(f.type==="hidden"&&e.getAttribute("name")===g)return e}else return e;if(e=si(e.nextSibling),e===null)break}return null}function _S(e,n,s){if(n==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!s||(e=si(e.nextSibling),e===null))return null;return e}function Zh(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function NS(e,n){var s=e.ownerDocument;if(e.data!=="$?"||s.readyState==="complete")n();else{var u=function(){n(),s.removeEventListener("DOMContentLoaded",u)};s.addEventListener("DOMContentLoaded",u),e._reactRetry=u}}function si(e){for(;e!=null;e=e.nextSibling){var n=e.nodeType;if(n===1||n===3)break;if(n===8){if(n=e.data,n==="$"||n==="$!"||n==="$?"||n==="F!"||n==="F")break;if(n==="/$")return null}}return e}var Kh=null;function fm(e){e=e.previousSibling;for(var n=0;e;){if(e.nodeType===8){var s=e.data;if(s==="$"||s==="$!"||s==="$?"){if(n===0)return e;n--}else s==="/$"&&n++}e=e.previousSibling}return null}function cm(e,n,s){switch(n=_r(s),e){case"html":if(e=n.documentElement,!e)throw Error(l(452));return e;case"head":if(e=n.head,!e)throw Error(l(453));return e;case"body":if(e=n.body,!e)throw Error(l(454));return e;default:throw Error(l(451))}}function Js(e){for(var n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Io(e)}var Ie=new Map,dm=new Set;function Nr(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var Xi=L.d;L.d={f:US,r:BS,D:XS,C:PS,L:VS,m:HS,X:LS,S:jS,M:GS};function US(){var e=Xi.f(),n=Ar();return e||n}function BS(e){var n=hl(e);n!==null&&n.tag===5&&n.type==="form"?Dg(n):Xi.r(e)}var Ll=typeof document>"u"?null:document;function gm(e,n,s){var u=Ll;if(u&&typeof n=="string"&&n){var f=$e(n);f='link[rel="'+e+'"][href="'+f+'"]',typeof s=="string"&&(f+='[crossorigin="'+s+'"]'),dm.has(f)||(dm.add(f),e={rel:e,crossOrigin:s,href:n},u.querySelector(f)===null&&(n=u.createElement("link"),fe(n,"link",e),le(n),u.head.appendChild(n)))}}function XS(e){Xi.D(e),gm("dns-prefetch",e,null)}function PS(e,n){Xi.C(e,n),gm("preconnect",e,n)}function VS(e,n,s){Xi.L(e,n,s);var u=Ll;if(u&&e&&n){var f='link[rel="preload"][as="'+$e(n)+'"]';n==="image"&&s&&s.imageSrcSet?(f+='[imagesrcset="'+$e(s.imageSrcSet)+'"]',typeof s.imageSizes=="string"&&(f+='[imagesizes="'+$e(s.imageSizes)+'"]')):f+='[href="'+$e(e)+'"]';var g=f;switch(n){case"style":g=Gl(e);break;case"script":g=$l(e)}Ie.has(g)||(e=m({rel:"preload",href:n==="image"&&s&&s.imageSrcSet?void 0:e,as:n},s),Ie.set(g,e),u.querySelector(f)!==null||n==="style"&&u.querySelector(Fs(g))||n==="script"&&u.querySelector(Is(g))||(n=u.createElement("link"),fe(n,"link",e),le(n),u.head.appendChild(n)))}}function HS(e,n){Xi.m(e,n);var s=Ll;if(s&&e){var u=n&&typeof n.as=="string"?n.as:"script",f='link[rel="modulepreload"][as="'+$e(u)+'"][href="'+$e(e)+'"]',g=f;switch(u){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":g=$l(e)}if(!Ie.has(g)&&(e=m({rel:"modulepreload",href:e},n),Ie.set(g,e),s.querySelector(f)===null)){switch(u){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(s.querySelector(Is(g)))return}u=s.createElement("link"),fe(u,"link",e),le(u),s.head.appendChild(u)}}}function jS(e,n,s){Xi.S(e,n,s);var u=Ll;if(u&&e){var f=fl(u).hoistableStyles,g=Gl(e);n=n||"default";var y=f.get(g);if(!y){var v={loading:0,preload:null};if(y=u.querySelector(Fs(g)))v.loading=5;else{e=m({rel:"stylesheet",href:e,"data-precedence":n},s),(s=Ie.get(g))&&Jh(e,s);var x=y=u.createElement("link");le(x),fe(x,"link",e),x._p=new Promise(function(R,_){x.onload=R,x.onerror=_}),x.addEventListener("load",function(){v.loading|=1}),x.addEventListener("error",function(){v.loading|=2}),v.loading|=4,Ur(y,n,u)}y={type:"stylesheet",instance:y,count:1,state:v},f.set(g,y)}}}function LS(e,n){Xi.X(e,n);var s=Ll;if(s&&e){var u=fl(s).hoistableScripts,f=$l(e),g=u.get(f);g||(g=s.querySelector(Is(f)),g||(e=m({src:e,async:!0},n),(n=Ie.get(f))&&Fh(e,n),g=s.createElement("script"),le(g),fe(g,"link",e),s.head.appendChild(g)),g={type:"script",instance:g,count:1,state:null},u.set(f,g))}}function GS(e,n){Xi.M(e,n);var s=Ll;if(s&&e){var u=fl(s).hoistableScripts,f=$l(e),g=u.get(f);g||(g=s.querySelector(Is(f)),g||(e=m({src:e,async:!0,type:"module"},n),(n=Ie.get(f))&&Fh(e,n),g=s.createElement("script"),le(g),fe(g,"link",e),s.head.appendChild(g)),g={type:"script",instance:g,count:1,state:null},u.set(f,g))}}function pm(e,n,s,u){var f=(f=at.current)?Nr(f):null;if(!f)throw Error(l(446));switch(e){case"meta":case"title":return null;case"style":return typeof s.precedence=="string"&&typeof s.href=="string"?(n=Gl(s.href),s=fl(f).hoistableStyles,u=s.get(n),u||(u={type:"style",instance:null,count:0,state:null},s.set(n,u)),u):{type:"void",instance:null,count:0,state:null};case"link":if(s.rel==="stylesheet"&&typeof s.href=="string"&&typeof s.precedence=="string"){e=Gl(s.href);var g=fl(f).hoistableStyles,y=g.get(e);if(y||(f=f.ownerDocument||f,y={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},g.set(e,y),(g=f.querySelector(Fs(e)))&&!g._p&&(y.instance=g,y.state.loading=5),Ie.has(e)||(s={rel:"preload",as:"style",href:s.href,crossOrigin:s.crossOrigin,integrity:s.integrity,media:s.media,hrefLang:s.hrefLang,referrerPolicy:s.referrerPolicy},Ie.set(e,s),g||$S(f,e,s,y.state))),n&&u===null)throw Error(l(528,""));return y}if(n&&u!==null)throw Error(l(529,""));return null;case"script":return n=s.async,s=s.src,typeof s=="string"&&n&&typeof n!="function"&&typeof n!="symbol"?(n=$l(s),s=fl(f).hoistableScripts,u=s.get(n),u||(u={type:"script",instance:null,count:0,state:null},s.set(n,u)),u):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,e))}}function Gl(e){return'href="'+$e(e)+'"'}function Fs(e){return'link[rel="stylesheet"]['+e+"]"}function mm(e){return m({},e,{"data-precedence":e.precedence,precedence:null})}function $S(e,n,s,u){e.querySelector('link[rel="preload"][as="style"]['+n+"]")?u.loading=1:(n=e.createElement("link"),u.preload=n,n.addEventListener("load",function(){return u.loading|=1}),n.addEventListener("error",function(){return u.loading|=2}),fe(n,"link",s),le(n),e.head.appendChild(n))}function $l(e){return'[src="'+$e(e)+'"]'}function Is(e){return"script[async]"+e}function Om(e,n,s){if(n.count++,n.instance===null)switch(n.type){case"style":var u=e.querySelector('style[data-href~="'+$e(s.href)+'"]');if(u)return n.instance=u,le(u),u;var f=m({},s,{"data-href":s.href,"data-precedence":s.precedence,href:null,precedence:null});return u=(e.ownerDocument||e).createElement("style"),le(u),fe(u,"style",f),Ur(u,s.precedence,e),n.instance=u;case"stylesheet":f=Gl(s.href);var g=e.querySelector(Fs(f));if(g)return n.state.loading|=4,n.instance=g,le(g),g;u=mm(s),(f=Ie.get(f))&&Jh(u,f),g=(e.ownerDocument||e).createElement("link"),le(g);var y=g;return y._p=new Promise(function(v,x){y.onload=v,y.onerror=x}),fe(g,"link",u),n.state.loading|=4,Ur(g,s.precedence,e),n.instance=g;case"script":return g=$l(s.src),(f=e.querySelector(Is(g)))?(n.instance=f,le(f),f):(u=s,(f=Ie.get(g))&&(u=m({},s),Fh(u,f)),e=e.ownerDocument||e,f=e.createElement("script"),le(f),fe(f,"link",u),e.head.appendChild(f),n.instance=f);case"void":return null;default:throw Error(l(443,n.type))}else n.type==="stylesheet"&&(n.state.loading&4)===0&&(u=n.instance,n.state.loading|=4,Ur(u,s.precedence,e));return n.instance}function Ur(e,n,s){for(var u=s.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),f=u.length?u[u.length-1]:null,g=f,y=0;y<u.length;y++){var v=u[y];if(v.dataset.precedence===n)g=v;else if(g!==f)break}g?g.parentNode.insertBefore(e,g.nextSibling):(n=s.nodeType===9?s.head:s,n.insertBefore(e,n.firstChild))}function Jh(e,n){e.crossOrigin==null&&(e.crossOrigin=n.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=n.referrerPolicy),e.title==null&&(e.title=n.title)}function Fh(e,n){e.crossOrigin==null&&(e.crossOrigin=n.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=n.referrerPolicy),e.integrity==null&&(e.integrity=n.integrity)}var Br=null;function ym(e,n,s){if(Br===null){var u=new Map,f=Br=new Map;f.set(s,u)}else f=Br,u=f.get(s),u||(u=new Map,f.set(s,u));if(u.has(e))return u;for(u.set(e,null),s=s.getElementsByTagName(e),f=0;f<s.length;f++){var g=s[f];if(!(g[cs]||g[Oe]||e==="link"&&g.getAttribute("rel")==="stylesheet")&&g.namespaceURI!=="http://www.w3.org/2000/svg"){var y=g.getAttribute(n)||"";y=e+y;var v=u.get(y);v?v.push(g):u.set(y,[g])}}return u}function Sm(e,n,s){e=e.ownerDocument||e,e.head.insertBefore(s,n==="title"?e.querySelector("head > title"):null)}function WS(e,n,s){if(s===1||n.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof n.precedence!="string"||typeof n.href!="string"||n.href==="")break;return!0;case"link":if(typeof n.rel!="string"||typeof n.href!="string"||n.href===""||n.onLoad||n.onError)break;switch(n.rel){case"stylesheet":return e=n.disabled,typeof n.precedence=="string"&&e==null;default:return!0}case"script":if(n.async&&typeof n.async!="function"&&typeof n.async!="symbol"&&!n.onLoad&&!n.onError&&n.src&&typeof n.src=="string")return!0}return!1}function vm(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var ta=null;function YS(){}function ZS(e,n,s){if(ta===null)throw Error(l(475));var u=ta;if(n.type==="stylesheet"&&(typeof s.media!="string"||matchMedia(s.media).matches!==!1)&&(n.state.loading&4)===0){if(n.instance===null){var f=Gl(s.href),g=e.querySelector(Fs(f));if(g){e=g._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(u.count++,u=Xr.bind(u),e.then(u,u)),n.state.loading|=4,n.instance=g,le(g);return}g=e.ownerDocument||e,s=mm(s),(f=Ie.get(f))&&Jh(s,f),g=g.createElement("link"),le(g);var y=g;y._p=new Promise(function(v,x){y.onload=v,y.onerror=x}),fe(g,"link",s),n.instance=g}u.stylesheets===null&&(u.stylesheets=new Map),u.stylesheets.set(n,e),(e=n.state.preload)&&(n.state.loading&3)===0&&(u.count++,n=Xr.bind(u),e.addEventListener("load",n),e.addEventListener("error",n))}}function KS(){if(ta===null)throw Error(l(475));var e=ta;return e.stylesheets&&e.count===0&&Ih(e,e.stylesheets),0<e.count?function(n){var s=setTimeout(function(){if(e.stylesheets&&Ih(e,e.stylesheets),e.unsuspend){var u=e.unsuspend;e.unsuspend=null,u()}},6e4);return e.unsuspend=n,function(){e.unsuspend=null,clearTimeout(s)}}:null}function Xr(){if(this.count--,this.count===0){if(this.stylesheets)Ih(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Pr=null;function Ih(e,n){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Pr=new Map,n.forEach(JS,e),Pr=null,Xr.call(e))}function JS(e,n){if(!(n.state.loading&4)){var s=Pr.get(e);if(s)var u=s.get(null);else{s=new Map,Pr.set(e,s);for(var f=e.querySelectorAll("link[data-precedence],style[data-precedence]"),g=0;g<f.length;g++){var y=f[g];(y.nodeName==="LINK"||y.getAttribute("media")!=="not all")&&(s.set(y.dataset.precedence,y),u=y)}u&&s.set(null,u)}f=n.instance,y=f.getAttribute("data-precedence"),g=s.get(y)||u,g===u&&s.set(null,f),s.set(y,f),this.count++,u=Xr.bind(this),f.addEventListener("load",u),f.addEventListener("error",u),g?g.parentNode.insertBefore(f,g.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(f,e.firstChild)),n.state.loading|=4}}var ea={$$typeof:j,Provider:null,Consumer:null,_currentValue:it,_currentValue2:it,_threadCount:0};function FS(e,n,s,u,f,g,y,v){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Zo(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Zo(0),this.hiddenUpdates=Zo(null),this.identifierPrefix=u,this.onUncaughtError=f,this.onCaughtError=g,this.onRecoverableError=y,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function bm(e,n,s,u,f,g,y,v,x,R,_,B){return e=new FS(e,n,s,y,v,x,R,B),n=1,g===!0&&(n|=24),g=Ne(3,null,null,n),e.current=g,g.stateNode=e,n=qu(),n.refCount++,e.pooledCache=n,n.refCount++,g.memoizedState={element:u,isDehydrated:s,cache:n},Bu(g),e}function xm(e){return e?(e=Tl,e):Tl}function Tm(e,n,s,u,f,g){f=xm(f),u.context===null?u.context=f:u.pendingContext=f,u=sn(n),u.payload={element:s},g=g===void 0?null:g,g!==null&&(u.callback=g),s=an(e,u,n),s!==null&&(Ve(s,e,n),Ms(s,e,n))}function wm(e,n){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var s=e.retryLane;e.retryLane=s!==0&&s<n?s:n}}function tf(e,n){wm(e,n),(e=e.alternate)&&wm(e,n)}function Qm(e){if(e.tag===13){var n=xl(e,67108864);n!==null&&Ve(n,e,67108864),tf(e,67108864)}}var Vr=!0;function IS(e,n,s,u){var f=D.T;D.T=null;var g=L.p;try{L.p=2,ef(e,n,s,u)}finally{L.p=g,D.T=f}}function tv(e,n,s,u){var f=D.T;D.T=null;var g=L.p;try{L.p=8,ef(e,n,s,u)}finally{L.p=g,D.T=f}}function ef(e,n,s,u){if(Vr){var f=nf(u);if(f===null)Hh(e,n,u,Hr,s),Am(e,u);else if(iv(f,e,n,s,u))u.stopPropagation();else if(Am(e,u),n&4&&-1<ev.indexOf(e)){for(;f!==null;){var g=hl(f);if(g!==null)switch(g.tag){case 3:if(g=g.stateNode,g.current.memoizedState.isDehydrated){var y=Dn(g.pendingLanes);if(y!==0){var v=g;for(v.pendingLanes|=2,v.entangledLanes|=2;y;){var x=1<<31-qe(y);v.entanglements[1]|=x,y&=~x}di(g),(zt&6)===0&&(Qr=oi()+500,Ys(0))}}break;case 13:v=xl(g,2),v!==null&&Ve(v,g,2),Ar(),tf(g,2)}if(g=nf(u),g===null&&Hh(e,n,u,Hr,s),g===f)break;f=g}f!==null&&u.stopPropagation()}else Hh(e,n,u,null,s)}}function nf(e){return e=ru(e),lf(e)}var Hr=null;function lf(e){if(Hr=null,e=ul(e),e!==null){var n=o(e);if(n===null)e=null;else{var s=n.tag;if(s===13){if(e=h(n),e!==null)return e;e=null}else if(s===3){if(n.stateNode.current.memoizedState.isDehydrated)return n.tag===3?n.stateNode.containerInfo:null;e=null}else n!==e&&(e=null)}}return Hr=e,null}function km(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(P1()){case Pc:return 2;case Vc:return 8;case qa:case V1:return 32;case Hc:return 268435456;default:return 32}default:return 32}}var sf=!1,Sn=null,vn=null,bn=null,ia=new Map,na=new Map,xn=[],ev="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Am(e,n){switch(e){case"focusin":case"focusout":Sn=null;break;case"dragenter":case"dragleave":vn=null;break;case"mouseover":case"mouseout":bn=null;break;case"pointerover":case"pointerout":ia.delete(n.pointerId);break;case"gotpointercapture":case"lostpointercapture":na.delete(n.pointerId)}}function la(e,n,s,u,f,g){return e===null||e.nativeEvent!==g?(e={blockedOn:n,domEventName:s,eventSystemFlags:u,nativeEvent:g,targetContainers:[f]},n!==null&&(n=hl(n),n!==null&&Qm(n)),e):(e.eventSystemFlags|=u,n=e.targetContainers,f!==null&&n.indexOf(f)===-1&&n.push(f),e)}function iv(e,n,s,u,f){switch(n){case"focusin":return Sn=la(Sn,e,n,s,u,f),!0;case"dragenter":return vn=la(vn,e,n,s,u,f),!0;case"mouseover":return bn=la(bn,e,n,s,u,f),!0;case"pointerover":var g=f.pointerId;return ia.set(g,la(ia.get(g)||null,e,n,s,u,f)),!0;case"gotpointercapture":return g=f.pointerId,na.set(g,la(na.get(g)||null,e,n,s,u,f)),!0}return!1}function Rm(e){var n=ul(e.target);if(n!==null){var s=o(n);if(s!==null){if(n=s.tag,n===13){if(n=h(s),n!==null){e.blockedOn=n,Z1(e.priority,function(){if(s.tag===13){var u=Pe();u=Ko(u);var f=xl(s,u);f!==null&&Ve(f,s,u),tf(s,u)}});return}}else if(n===3&&s.stateNode.current.memoizedState.isDehydrated){e.blockedOn=s.tag===3?s.stateNode.containerInfo:null;return}}}e.blockedOn=null}function jr(e){if(e.blockedOn!==null)return!1;for(var n=e.targetContainers;0<n.length;){var s=nf(e.nativeEvent);if(s===null){s=e.nativeEvent;var u=new s.constructor(s.type,s);au=u,s.target.dispatchEvent(u),au=null}else return n=hl(s),n!==null&&Qm(n),e.blockedOn=s,!1;n.shift()}return!0}function Em(e,n,s){jr(e)&&s.delete(n)}function nv(){sf=!1,Sn!==null&&jr(Sn)&&(Sn=null),vn!==null&&jr(vn)&&(vn=null),bn!==null&&jr(bn)&&(bn=null),ia.forEach(Em),na.forEach(Em)}function Lr(e,n){e.blockedOn===n&&(e.blockedOn=null,sf||(sf=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,nv)))}var Gr=null;function zm(e){Gr!==e&&(Gr=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,function(){Gr===e&&(Gr=null);for(var n=0;n<e.length;n+=3){var s=e[n],u=e[n+1],f=e[n+2];if(typeof u!="function"){if(lf(u||s)===null)continue;break}var g=hl(s);g!==null&&(e.splice(n,3),n-=3,nh(g,{pending:!0,data:f,method:s.method,action:u},u,f))}}))}function sa(e){function n(x){return Lr(x,e)}Sn!==null&&Lr(Sn,e),vn!==null&&Lr(vn,e),bn!==null&&Lr(bn,e),ia.forEach(n),na.forEach(n);for(var s=0;s<xn.length;s++){var u=xn[s];u.blockedOn===e&&(u.blockedOn=null)}for(;0<xn.length&&(s=xn[0],s.blockedOn===null);)Rm(s),s.blockedOn===null&&xn.shift();if(s=(e.ownerDocument||e).$$reactFormReplay,s!=null)for(u=0;u<s.length;u+=3){var f=s[u],g=s[u+1],y=f[Qe]||null;if(typeof g=="function")y||zm(s);else if(y){var v=null;if(g&&g.hasAttribute("formAction")){if(f=g,y=g[Qe]||null)v=y.formAction;else if(lf(f)!==null)continue}else v=y.action;typeof v=="function"?s[u+1]=v:(s.splice(u,3),u-=3),zm(s)}}}function af(e){this._internalRoot=e}$r.prototype.render=af.prototype.render=function(e){var n=this._internalRoot;if(n===null)throw Error(l(409));var s=n.current,u=Pe();Tm(s,u,e,n,null,null)},$r.prototype.unmount=af.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var n=e.containerInfo;Tm(e.current,2,null,e,null,null),Ar(),n[ol]=null}};function $r(e){this._internalRoot=e}$r.prototype.unstable_scheduleHydration=function(e){if(e){var n=Wc();e={blockedOn:null,target:e,priority:n};for(var s=0;s<xn.length&&n!==0&&n<xn[s].priority;s++);xn.splice(s,0,e),s===0&&Rm(e)}};var Mm=t.version;if(Mm!=="19.1.0")throw Error(l(527,Mm,"19.1.0"));L.findDOMNode=function(e){var n=e._reactInternals;if(n===void 0)throw typeof e.render=="function"?Error(l(188)):(e=Object.keys(e).join(","),Error(l(268,e)));return e=d(n),e=e!==null?p(e):null,e=e===null?null:e.stateNode,e};var lv={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Wr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Wr.isDisabled&&Wr.supportsFiber)try{us=Wr.inject(lv),De=Wr}catch{}}return ra.createRoot=function(e,n){if(!a(e))throw Error(l(299));var s=!1,u="",f=Wg,g=Yg,y=Zg,v=null;return n!=null&&(n.unstable_strictMode===!0&&(s=!0),n.identifierPrefix!==void 0&&(u=n.identifierPrefix),n.onUncaughtError!==void 0&&(f=n.onUncaughtError),n.onCaughtError!==void 0&&(g=n.onCaughtError),n.onRecoverableError!==void 0&&(y=n.onRecoverableError),n.unstable_transitionCallbacks!==void 0&&(v=n.unstable_transitionCallbacks)),n=bm(e,1,!1,null,null,s,u,f,g,y,v,null),e[ol]=n.current,Vh(e),new af(n)},ra.hydrateRoot=function(e,n,s){if(!a(e))throw Error(l(299));var u=!1,f="",g=Wg,y=Yg,v=Zg,x=null,R=null;return s!=null&&(s.unstable_strictMode===!0&&(u=!0),s.identifierPrefix!==void 0&&(f=s.identifierPrefix),s.onUncaughtError!==void 0&&(g=s.onUncaughtError),s.onCaughtError!==void 0&&(y=s.onCaughtError),s.onRecoverableError!==void 0&&(v=s.onRecoverableError),s.unstable_transitionCallbacks!==void 0&&(x=s.unstable_transitionCallbacks),s.formState!==void 0&&(R=s.formState)),n=bm(e,1,!0,n,s??null,u,f,g,y,v,x,R),n.context=xm(null),s=n.current,u=Pe(),u=Ko(u),f=sn(u),f.callback=null,an(s,f,u),s=u,n.current.lanes=s,fs(n,s),di(n),e[ol]=n.current,Vh(e),new $r(n)},ra.version="19.1.0",ra}var Vm;function gv(){if(Vm)return uf.exports;Vm=1;function r(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(r)}catch(t){console.error(t)}}return r(),uf.exports=dv(),uf.exports}var pv=gv();let _f=[],l0=[];(()=>{let r="lc,34,7n,7,7b,19,,,,2,,2,,,20,b,1c,l,g,,2t,7,2,6,2,2,,4,z,,u,r,2j,b,1m,9,9,,o,4,,9,,3,,5,17,3,3b,f,,w,1j,,,,4,8,4,,3,7,a,2,t,,1m,,,,2,4,8,,9,,a,2,q,,2,2,1l,,4,2,4,2,2,3,3,,u,2,3,,b,2,1l,,4,5,,2,4,,k,2,m,6,,,1m,,,2,,4,8,,7,3,a,2,u,,1n,,,,c,,9,,14,,3,,1l,3,5,3,,4,7,2,b,2,t,,1m,,2,,2,,3,,5,2,7,2,b,2,s,2,1l,2,,,2,4,8,,9,,a,2,t,,20,,4,,2,3,,,8,,29,,2,7,c,8,2q,,2,9,b,6,22,2,r,,,,,,1j,e,,5,,2,5,b,,10,9,,2u,4,,6,,2,2,2,p,2,4,3,g,4,d,,2,2,6,,f,,jj,3,qa,3,t,3,t,2,u,2,1s,2,,7,8,,2,b,9,,19,3,3b,2,y,,3a,3,4,2,9,,6,3,63,2,2,,1m,,,7,,,,,2,8,6,a,2,,1c,h,1r,4,1c,7,,,5,,14,9,c,2,w,4,2,2,,3,1k,,,2,3,,,3,1m,8,2,2,48,3,,d,,7,4,,6,,3,2,5i,1m,,5,ek,,5f,x,2da,3,3x,,2o,w,fe,6,2x,2,n9w,4,,a,w,2,28,2,7k,,3,,4,,p,2,5,,47,2,q,i,d,,12,8,p,b,1a,3,1c,,2,4,2,2,13,,1v,6,2,2,2,2,c,,8,,1b,,1f,,,3,2,2,5,2,,,16,2,8,,6m,,2,,4,,fn4,,kh,g,g,g,a6,2,gt,,6a,,45,5,1ae,3,,2,5,4,14,3,4,,4l,2,fx,4,ar,2,49,b,4w,,1i,f,1k,3,1d,4,2,2,1x,3,10,5,,8,1q,,c,2,1g,9,a,4,2,,2n,3,2,,,2,6,,4g,,3,8,l,2,1l,2,,,,,m,,e,7,3,5,5f,8,2,3,,,n,,29,,2,6,,,2,,,2,,2,6j,,2,4,6,2,,2,r,2,2d,8,2,,,2,2y,,,,2,6,,,2t,3,2,4,,5,77,9,,2,6t,,a,2,,,4,,40,4,2,2,4,,w,a,14,6,2,4,8,,9,6,2,3,1a,d,,2,ba,7,,6,,,2a,m,2,7,,2,,2,3e,6,3,,,2,,7,,,20,2,3,,,,9n,2,f0b,5,1n,7,t4,,1r,4,29,,f5k,2,43q,,,3,4,5,8,8,2,7,u,4,44,3,1iz,1j,4,1e,8,,e,,m,5,,f,11s,7,,h,2,7,,2,,5,79,7,c5,4,15s,7,31,7,240,5,gx7k,2o,3k,6o".split(",").map(t=>t?parseInt(t,36):1);for(let t=0,i=0;t<r.length;t++)(t%2?l0:_f).push(i=i+r[t])})();function mv(r){if(r<768)return!1;for(let t=0,i=_f.length;;){let l=t+i>>1;if(r<_f[l])i=l;else if(r>=l0[l])t=l+1;else return!0;if(t==i)return!1}}function Hm(r){return r>=127462&&r<=127487}const jm=8205;function Ov(r,t,i=!0,l=!0){return(i?s0:yv)(r,t,l)}function s0(r,t,i){if(t==r.length)return t;t&&a0(r.charCodeAt(t))&&r0(r.charCodeAt(t-1))&&t--;let l=df(r,t);for(t+=Lm(l);t<r.length;){let a=df(r,t);if(l==jm||a==jm||i&&mv(a))t+=Lm(a),l=a;else if(Hm(a)){let o=0,h=t-2;for(;h>=0&&Hm(df(r,h));)o++,h-=2;if(o%2==0)break;t+=2}else break}return t}function yv(r,t,i){for(;t>0;){let l=s0(r,t-2,i);if(l<t)return l;t--}return 0}function df(r,t){let i=r.charCodeAt(t);if(!r0(i)||t+1==r.length)return i;let l=r.charCodeAt(t+1);return a0(l)?(i-55296<<10)+(l-56320)+65536:i}function a0(r){return r>=56320&&r<57344}function r0(r){return r>=55296&&r<56320}function Lm(r){return r<65536?1:2}class wt{lineAt(t){if(t<0||t>this.length)throw new RangeError(`Invalid position ${t} in document of length ${this.length}`);return this.lineInner(t,!1,1,0)}line(t){if(t<1||t>this.lines)throw new RangeError(`Invalid line number ${t} in ${this.lines}-line document`);return this.lineInner(t,!0,1,0)}replace(t,i,l){[t,i]=Il(this,t,i);let a=[];return this.decompose(0,t,a,2),l.length&&l.decompose(0,l.length,a,3),this.decompose(i,this.length,a,1),yi.from(a,this.length-(i-t)+l.length)}append(t){return this.replace(this.length,this.length,t)}slice(t,i=this.length){[t,i]=Il(this,t,i);let l=[];return this.decompose(t,i,l,0),yi.from(l,i-t)}eq(t){if(t==this)return!0;if(t.length!=this.length||t.lines!=this.lines)return!1;let i=this.scanIdentical(t,1),l=this.length-this.scanIdentical(t,-1),a=new ma(this),o=new ma(t);for(let h=i,c=i;;){if(a.next(h),o.next(h),h=0,a.lineBreak!=o.lineBreak||a.done!=o.done||a.value!=o.value)return!1;if(c+=a.value.length,a.done||c>=l)return!0}}iter(t=1){return new ma(this,t)}iterRange(t,i=this.length){return new o0(this,t,i)}iterLines(t,i){let l;if(t==null)l=this.iter();else{i==null&&(i=this.lines+1);let a=this.line(t).from;l=this.iterRange(a,Math.max(a,i==this.lines+1?this.length:i<=1?0:this.line(i-1).to))}return new u0(l)}toString(){return this.sliceString(0)}toJSON(){let t=[];return this.flatten(t),t}constructor(){}static of(t){if(t.length==0)throw new RangeError("A document must have at least one line");return t.length==1&&!t[0]?wt.empty:t.length<=32?new Zt(t):yi.from(Zt.split(t,[]))}}class Zt extends wt{constructor(t,i=Sv(t)){super(),this.text=t,this.length=i}get lines(){return this.text.length}get children(){return null}lineInner(t,i,l,a){for(let o=0;;o++){let h=this.text[o],c=a+h.length;if((i?l:c)>=t)return new vv(a,c,l,h);a=c+1,l++}}decompose(t,i,l,a){let o=t<=0&&i>=this.length?this:new Zt(Gm(this.text,t,i),Math.min(i,this.length)-Math.max(0,t));if(a&1){let h=l.pop(),c=co(o.text,h.text.slice(),0,o.length);if(c.length<=32)l.push(new Zt(c,h.length+o.length));else{let d=c.length>>1;l.push(new Zt(c.slice(0,d)),new Zt(c.slice(d)))}}else l.push(o)}replace(t,i,l){if(!(l instanceof Zt))return super.replace(t,i,l);[t,i]=Il(this,t,i);let a=co(this.text,co(l.text,Gm(this.text,0,t)),i),o=this.length+l.length-(i-t);return a.length<=32?new Zt(a,o):yi.from(Zt.split(a,[]),o)}sliceString(t,i=this.length,l=`
`){[t,i]=Il(this,t,i);let a="";for(let o=0,h=0;o<=i&&h<this.text.length;h++){let c=this.text[h],d=o+c.length;o>t&&h&&(a+=l),t<d&&i>o&&(a+=c.slice(Math.max(0,t-o),i-o)),o=d+1}return a}flatten(t){for(let i of this.text)t.push(i)}scanIdentical(){return 0}static split(t,i){let l=[],a=-1;for(let o of t)l.push(o),a+=o.length+1,l.length==32&&(i.push(new Zt(l,a)),l=[],a=-1);return a>-1&&i.push(new Zt(l,a)),i}}class yi extends wt{constructor(t,i){super(),this.children=t,this.length=i,this.lines=0;for(let l of t)this.lines+=l.lines}lineInner(t,i,l,a){for(let o=0;;o++){let h=this.children[o],c=a+h.length,d=l+h.lines-1;if((i?d:c)>=t)return h.lineInner(t,i,l,a);a=c+1,l=d+1}}decompose(t,i,l,a){for(let o=0,h=0;h<=i&&o<this.children.length;o++){let c=this.children[o],d=h+c.length;if(t<=d&&i>=h){let p=a&((h<=t?1:0)|(d>=i?2:0));h>=t&&d<=i&&!p?l.push(c):c.decompose(t-h,i-h,l,p)}h=d+1}}replace(t,i,l){if([t,i]=Il(this,t,i),l.lines<this.lines)for(let a=0,o=0;a<this.children.length;a++){let h=this.children[a],c=o+h.length;if(t>=o&&i<=c){let d=h.replace(t-o,i-o,l),p=this.lines-h.lines+d.lines;if(d.lines<p>>4&&d.lines>p>>6){let m=this.children.slice();return m[a]=d,new yi(m,this.length-(i-t)+l.length)}return super.replace(o,c,d)}o=c+1}return super.replace(t,i,l)}sliceString(t,i=this.length,l=`
`){[t,i]=Il(this,t,i);let a="";for(let o=0,h=0;o<this.children.length&&h<=i;o++){let c=this.children[o],d=h+c.length;h>t&&o&&(a+=l),t<d&&i>h&&(a+=c.sliceString(t-h,i-h,l)),h=d+1}return a}flatten(t){for(let i of this.children)i.flatten(t)}scanIdentical(t,i){if(!(t instanceof yi))return 0;let l=0,[a,o,h,c]=i>0?[0,0,this.children.length,t.children.length]:[this.children.length-1,t.children.length-1,-1,-1];for(;;a+=i,o+=i){if(a==h||o==c)return l;let d=this.children[a],p=t.children[o];if(d!=p)return l+d.scanIdentical(p,i);l+=d.length+1}}static from(t,i=t.reduce((l,a)=>l+a.length+1,-1)){let l=0;for(let b of t)l+=b.lines;if(l<32){let b=[];for(let T of t)T.flatten(b);return new Zt(b,i)}let a=Math.max(32,l>>5),o=a<<1,h=a>>1,c=[],d=0,p=-1,m=[];function O(b){let T;if(b.lines>o&&b instanceof yi)for(let z of b.children)O(z);else b.lines>h&&(d>h||!d)?(S(),c.push(b)):b instanceof Zt&&d&&(T=m[m.length-1])instanceof Zt&&b.lines+T.lines<=32?(d+=b.lines,p+=b.length+1,m[m.length-1]=new Zt(T.text.concat(b.text),T.length+1+b.length)):(d+b.lines>a&&S(),d+=b.lines,p+=b.length+1,m.push(b))}function S(){d!=0&&(c.push(m.length==1?m[0]:yi.from(m,p)),p=-1,d=m.length=0)}for(let b of t)O(b);return S(),c.length==1?c[0]:new yi(c,i)}}wt.empty=new Zt([""],0);function Sv(r){let t=-1;for(let i of r)t+=i.length+1;return t}function co(r,t,i=0,l=1e9){for(let a=0,o=0,h=!0;o<r.length&&a<=l;o++){let c=r[o],d=a+c.length;d>=i&&(d>l&&(c=c.slice(0,l-a)),a<i&&(c=c.slice(i-a)),h?(t[t.length-1]+=c,h=!1):t.push(c)),a=d+1}return t}function Gm(r,t,i){return co(r,[""],t,i)}class ma{constructor(t,i=1){this.dir=i,this.done=!1,this.lineBreak=!1,this.value="",this.nodes=[t],this.offsets=[i>0?1:(t instanceof Zt?t.text.length:t.children.length)<<1]}nextInner(t,i){for(this.done=this.lineBreak=!1;;){let l=this.nodes.length-1,a=this.nodes[l],o=this.offsets[l],h=o>>1,c=a instanceof Zt?a.text.length:a.children.length;if(h==(i>0?c:0)){if(l==0)return this.done=!0,this.value="",this;i>0&&this.offsets[l-1]++,this.nodes.pop(),this.offsets.pop()}else if((o&1)==(i>0?0:1)){if(this.offsets[l]+=i,t==0)return this.lineBreak=!0,this.value=`
`,this;t--}else if(a instanceof Zt){let d=a.text[h+(i<0?-1:0)];if(this.offsets[l]+=i,d.length>Math.max(0,t))return this.value=t==0?d:i>0?d.slice(t):d.slice(0,d.length-t),this;t-=d.length}else{let d=a.children[h+(i<0?-1:0)];t>d.length?(t-=d.length,this.offsets[l]+=i):(i<0&&this.offsets[l]--,this.nodes.push(d),this.offsets.push(i>0?1:(d instanceof Zt?d.text.length:d.children.length)<<1))}}}next(t=0){return t<0&&(this.nextInner(-t,-this.dir),t=this.value.length),this.nextInner(t,this.dir)}}class o0{constructor(t,i,l){this.value="",this.done=!1,this.cursor=new ma(t,i>l?-1:1),this.pos=i>l?t.length:0,this.from=Math.min(i,l),this.to=Math.max(i,l)}nextInner(t,i){if(i<0?this.pos<=this.from:this.pos>=this.to)return this.value="",this.done=!0,this;t+=Math.max(0,i<0?this.pos-this.to:this.from-this.pos);let l=i<0?this.pos-this.from:this.to-this.pos;t>l&&(t=l),l-=t;let{value:a}=this.cursor.next(t);return this.pos+=(a.length+t)*i,this.value=a.length<=l?a:i<0?a.slice(a.length-l):a.slice(0,l),this.done=!this.value,this}next(t=0){return t<0?t=Math.max(t,this.from-this.pos):t>0&&(t=Math.min(t,this.to-this.pos)),this.nextInner(t,this.cursor.dir)}get lineBreak(){return this.cursor.lineBreak&&this.value!=""}}class u0{constructor(t){this.inner=t,this.afterBreak=!0,this.value="",this.done=!1}next(t=0){let{done:i,lineBreak:l,value:a}=this.inner.next(t);return i&&this.afterBreak?(this.value="",this.afterBreak=!1):i?(this.done=!0,this.value=""):l?this.afterBreak?this.value="":(this.afterBreak=!0,this.next()):(this.value=a,this.afterBreak=!1),this}get lineBreak(){return!1}}typeof Symbol<"u"&&(wt.prototype[Symbol.iterator]=function(){return this.iter()},ma.prototype[Symbol.iterator]=o0.prototype[Symbol.iterator]=u0.prototype[Symbol.iterator]=function(){return this});class vv{constructor(t,i,l,a){this.from=t,this.to=i,this.number=l,this.text=a}get length(){return this.to-this.from}}function Il(r,t,i){return t=Math.max(0,Math.min(r.length,t)),[t,Math.max(t,Math.min(r.length,i))]}function Wi(r,t,i=!0,l=!0){return Ov(r,t,i,l)}function bv(r){return r>=56320&&r<57344}function xv(r){return r>=55296&&r<56320}function Tv(r,t){let i=r.charCodeAt(t);if(!xv(i)||t+1==r.length)return i;let l=r.charCodeAt(t+1);return bv(l)?(i-55296<<10)+(l-56320)+65536:i}function wv(r){return r<65536?1:2}const Nf=/\r\n?|\n/;var Te=function(r){return r[r.Simple=0]="Simple",r[r.TrackDel=1]="TrackDel",r[r.TrackBefore=2]="TrackBefore",r[r.TrackAfter=3]="TrackAfter",r}(Te||(Te={}));class Zi{constructor(t){this.sections=t}get length(){let t=0;for(let i=0;i<this.sections.length;i+=2)t+=this.sections[i];return t}get newLength(){let t=0;for(let i=0;i<this.sections.length;i+=2){let l=this.sections[i+1];t+=l<0?this.sections[i]:l}return t}get empty(){return this.sections.length==0||this.sections.length==2&&this.sections[1]<0}iterGaps(t){for(let i=0,l=0,a=0;i<this.sections.length;){let o=this.sections[i++],h=this.sections[i++];h<0?(t(l,a,o),a+=o):a+=h,l+=o}}iterChangedRanges(t,i=!1){Uf(this,t,i)}get invertedDesc(){let t=[];for(let i=0;i<this.sections.length;){let l=this.sections[i++],a=this.sections[i++];a<0?t.push(l,a):t.push(a,l)}return new Zi(t)}composeDesc(t){return this.empty?t:t.empty?this:h0(this,t)}mapDesc(t,i=!1){return t.empty?this:Bf(this,t,i)}mapPos(t,i=-1,l=Te.Simple){let a=0,o=0;for(let h=0;h<this.sections.length;){let c=this.sections[h++],d=this.sections[h++],p=a+c;if(d<0){if(p>t)return o+(t-a);o+=c}else{if(l!=Te.Simple&&p>=t&&(l==Te.TrackDel&&a<t&&p>t||l==Te.TrackBefore&&a<t||l==Te.TrackAfter&&p>t))return null;if(p>t||p==t&&i<0&&!c)return t==a||i<0?o:o+d;o+=d}a=p}if(t>a)throw new RangeError(`Position ${t} is out of range for changeset of length ${a}`);return o}touchesRange(t,i=t){for(let l=0,a=0;l<this.sections.length&&a<=i;){let o=this.sections[l++],h=this.sections[l++],c=a+o;if(h>=0&&a<=i&&c>=t)return a<t&&c>i?"cover":!0;a=c}return!1}toString(){let t="";for(let i=0;i<this.sections.length;){let l=this.sections[i++],a=this.sections[i++];t+=(t?" ":"")+l+(a>=0?":"+a:"")}return t}toJSON(){return this.sections}static fromJSON(t){if(!Array.isArray(t)||t.length%2||t.some(i=>typeof i!="number"))throw new RangeError("Invalid JSON representation of ChangeDesc");return new Zi(t)}static create(t){return new Zi(t)}}class oe extends Zi{constructor(t,i){super(t),this.inserted=i}apply(t){if(this.length!=t.length)throw new RangeError("Applying change set to a document with the wrong length");return Uf(this,(i,l,a,o,h)=>t=t.replace(a,a+(l-i),h),!1),t}mapDesc(t,i=!1){return Bf(this,t,i,!0)}invert(t){let i=this.sections.slice(),l=[];for(let a=0,o=0;a<i.length;a+=2){let h=i[a],c=i[a+1];if(c>=0){i[a]=c,i[a+1]=h;let d=a>>1;for(;l.length<d;)l.push(wt.empty);l.push(h?t.slice(o,o+h):wt.empty)}o+=h}return new oe(i,l)}compose(t){return this.empty?t:t.empty?this:h0(this,t,!0)}map(t,i=!1){return t.empty?this:Bf(this,t,i,!0)}iterChanges(t,i=!1){Uf(this,t,i)}get desc(){return Zi.create(this.sections)}filter(t){let i=[],l=[],a=[],o=new va(this);t:for(let h=0,c=0;;){let d=h==t.length?1e9:t[h++];for(;c<d||c==d&&o.len==0;){if(o.done)break t;let m=Math.min(o.len,d-c);ve(a,m,-1);let O=o.ins==-1?-1:o.off==0?o.ins:0;ve(i,m,O),O>0&&An(l,i,o.text),o.forward(m),c+=m}let p=t[h++];for(;c<p;){if(o.done)break t;let m=Math.min(o.len,p-c);ve(i,m,-1),ve(a,m,o.ins==-1?-1:o.off==0?o.ins:0),o.forward(m),c+=m}}return{changes:new oe(i,l),filtered:Zi.create(a)}}toJSON(){let t=[];for(let i=0;i<this.sections.length;i+=2){let l=this.sections[i],a=this.sections[i+1];a<0?t.push(l):a==0?t.push([l]):t.push([l].concat(this.inserted[i>>1].toJSON()))}return t}static of(t,i,l){let a=[],o=[],h=0,c=null;function d(m=!1){if(!m&&!a.length)return;h<i&&ve(a,i-h,-1);let O=new oe(a,o);c=c?c.compose(O.map(c)):O,a=[],o=[],h=0}function p(m){if(Array.isArray(m))for(let O of m)p(O);else if(m instanceof oe){if(m.length!=i)throw new RangeError(`Mismatched change set length (got ${m.length}, expected ${i})`);d(),c=c?c.compose(m.map(c)):m}else{let{from:O,to:S=O,insert:b}=m;if(O>S||O<0||S>i)throw new RangeError(`Invalid change range ${O} to ${S} (in doc of length ${i})`);let T=b?typeof b=="string"?wt.of(b.split(l||Nf)):b:wt.empty,z=T.length;if(O==S&&z==0)return;O<h&&d(),O>h&&ve(a,O-h,-1),ve(a,S-O,z),An(o,a,T),h=S}}return p(t),d(!c),c}static empty(t){return new oe(t?[t,-1]:[],[])}static fromJSON(t){if(!Array.isArray(t))throw new RangeError("Invalid JSON representation of ChangeSet");let i=[],l=[];for(let a=0;a<t.length;a++){let o=t[a];if(typeof o=="number")i.push(o,-1);else{if(!Array.isArray(o)||typeof o[0]!="number"||o.some((h,c)=>c&&typeof h!="string"))throw new RangeError("Invalid JSON representation of ChangeSet");if(o.length==1)i.push(o[0],0);else{for(;l.length<a;)l.push(wt.empty);l[a]=wt.of(o.slice(1)),i.push(o[0],l[a].length)}}}return new oe(i,l)}static createSet(t,i){return new oe(t,i)}}function ve(r,t,i,l=!1){if(t==0&&i<=0)return;let a=r.length-2;a>=0&&i<=0&&i==r[a+1]?r[a]+=t:a>=0&&t==0&&r[a]==0?r[a+1]+=i:l?(r[a]+=t,r[a+1]+=i):r.push(t,i)}function An(r,t,i){if(i.length==0)return;let l=t.length-2>>1;if(l<r.length)r[r.length-1]=r[r.length-1].append(i);else{for(;r.length<l;)r.push(wt.empty);r.push(i)}}function Uf(r,t,i){let l=r.inserted;for(let a=0,o=0,h=0;h<r.sections.length;){let c=r.sections[h++],d=r.sections[h++];if(d<0)a+=c,o+=c;else{let p=a,m=o,O=wt.empty;for(;p+=c,m+=d,d&&l&&(O=O.append(l[h-2>>1])),!(i||h==r.sections.length||r.sections[h+1]<0);)c=r.sections[h++],d=r.sections[h++];t(a,p,o,m,O),a=p,o=m}}}function Bf(r,t,i,l=!1){let a=[],o=l?[]:null,h=new va(r),c=new va(t);for(let d=-1;;){if(h.done&&c.len||c.done&&h.len)throw new Error("Mismatched change set lengths");if(h.ins==-1&&c.ins==-1){let p=Math.min(h.len,c.len);ve(a,p,-1),h.forward(p),c.forward(p)}else if(c.ins>=0&&(h.ins<0||d==h.i||h.off==0&&(c.len<h.len||c.len==h.len&&!i))){let p=c.len;for(ve(a,c.ins,-1);p;){let m=Math.min(h.len,p);h.ins>=0&&d<h.i&&h.len<=m&&(ve(a,0,h.ins),o&&An(o,a,h.text),d=h.i),h.forward(m),p-=m}c.next()}else if(h.ins>=0){let p=0,m=h.len;for(;m;)if(c.ins==-1){let O=Math.min(m,c.len);p+=O,m-=O,c.forward(O)}else if(c.ins==0&&c.len<m)m-=c.len,c.next();else break;ve(a,p,d<h.i?h.ins:0),o&&d<h.i&&An(o,a,h.text),d=h.i,h.forward(h.len-m)}else{if(h.done&&c.done)return o?oe.createSet(a,o):Zi.create(a);throw new Error("Mismatched change set lengths")}}}function h0(r,t,i=!1){let l=[],a=i?[]:null,o=new va(r),h=new va(t);for(let c=!1;;){if(o.done&&h.done)return a?oe.createSet(l,a):Zi.create(l);if(o.ins==0)ve(l,o.len,0,c),o.next();else if(h.len==0&&!h.done)ve(l,0,h.ins,c),a&&An(a,l,h.text),h.next();else{if(o.done||h.done)throw new Error("Mismatched change set lengths");{let d=Math.min(o.len2,h.len),p=l.length;if(o.ins==-1){let m=h.ins==-1?-1:h.off?0:h.ins;ve(l,d,m,c),a&&m&&An(a,l,h.text)}else h.ins==-1?(ve(l,o.off?0:o.len,d,c),a&&An(a,l,o.textBit(d))):(ve(l,o.off?0:o.len,h.off?0:h.ins,c),a&&!h.off&&An(a,l,h.text));c=(o.ins>d||h.ins>=0&&h.len>d)&&(c||l.length>p),o.forward2(d),h.forward(d)}}}}class va{constructor(t){this.set=t,this.i=0,this.next()}next(){let{sections:t}=this.set;this.i<t.length?(this.len=t[this.i++],this.ins=t[this.i++]):(this.len=0,this.ins=-2),this.off=0}get done(){return this.ins==-2}get len2(){return this.ins<0?this.len:this.ins}get text(){let{inserted:t}=this.set,i=this.i-2>>1;return i>=t.length?wt.empty:t[i]}textBit(t){let{inserted:i}=this.set,l=this.i-2>>1;return l>=i.length&&!t?wt.empty:i[l].slice(this.off,t==null?void 0:this.off+t)}forward(t){t==this.len?this.next():(this.len-=t,this.off+=t)}forward2(t){this.ins==-1?this.forward(t):t==this.ins?this.next():(this.ins-=t,this.off+=t)}}class In{constructor(t,i,l){this.from=t,this.to=i,this.flags=l}get anchor(){return this.flags&32?this.to:this.from}get head(){return this.flags&32?this.from:this.to}get empty(){return this.from==this.to}get assoc(){return this.flags&8?-1:this.flags&16?1:0}get bidiLevel(){let t=this.flags&7;return t==7?null:t}get goalColumn(){let t=this.flags>>6;return t==16777215?void 0:t}map(t,i=-1){let l,a;return this.empty?l=a=t.mapPos(this.from,i):(l=t.mapPos(this.from,1),a=t.mapPos(this.to,-1)),l==this.from&&a==this.to?this:new In(l,a,this.flags)}extend(t,i=t){if(t<=this.anchor&&i>=this.anchor)return I.range(t,i);let l=Math.abs(t-this.anchor)>Math.abs(i-this.anchor)?t:i;return I.range(this.anchor,l)}eq(t,i=!1){return this.anchor==t.anchor&&this.head==t.head&&(!i||!this.empty||this.assoc==t.assoc)}toJSON(){return{anchor:this.anchor,head:this.head}}static fromJSON(t){if(!t||typeof t.anchor!="number"||typeof t.head!="number")throw new RangeError("Invalid JSON representation for SelectionRange");return I.range(t.anchor,t.head)}static create(t,i,l){return new In(t,i,l)}}class I{constructor(t,i){this.ranges=t,this.mainIndex=i}map(t,i=-1){return t.empty?this:I.create(this.ranges.map(l=>l.map(t,i)),this.mainIndex)}eq(t,i=!1){if(this.ranges.length!=t.ranges.length||this.mainIndex!=t.mainIndex)return!1;for(let l=0;l<this.ranges.length;l++)if(!this.ranges[l].eq(t.ranges[l],i))return!1;return!0}get main(){return this.ranges[this.mainIndex]}asSingle(){return this.ranges.length==1?this:new I([this.main],0)}addRange(t,i=!0){return I.create([t].concat(this.ranges),i?0:this.mainIndex+1)}replaceRange(t,i=this.mainIndex){let l=this.ranges.slice();return l[i]=t,I.create(l,this.mainIndex)}toJSON(){return{ranges:this.ranges.map(t=>t.toJSON()),main:this.mainIndex}}static fromJSON(t){if(!t||!Array.isArray(t.ranges)||typeof t.main!="number"||t.main>=t.ranges.length)throw new RangeError("Invalid JSON representation for EditorSelection");return new I(t.ranges.map(i=>In.fromJSON(i)),t.main)}static single(t,i=t){return new I([I.range(t,i)],0)}static create(t,i=0){if(t.length==0)throw new RangeError("A selection needs at least one range");for(let l=0,a=0;a<t.length;a++){let o=t[a];if(o.empty?o.from<=l:o.from<l)return I.normalized(t.slice(),i);l=o.to}return new I(t,i)}static cursor(t,i=0,l,a){return In.create(t,t,(i==0?0:i<0?8:16)|(l==null?7:Math.min(6,l))|(a??16777215)<<6)}static range(t,i,l,a){let o=(l??16777215)<<6|(a==null?7:Math.min(6,a));return i<t?In.create(i,t,48|o):In.create(t,i,(i>t?8:0)|o)}static normalized(t,i=0){let l=t[i];t.sort((a,o)=>a.from-o.from),i=t.indexOf(l);for(let a=1;a<t.length;a++){let o=t[a],h=t[a-1];if(o.empty?o.from<=h.to:o.from<h.to){let c=h.from,d=Math.max(o.to,h.to);a<=i&&i--,t.splice(--a,2,o.anchor>o.head?I.range(d,c):I.range(c,d))}}return new I(t,i)}}function f0(r,t){for(let i of r.ranges)if(i.to>t)throw new RangeError("Selection points outside of document")}let yc=0;class ut{constructor(t,i,l,a,o){this.combine=t,this.compareInput=i,this.compare=l,this.isStatic=a,this.id=yc++,this.default=t([]),this.extensions=typeof o=="function"?o(this):o}get reader(){return this}static define(t={}){return new ut(t.combine||(i=>i),t.compareInput||((i,l)=>i===l),t.compare||(t.combine?(i,l)=>i===l:Sc),!!t.static,t.enables)}of(t){return new go([],this,0,t)}compute(t,i){if(this.isStatic)throw new Error("Can't compute a static facet");return new go(t,this,1,i)}computeN(t,i){if(this.isStatic)throw new Error("Can't compute a static facet");return new go(t,this,2,i)}from(t,i){return i||(i=l=>l),this.compute([t],l=>i(l.field(t)))}}function Sc(r,t){return r==t||r.length==t.length&&r.every((i,l)=>i===t[l])}class go{constructor(t,i,l,a){this.dependencies=t,this.facet=i,this.type=l,this.value=a,this.id=yc++}dynamicSlot(t){var i;let l=this.value,a=this.facet.compareInput,o=this.id,h=t[o]>>1,c=this.type==2,d=!1,p=!1,m=[];for(let O of this.dependencies)O=="doc"?d=!0:O=="selection"?p=!0:(((i=t[O.id])!==null&&i!==void 0?i:1)&1)==0&&m.push(t[O.id]);return{create(O){return O.values[h]=l(O),1},update(O,S){if(d&&S.docChanged||p&&(S.docChanged||S.selection)||Xf(O,m)){let b=l(O);if(c?!$m(b,O.values[h],a):!a(b,O.values[h]))return O.values[h]=b,1}return 0},reconfigure:(O,S)=>{let b,T=S.config.address[o];if(T!=null){let z=wo(S,T);if(this.dependencies.every(C=>C instanceof ut?S.facet(C)===O.facet(C):C instanceof Cn?S.field(C,!1)==O.field(C,!1):!0)||(c?$m(b=l(O),z,a):a(b=l(O),z)))return O.values[h]=z,0}else b=l(O);return O.values[h]=b,1}}}}function $m(r,t,i){if(r.length!=t.length)return!1;for(let l=0;l<r.length;l++)if(!i(r[l],t[l]))return!1;return!0}function Xf(r,t){let i=!1;for(let l of t)Oa(r,l)&1&&(i=!0);return i}function Qv(r,t,i){let l=i.map(d=>r[d.id]),a=i.map(d=>d.type),o=l.filter(d=>!(d&1)),h=r[t.id]>>1;function c(d){let p=[];for(let m=0;m<l.length;m++){let O=wo(d,l[m]);if(a[m]==2)for(let S of O)p.push(S);else p.push(O)}return t.combine(p)}return{create(d){for(let p of l)Oa(d,p);return d.values[h]=c(d),1},update(d,p){if(!Xf(d,o))return 0;let m=c(d);return t.compare(m,d.values[h])?0:(d.values[h]=m,1)},reconfigure(d,p){let m=Xf(d,l),O=p.config.facets[t.id],S=p.facet(t);if(O&&!m&&Sc(i,O))return d.values[h]=S,0;let b=c(d);return t.compare(b,S)?(d.values[h]=S,0):(d.values[h]=b,1)}}}const Yr=ut.define({static:!0});class Cn{constructor(t,i,l,a,o){this.id=t,this.createF=i,this.updateF=l,this.compareF=a,this.spec=o,this.provides=void 0}static define(t){let i=new Cn(yc++,t.create,t.update,t.compare||((l,a)=>l===a),t);return t.provide&&(i.provides=t.provide(i)),i}create(t){let i=t.facet(Yr).find(l=>l.field==this);return((i==null?void 0:i.create)||this.createF)(t)}slot(t){let i=t[this.id]>>1;return{create:l=>(l.values[i]=this.create(l),1),update:(l,a)=>{let o=l.values[i],h=this.updateF(o,a);return this.compareF(o,h)?0:(l.values[i]=h,1)},reconfigure:(l,a)=>{let o=l.facet(Yr),h=a.facet(Yr),c;return(c=o.find(d=>d.field==this))&&c!=h.find(d=>d.field==this)?(l.values[i]=c.create(l),1):a.config.address[this.id]!=null?(l.values[i]=a.field(this),0):(l.values[i]=this.create(l),1)}}}init(t){return[this,Yr.of({field:this,create:t})]}get extension(){return this}}const Fn={lowest:4,low:3,default:2,high:1,highest:0};function oa(r){return t=>new c0(t,r)}const vc={highest:oa(Fn.highest),high:oa(Fn.high),default:oa(Fn.default),low:oa(Fn.low),lowest:oa(Fn.lowest)};class c0{constructor(t,i){this.inner=t,this.prec=i}}class Bo{of(t){return new Pf(this,t)}reconfigure(t){return Bo.reconfigure.of({compartment:this,extension:t})}get(t){return t.config.compartments.get(this)}}class Pf{constructor(t,i){this.compartment=t,this.inner=i}}class To{constructor(t,i,l,a,o,h){for(this.base=t,this.compartments=i,this.dynamicSlots=l,this.address=a,this.staticValues=o,this.facets=h,this.statusTemplate=[];this.statusTemplate.length<l.length;)this.statusTemplate.push(0)}staticFacet(t){let i=this.address[t.id];return i==null?t.default:this.staticValues[i>>1]}static resolve(t,i,l){let a=[],o=Object.create(null),h=new Map;for(let S of kv(t,i,h))S instanceof Cn?a.push(S):(o[S.facet.id]||(o[S.facet.id]=[])).push(S);let c=Object.create(null),d=[],p=[];for(let S of a)c[S.id]=p.length<<1,p.push(b=>S.slot(b));let m=l==null?void 0:l.config.facets;for(let S in o){let b=o[S],T=b[0].facet,z=m&&m[S]||[];if(b.every(C=>C.type==0))if(c[T.id]=d.length<<1|1,Sc(z,b))d.push(l.facet(T));else{let C=T.combine(b.map(q=>q.value));d.push(l&&T.compare(C,l.facet(T))?l.facet(T):C)}else{for(let C of b)C.type==0?(c[C.id]=d.length<<1|1,d.push(C.value)):(c[C.id]=p.length<<1,p.push(q=>C.dynamicSlot(q)));c[T.id]=p.length<<1,p.push(C=>Qv(C,T,b))}}let O=p.map(S=>S(c));return new To(t,h,O,c,d,o)}}function kv(r,t,i){let l=[[],[],[],[],[]],a=new Map;function o(h,c){let d=a.get(h);if(d!=null){if(d<=c)return;let p=l[d].indexOf(h);p>-1&&l[d].splice(p,1),h instanceof Pf&&i.delete(h.compartment)}if(a.set(h,c),Array.isArray(h))for(let p of h)o(p,c);else if(h instanceof Pf){if(i.has(h.compartment))throw new RangeError("Duplicate use of compartment in extensions");let p=t.get(h.compartment)||h.inner;i.set(h.compartment,p),o(p,c)}else if(h instanceof c0)o(h.inner,h.prec);else if(h instanceof Cn)l[c].push(h),h.provides&&o(h.provides,c);else if(h instanceof go)l[c].push(h),h.facet.extensions&&o(h.facet.extensions,Fn.default);else{let p=h.extension;if(!p)throw new Error(`Unrecognized extension value in extension set (${h}). This sometimes happens because multiple instances of @codemirror/state are loaded, breaking instanceof checks.`);o(p,c)}}return o(r,Fn.default),l.reduce((h,c)=>h.concat(c))}function Oa(r,t){if(t&1)return 2;let i=t>>1,l=r.status[i];if(l==4)throw new Error("Cyclic dependency between fields and/or facets");if(l&2)return l;r.status[i]=4;let a=r.computeSlot(r,r.config.dynamicSlots[i]);return r.status[i]=2|a}function wo(r,t){return t&1?r.config.staticValues[t>>1]:r.values[t>>1]}const d0=ut.define(),Vf=ut.define({combine:r=>r.some(t=>t),static:!0}),g0=ut.define({combine:r=>r.length?r[0]:void 0,static:!0}),p0=ut.define(),m0=ut.define(),O0=ut.define(),y0=ut.define({combine:r=>r.length?r[0]:!1});class rl{constructor(t,i){this.type=t,this.value=i}static define(){return new Av}}class Av{of(t){return new rl(this,t)}}class Rv{constructor(t){this.map=t}of(t){return new $t(this,t)}}class $t{constructor(t,i){this.type=t,this.value=i}map(t){let i=this.type.map(this.value,t);return i===void 0?void 0:i==this.value?this:new $t(this.type,i)}is(t){return this.type==t}static define(t={}){return new Rv(t.map||(i=>i))}static mapEffects(t,i){if(!t.length)return t;let l=[];for(let a of t){let o=a.map(i);o&&l.push(o)}return l}}$t.reconfigure=$t.define();$t.appendConfig=$t.define();class ge{constructor(t,i,l,a,o,h){this.startState=t,this.changes=i,this.selection=l,this.effects=a,this.annotations=o,this.scrollIntoView=h,this._doc=null,this._state=null,l&&f0(l,i.newLength),o.some(c=>c.type==ge.time)||(this.annotations=o.concat(ge.time.of(Date.now())))}static create(t,i,l,a,o,h){return new ge(t,i,l,a,o,h)}get newDoc(){return this._doc||(this._doc=this.changes.apply(this.startState.doc))}get newSelection(){return this.selection||this.startState.selection.map(this.changes)}get state(){return this._state||this.startState.applyTransaction(this),this._state}annotation(t){for(let i of this.annotations)if(i.type==t)return i.value}get docChanged(){return!this.changes.empty}get reconfigured(){return this.startState.config!=this.state.config}isUserEvent(t){let i=this.annotation(ge.userEvent);return!!(i&&(i==t||i.length>t.length&&i.slice(0,t.length)==t&&i[t.length]=="."))}}ge.time=rl.define();ge.userEvent=rl.define();ge.addToHistory=rl.define();ge.remote=rl.define();function Ev(r,t){let i=[];for(let l=0,a=0;;){let o,h;if(l<r.length&&(a==t.length||t[a]>=r[l]))o=r[l++],h=r[l++];else if(a<t.length)o=t[a++],h=t[a++];else return i;!i.length||i[i.length-1]<o?i.push(o,h):i[i.length-1]<h&&(i[i.length-1]=h)}}function S0(r,t,i){var l;let a,o,h;return i?(a=t.changes,o=oe.empty(t.changes.length),h=r.changes.compose(t.changes)):(a=t.changes.map(r.changes),o=r.changes.mapDesc(t.changes,!0),h=r.changes.compose(a)),{changes:h,selection:t.selection?t.selection.map(o):(l=r.selection)===null||l===void 0?void 0:l.map(a),effects:$t.mapEffects(r.effects,a).concat($t.mapEffects(t.effects,o)),annotations:r.annotations.length?r.annotations.concat(t.annotations):t.annotations,scrollIntoView:r.scrollIntoView||t.scrollIntoView}}function Hf(r,t,i){let l=t.selection,a=Zl(t.annotations);return t.userEvent&&(a=a.concat(ge.userEvent.of(t.userEvent))),{changes:t.changes instanceof oe?t.changes:oe.of(t.changes||[],i,r.facet(g0)),selection:l&&(l instanceof I?l:I.single(l.anchor,l.head)),effects:Zl(t.effects),annotations:a,scrollIntoView:!!t.scrollIntoView}}function v0(r,t,i){let l=Hf(r,t.length?t[0]:{},r.doc.length);t.length&&t[0].filter===!1&&(i=!1);for(let o=1;o<t.length;o++){t[o].filter===!1&&(i=!1);let h=!!t[o].sequential;l=S0(l,Hf(r,t[o],h?l.changes.newLength:r.doc.length),h)}let a=ge.create(r,l.changes,l.selection,l.effects,l.annotations,l.scrollIntoView);return Mv(i?zv(a):a)}function zv(r){let t=r.startState,i=!0;for(let a of t.facet(p0)){let o=a(r);if(o===!1){i=!1;break}Array.isArray(o)&&(i=i===!0?o:Ev(i,o))}if(i!==!0){let a,o;if(i===!1)o=r.changes.invertedDesc,a=oe.empty(t.doc.length);else{let h=r.changes.filter(i);a=h.changes,o=h.filtered.mapDesc(h.changes).invertedDesc}r=ge.create(t,a,r.selection&&r.selection.map(o),$t.mapEffects(r.effects,o),r.annotations,r.scrollIntoView)}let l=t.facet(m0);for(let a=l.length-1;a>=0;a--){let o=l[a](r);o instanceof ge?r=o:Array.isArray(o)&&o.length==1&&o[0]instanceof ge?r=o[0]:r=v0(t,Zl(o),!1)}return r}function Mv(r){let t=r.startState,i=t.facet(O0),l=r;for(let a=i.length-1;a>=0;a--){let o=i[a](r);o&&Object.keys(o).length&&(l=S0(l,Hf(t,o,r.changes.newLength),!0))}return l==r?r:ge.create(t,r.changes,r.selection,l.effects,l.annotations,l.scrollIntoView)}const Cv=[];function Zl(r){return r==null?Cv:Array.isArray(r)?r:[r]}var Yi=function(r){return r[r.Word=0]="Word",r[r.Space=1]="Space",r[r.Other=2]="Other",r}(Yi||(Yi={}));const Dv=/[\u00df\u0587\u0590-\u05f4\u0600-\u06ff\u3040-\u309f\u30a0-\u30ff\u3400-\u4db5\u4e00-\u9fcc\uac00-\ud7af]/;let jf;try{jf=new RegExp("[\\p{Alphabetic}\\p{Number}_]","u")}catch{}function qv(r){if(jf)return jf.test(r);for(let t=0;t<r.length;t++){let i=r[t];if(/\w/.test(i)||i>""&&(i.toUpperCase()!=i.toLowerCase()||Dv.test(i)))return!0}return!1}function _v(r){return t=>{if(!/\S/.test(t))return Yi.Space;if(qv(t))return Yi.Word;for(let i=0;i<r.length;i++)if(t.indexOf(r[i])>-1)return Yi.Word;return Yi.Other}}class At{constructor(t,i,l,a,o,h){this.config=t,this.doc=i,this.selection=l,this.values=a,this.status=t.statusTemplate.slice(),this.computeSlot=o,h&&(h._state=this);for(let c=0;c<this.config.dynamicSlots.length;c++)Oa(this,c<<1);this.computeSlot=null}field(t,i=!0){let l=this.config.address[t.id];if(l==null){if(i)throw new RangeError("Field is not present in this state");return}return Oa(this,l),wo(this,l)}update(...t){return v0(this,t,!0)}applyTransaction(t){let i=this.config,{base:l,compartments:a}=i;for(let c of t.effects)c.is(Bo.reconfigure)?(i&&(a=new Map,i.compartments.forEach((d,p)=>a.set(p,d)),i=null),a.set(c.value.compartment,c.value.extension)):c.is($t.reconfigure)?(i=null,l=c.value):c.is($t.appendConfig)&&(i=null,l=Zl(l).concat(c.value));let o;i?o=t.startState.values.slice():(i=To.resolve(l,a,this),o=new At(i,this.doc,this.selection,i.dynamicSlots.map(()=>null),(d,p)=>p.reconfigure(d,this),null).values);let h=t.startState.facet(Vf)?t.newSelection:t.newSelection.asSingle();new At(i,t.newDoc,h,o,(c,d)=>d.update(c,t),t)}replaceSelection(t){return typeof t=="string"&&(t=this.toText(t)),this.changeByRange(i=>({changes:{from:i.from,to:i.to,insert:t},range:I.cursor(i.from+t.length)}))}changeByRange(t){let i=this.selection,l=t(i.ranges[0]),a=this.changes(l.changes),o=[l.range],h=Zl(l.effects);for(let c=1;c<i.ranges.length;c++){let d=t(i.ranges[c]),p=this.changes(d.changes),m=p.map(a);for(let S=0;S<c;S++)o[S]=o[S].map(m);let O=a.mapDesc(p,!0);o.push(d.range.map(O)),a=a.compose(m),h=$t.mapEffects(h,m).concat($t.mapEffects(Zl(d.effects),O))}return{changes:a,selection:I.create(o,i.mainIndex),effects:h}}changes(t=[]){return t instanceof oe?t:oe.of(t,this.doc.length,this.facet(At.lineSeparator))}toText(t){return wt.of(t.split(this.facet(At.lineSeparator)||Nf))}sliceDoc(t=0,i=this.doc.length){return this.doc.sliceString(t,i,this.lineBreak)}facet(t){let i=this.config.address[t.id];return i==null?t.default:(Oa(this,i),wo(this,i))}toJSON(t){let i={doc:this.sliceDoc(),selection:this.selection.toJSON()};if(t)for(let l in t){let a=t[l];a instanceof Cn&&this.config.address[a.id]!=null&&(i[l]=a.spec.toJSON(this.field(t[l]),this))}return i}static fromJSON(t,i={},l){if(!t||typeof t.doc!="string")throw new RangeError("Invalid JSON representation for EditorState");let a=[];if(l){for(let o in l)if(Object.prototype.hasOwnProperty.call(t,o)){let h=l[o],c=t[o];a.push(h.init(d=>h.spec.fromJSON(c,d)))}}return At.create({doc:t.doc,selection:I.fromJSON(t.selection),extensions:i.extensions?a.concat([i.extensions]):a})}static create(t={}){let i=To.resolve(t.extensions||[],new Map),l=t.doc instanceof wt?t.doc:wt.of((t.doc||"").split(i.staticFacet(At.lineSeparator)||Nf)),a=t.selection?t.selection instanceof I?t.selection:I.single(t.selection.anchor,t.selection.head):I.single(0);return f0(a,l.length),i.staticFacet(Vf)||(a=a.asSingle()),new At(i,l,a,i.dynamicSlots.map(()=>null),(o,h)=>h.create(o),null)}get tabSize(){return this.facet(At.tabSize)}get lineBreak(){return this.facet(At.lineSeparator)||`
`}get readOnly(){return this.facet(y0)}phrase(t,...i){for(let l of this.facet(At.phrases))if(Object.prototype.hasOwnProperty.call(l,t)){t=l[t];break}return i.length&&(t=t.replace(/\$(\$|\d*)/g,(l,a)=>{if(a=="$")return"$";let o=+(a||1);return!o||o>i.length?l:i[o-1]})),t}languageDataAt(t,i,l=-1){let a=[];for(let o of this.facet(d0))for(let h of o(this,i,l))Object.prototype.hasOwnProperty.call(h,t)&&a.push(h[t]);return a}charCategorizer(t){return _v(this.languageDataAt("wordChars",t).join(""))}wordAt(t){let{text:i,from:l,length:a}=this.doc.lineAt(t),o=this.charCategorizer(t),h=t-l,c=t-l;for(;h>0;){let d=Wi(i,h,!1);if(o(i.slice(d,h))!=Yi.Word)break;h=d}for(;c<a;){let d=Wi(i,c);if(o(i.slice(c,d))!=Yi.Word)break;c=d}return h==c?null:I.range(h+l,c+l)}}At.allowMultipleSelections=Vf;At.tabSize=ut.define({combine:r=>r.length?r[0]:4});At.lineSeparator=g0;At.readOnly=y0;At.phrases=ut.define({compare(r,t){let i=Object.keys(r),l=Object.keys(t);return i.length==l.length&&i.every(a=>r[a]==t[a])}});At.languageData=d0;At.changeFilter=p0;At.transactionFilter=m0;At.transactionExtender=O0;Bo.reconfigure=$t.define();class il{eq(t){return this==t}range(t,i=t){return Lf.create(t,i,this)}}il.prototype.startSide=il.prototype.endSide=0;il.prototype.point=!1;il.prototype.mapMode=Te.TrackDel;let Lf=class b0{constructor(t,i,l){this.from=t,this.to=i,this.value=l}static create(t,i,l){return new b0(t,i,l)}};function Gf(r,t){return r.from-t.from||r.value.startSide-t.value.startSide}class bc{constructor(t,i,l,a){this.from=t,this.to=i,this.value=l,this.maxPoint=a}get length(){return this.to[this.to.length-1]}findIndex(t,i,l,a=0){let o=l?this.to:this.from;for(let h=a,c=o.length;;){if(h==c)return h;let d=h+c>>1,p=o[d]-t||(l?this.value[d].endSide:this.value[d].startSide)-i;if(d==h)return p>=0?h:c;p>=0?c=d:h=d+1}}between(t,i,l,a){for(let o=this.findIndex(i,-1e9,!0),h=this.findIndex(l,1e9,!1,o);o<h;o++)if(a(this.from[o]+t,this.to[o]+t,this.value[o])===!1)return!1}map(t,i){let l=[],a=[],o=[],h=-1,c=-1;for(let d=0;d<this.value.length;d++){let p=this.value[d],m=this.from[d]+t,O=this.to[d]+t,S,b;if(m==O){let T=i.mapPos(m,p.startSide,p.mapMode);if(T==null||(S=b=T,p.startSide!=p.endSide&&(b=i.mapPos(m,p.endSide),b<S)))continue}else if(S=i.mapPos(m,p.startSide),b=i.mapPos(O,p.endSide),S>b||S==b&&p.startSide>0&&p.endSide<=0)continue;(b-S||p.endSide-p.startSide)<0||(h<0&&(h=S),p.point&&(c=Math.max(c,b-S)),l.push(p),a.push(S-h),o.push(b-h))}return{mapped:l.length?new bc(a,o,l,c):null,pos:h}}}class Pt{constructor(t,i,l,a){this.chunkPos=t,this.chunk=i,this.nextLayer=l,this.maxPoint=a}static create(t,i,l,a){return new Pt(t,i,l,a)}get length(){let t=this.chunk.length-1;return t<0?0:Math.max(this.chunkEnd(t),this.nextLayer.length)}get size(){if(this.isEmpty)return 0;let t=this.nextLayer.size;for(let i of this.chunk)t+=i.value.length;return t}chunkEnd(t){return this.chunkPos[t]+this.chunk[t].length}update(t){let{add:i=[],sort:l=!1,filterFrom:a=0,filterTo:o=this.length}=t,h=t.filter;if(i.length==0&&!h)return this;if(l&&(i=i.slice().sort(Gf)),this.isEmpty)return i.length?Pt.of(i):this;let c=new x0(this,null,-1).goto(0),d=0,p=[],m=new Qo;for(;c.value||d<i.length;)if(d<i.length&&(c.from-i[d].from||c.startSide-i[d].value.startSide)>=0){let O=i[d++];m.addInner(O.from,O.to,O.value)||p.push(O)}else c.rangeIndex==1&&c.chunkIndex<this.chunk.length&&(d==i.length||this.chunkEnd(c.chunkIndex)<i[d].from)&&(!h||a>this.chunkEnd(c.chunkIndex)||o<this.chunkPos[c.chunkIndex])&&m.addChunk(this.chunkPos[c.chunkIndex],this.chunk[c.chunkIndex])?c.nextChunk():((!h||a>c.to||o<c.from||h(c.from,c.to,c.value))&&(m.addInner(c.from,c.to,c.value)||p.push(Lf.create(c.from,c.to,c.value))),c.next());return m.finishInner(this.nextLayer.isEmpty&&!p.length?Pt.empty:this.nextLayer.update({add:p,filter:h,filterFrom:a,filterTo:o}))}map(t){if(t.empty||this.isEmpty)return this;let i=[],l=[],a=-1;for(let h=0;h<this.chunk.length;h++){let c=this.chunkPos[h],d=this.chunk[h],p=t.touchesRange(c,c+d.length);if(p===!1)a=Math.max(a,d.maxPoint),i.push(d),l.push(t.mapPos(c));else if(p===!0){let{mapped:m,pos:O}=d.map(c,t);m&&(a=Math.max(a,m.maxPoint),i.push(m),l.push(O))}}let o=this.nextLayer.map(t);return i.length==0?o:new Pt(l,i,o||Pt.empty,a)}between(t,i,l){if(!this.isEmpty){for(let a=0;a<this.chunk.length;a++){let o=this.chunkPos[a],h=this.chunk[a];if(i>=o&&t<=o+h.length&&h.between(o,t-o,i-o,l)===!1)return}this.nextLayer.between(t,i,l)}}iter(t=0){return ba.from([this]).goto(t)}get isEmpty(){return this.nextLayer==this}static iter(t,i=0){return ba.from(t).goto(i)}static compare(t,i,l,a,o=-1){let h=t.filter(O=>O.maxPoint>0||!O.isEmpty&&O.maxPoint>=o),c=i.filter(O=>O.maxPoint>0||!O.isEmpty&&O.maxPoint>=o),d=Wm(h,c,l),p=new ua(h,d,o),m=new ua(c,d,o);l.iterGaps((O,S,b)=>Ym(p,O,m,S,b,a)),l.empty&&l.length==0&&Ym(p,0,m,0,0,a)}static eq(t,i,l=0,a){a==null&&(a=999999999);let o=t.filter(m=>!m.isEmpty&&i.indexOf(m)<0),h=i.filter(m=>!m.isEmpty&&t.indexOf(m)<0);if(o.length!=h.length)return!1;if(!o.length)return!0;let c=Wm(o,h),d=new ua(o,c,0).goto(l),p=new ua(h,c,0).goto(l);for(;;){if(d.to!=p.to||!$f(d.active,p.active)||d.point&&(!p.point||!d.point.eq(p.point)))return!1;if(d.to>a)return!0;d.next(),p.next()}}static spans(t,i,l,a,o=-1){let h=new ua(t,null,o).goto(i),c=i,d=h.openStart;for(;;){let p=Math.min(h.to,l);if(h.point){let m=h.activeForPoint(h.to),O=h.pointFrom<i?m.length+1:h.point.startSide<0?m.length:Math.min(m.length,d);a.point(c,p,h.point,m,O,h.pointRank),d=Math.min(h.openEnd(p),m.length)}else p>c&&(a.span(c,p,h.active,d),d=h.openEnd(p));if(h.to>l)return d+(h.point&&h.to>l?1:0);c=h.to,h.next()}}static of(t,i=!1){let l=new Qo;for(let a of t instanceof Lf?[t]:i?Nv(t):t)l.add(a.from,a.to,a.value);return l.finish()}static join(t){if(!t.length)return Pt.empty;let i=t[t.length-1];for(let l=t.length-2;l>=0;l--)for(let a=t[l];a!=Pt.empty;a=a.nextLayer)i=new Pt(a.chunkPos,a.chunk,i,Math.max(a.maxPoint,i.maxPoint));return i}}Pt.empty=new Pt([],[],null,-1);function Nv(r){if(r.length>1)for(let t=r[0],i=1;i<r.length;i++){let l=r[i];if(Gf(t,l)>0)return r.slice().sort(Gf);t=l}return r}Pt.empty.nextLayer=Pt.empty;class Qo{finishChunk(t){this.chunks.push(new bc(this.from,this.to,this.value,this.maxPoint)),this.chunkPos.push(this.chunkStart),this.chunkStart=-1,this.setMaxPoint=Math.max(this.setMaxPoint,this.maxPoint),this.maxPoint=-1,t&&(this.from=[],this.to=[],this.value=[])}constructor(){this.chunks=[],this.chunkPos=[],this.chunkStart=-1,this.last=null,this.lastFrom=-1e9,this.lastTo=-1e9,this.from=[],this.to=[],this.value=[],this.maxPoint=-1,this.setMaxPoint=-1,this.nextLayer=null}add(t,i,l){this.addInner(t,i,l)||(this.nextLayer||(this.nextLayer=new Qo)).add(t,i,l)}addInner(t,i,l){let a=t-this.lastTo||l.startSide-this.last.endSide;if(a<=0&&(t-this.lastFrom||l.startSide-this.last.startSide)<0)throw new Error("Ranges must be added sorted by `from` position and `startSide`");return a<0?!1:(this.from.length==250&&this.finishChunk(!0),this.chunkStart<0&&(this.chunkStart=t),this.from.push(t-this.chunkStart),this.to.push(i-this.chunkStart),this.last=l,this.lastFrom=t,this.lastTo=i,this.value.push(l),l.point&&(this.maxPoint=Math.max(this.maxPoint,i-t)),!0)}addChunk(t,i){if((t-this.lastTo||i.value[0].startSide-this.last.endSide)<0)return!1;this.from.length&&this.finishChunk(!0),this.setMaxPoint=Math.max(this.setMaxPoint,i.maxPoint),this.chunks.push(i),this.chunkPos.push(t);let l=i.value.length-1;return this.last=i.value[l],this.lastFrom=i.from[l]+t,this.lastTo=i.to[l]+t,!0}finish(){return this.finishInner(Pt.empty)}finishInner(t){if(this.from.length&&this.finishChunk(!1),this.chunks.length==0)return t;let i=Pt.create(this.chunkPos,this.chunks,this.nextLayer?this.nextLayer.finishInner(t):t,this.setMaxPoint);return this.from=null,i}}function Wm(r,t,i){let l=new Map;for(let o of r)for(let h=0;h<o.chunk.length;h++)o.chunk[h].maxPoint<=0&&l.set(o.chunk[h],o.chunkPos[h]);let a=new Set;for(let o of t)for(let h=0;h<o.chunk.length;h++){let c=l.get(o.chunk[h]);c!=null&&(i?i.mapPos(c):c)==o.chunkPos[h]&&!(i!=null&&i.touchesRange(c,c+o.chunk[h].length))&&a.add(o.chunk[h])}return a}class x0{constructor(t,i,l,a=0){this.layer=t,this.skip=i,this.minPoint=l,this.rank=a}get startSide(){return this.value?this.value.startSide:0}get endSide(){return this.value?this.value.endSide:0}goto(t,i=-1e9){return this.chunkIndex=this.rangeIndex=0,this.gotoInner(t,i,!1),this}gotoInner(t,i,l){for(;this.chunkIndex<this.layer.chunk.length;){let a=this.layer.chunk[this.chunkIndex];if(!(this.skip&&this.skip.has(a)||this.layer.chunkEnd(this.chunkIndex)<t||a.maxPoint<this.minPoint))break;this.chunkIndex++,l=!1}if(this.chunkIndex<this.layer.chunk.length){let a=this.layer.chunk[this.chunkIndex].findIndex(t-this.layer.chunkPos[this.chunkIndex],i,!0);(!l||this.rangeIndex<a)&&this.setRangeIndex(a)}this.next()}forward(t,i){(this.to-t||this.endSide-i)<0&&this.gotoInner(t,i,!0)}next(){for(;;)if(this.chunkIndex==this.layer.chunk.length){this.from=this.to=1e9,this.value=null;break}else{let t=this.layer.chunkPos[this.chunkIndex],i=this.layer.chunk[this.chunkIndex],l=t+i.from[this.rangeIndex];if(this.from=l,this.to=t+i.to[this.rangeIndex],this.value=i.value[this.rangeIndex],this.setRangeIndex(this.rangeIndex+1),this.minPoint<0||this.value.point&&this.to-this.from>=this.minPoint)break}}setRangeIndex(t){if(t==this.layer.chunk[this.chunkIndex].value.length){if(this.chunkIndex++,this.skip)for(;this.chunkIndex<this.layer.chunk.length&&this.skip.has(this.layer.chunk[this.chunkIndex]);)this.chunkIndex++;this.rangeIndex=0}else this.rangeIndex=t}nextChunk(){this.chunkIndex++,this.rangeIndex=0,this.next()}compare(t){return this.from-t.from||this.startSide-t.startSide||this.rank-t.rank||this.to-t.to||this.endSide-t.endSide}}class ba{constructor(t){this.heap=t}static from(t,i=null,l=-1){let a=[];for(let o=0;o<t.length;o++)for(let h=t[o];!h.isEmpty;h=h.nextLayer)h.maxPoint>=l&&a.push(new x0(h,i,l,o));return a.length==1?a[0]:new ba(a)}get startSide(){return this.value?this.value.startSide:0}goto(t,i=-1e9){for(let l of this.heap)l.goto(t,i);for(let l=this.heap.length>>1;l>=0;l--)gf(this.heap,l);return this.next(),this}forward(t,i){for(let l of this.heap)l.forward(t,i);for(let l=this.heap.length>>1;l>=0;l--)gf(this.heap,l);(this.to-t||this.value.endSide-i)<0&&this.next()}next(){if(this.heap.length==0)this.from=this.to=1e9,this.value=null,this.rank=-1;else{let t=this.heap[0];this.from=t.from,this.to=t.to,this.value=t.value,this.rank=t.rank,t.value&&t.next(),gf(this.heap,0)}}}function gf(r,t){for(let i=r[t];;){let l=(t<<1)+1;if(l>=r.length)break;let a=r[l];if(l+1<r.length&&a.compare(r[l+1])>=0&&(a=r[l+1],l++),i.compare(a)<0)break;r[l]=i,r[t]=a,t=l}}class ua{constructor(t,i,l){this.minPoint=l,this.active=[],this.activeTo=[],this.activeRank=[],this.minActive=-1,this.point=null,this.pointFrom=0,this.pointRank=0,this.to=-1e9,this.endSide=0,this.openStart=-1,this.cursor=ba.from(t,i,l)}goto(t,i=-1e9){return this.cursor.goto(t,i),this.active.length=this.activeTo.length=this.activeRank.length=0,this.minActive=-1,this.to=t,this.endSide=i,this.openStart=-1,this.next(),this}forward(t,i){for(;this.minActive>-1&&(this.activeTo[this.minActive]-t||this.active[this.minActive].endSide-i)<0;)this.removeActive(this.minActive);this.cursor.forward(t,i)}removeActive(t){Zr(this.active,t),Zr(this.activeTo,t),Zr(this.activeRank,t),this.minActive=Zm(this.active,this.activeTo)}addActive(t){let i=0,{value:l,to:a,rank:o}=this.cursor;for(;i<this.activeRank.length&&(o-this.activeRank[i]||a-this.activeTo[i])>0;)i++;Kr(this.active,i,l),Kr(this.activeTo,i,a),Kr(this.activeRank,i,o),t&&Kr(t,i,this.cursor.from),this.minActive=Zm(this.active,this.activeTo)}next(){let t=this.to,i=this.point;this.point=null;let l=this.openStart<0?[]:null;for(;;){let a=this.minActive;if(a>-1&&(this.activeTo[a]-this.cursor.from||this.active[a].endSide-this.cursor.startSide)<0){if(this.activeTo[a]>t){this.to=this.activeTo[a],this.endSide=this.active[a].endSide;break}this.removeActive(a),l&&Zr(l,a)}else if(this.cursor.value)if(this.cursor.from>t){this.to=this.cursor.from,this.endSide=this.cursor.startSide;break}else{let o=this.cursor.value;if(!o.point)this.addActive(l),this.cursor.next();else if(i&&this.cursor.to==this.to&&this.cursor.from<this.cursor.to)this.cursor.next();else{this.point=o,this.pointFrom=this.cursor.from,this.pointRank=this.cursor.rank,this.to=this.cursor.to,this.endSide=o.endSide,this.cursor.next(),this.forward(this.to,this.endSide);break}}else{this.to=this.endSide=1e9;break}}if(l){this.openStart=0;for(let a=l.length-1;a>=0&&l[a]<t;a--)this.openStart++}}activeForPoint(t){if(!this.active.length)return this.active;let i=[];for(let l=this.active.length-1;l>=0&&!(this.activeRank[l]<this.pointRank);l--)(this.activeTo[l]>t||this.activeTo[l]==t&&this.active[l].endSide>=this.point.endSide)&&i.push(this.active[l]);return i.reverse()}openEnd(t){let i=0;for(let l=this.activeTo.length-1;l>=0&&this.activeTo[l]>t;l--)i++;return i}}function Ym(r,t,i,l,a,o){r.goto(t),i.goto(l);let h=l+a,c=l,d=l-t;for(;;){let p=r.to+d-i.to,m=p||r.endSide-i.endSide,O=m<0?r.to+d:i.to,S=Math.min(O,h);if(r.point||i.point?r.point&&i.point&&(r.point==i.point||r.point.eq(i.point))&&$f(r.activeForPoint(r.to),i.activeForPoint(i.to))||o.comparePoint(c,S,r.point,i.point):S>c&&!$f(r.active,i.active)&&o.compareRange(c,S,r.active,i.active),O>h)break;(p||r.openEnd!=i.openEnd)&&o.boundChange&&o.boundChange(O),c=O,m<=0&&r.next(),m>=0&&i.next()}}function $f(r,t){if(r.length!=t.length)return!1;for(let i=0;i<r.length;i++)if(r[i]!=t[i]&&!r[i].eq(t[i]))return!1;return!0}function Zr(r,t){for(let i=t,l=r.length-1;i<l;i++)r[i]=r[i+1];r.pop()}function Kr(r,t,i){for(let l=r.length-1;l>=t;l--)r[l+1]=r[l];r[t]=i}function Zm(r,t){let i=-1,l=1e9;for(let a=0;a<t.length;a++)(t[a]-l||r[a].endSide-r[i].endSide)<0&&(i=a,l=t[a]);return i}function Uv(r,t,i,l){for(let a=0,o=0;;){if(o>=t)return a;if(a==r.length)break;o+=r.charCodeAt(a)==9?i-o%i:1,a=Wi(r,a)}return r.length}const Wf="ͼ",Km=typeof Symbol>"u"?"__"+Wf:Symbol.for(Wf),Yf=typeof Symbol>"u"?"__styleSet"+Math.floor(Math.random()*1e8):Symbol("styleSet"),Jm=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{};class ts{constructor(t,i){this.rules=[];let{finish:l}=i||{};function a(h){return/^@/.test(h)?[h]:h.split(/,\s*/)}function o(h,c,d,p){let m=[],O=/^@(\w+)\b/.exec(h[0]),S=O&&O[1]=="keyframes";if(O&&c==null)return d.push(h[0]+";");for(let b in c){let T=c[b];if(/&/.test(b))o(b.split(/,\s*/).map(z=>h.map(C=>z.replace(/&/,C))).reduce((z,C)=>z.concat(C)),T,d);else if(T&&typeof T=="object"){if(!O)throw new RangeError("The value of a property ("+b+") should be a primitive value.");o(a(b),T,m,S)}else T!=null&&m.push(b.replace(/_.*/,"").replace(/[A-Z]/g,z=>"-"+z.toLowerCase())+": "+T+";")}(m.length||S)&&d.push((l&&!O&&!p?h.map(l):h).join(", ")+" {"+m.join(" ")+"}")}for(let h in t)o(a(h),t[h],this.rules)}getRules(){return this.rules.join(`
`)}static newName(){let t=Jm[Km]||1;return Jm[Km]=t+1,Wf+t.toString(36)}static mount(t,i,l){let a=t[Yf],o=l&&l.nonce;a?o&&a.setNonce(o):a=new Bv(t,o),a.mount(Array.isArray(i)?i:[i],t)}}let Fm=new Map;class Bv{constructor(t,i){let l=t.ownerDocument||t,a=l.defaultView;if(!t.head&&t.adoptedStyleSheets&&a.CSSStyleSheet){let o=Fm.get(l);if(o)return t[Yf]=o;this.sheet=new a.CSSStyleSheet,Fm.set(l,this)}else this.styleTag=l.createElement("style"),i&&this.styleTag.setAttribute("nonce",i);this.modules=[],t[Yf]=this}mount(t,i){let l=this.sheet,a=0,o=0;for(let h=0;h<t.length;h++){let c=t[h],d=this.modules.indexOf(c);if(d<o&&d>-1&&(this.modules.splice(d,1),o--,d=-1),d==-1){if(this.modules.splice(o++,0,c),l)for(let p=0;p<c.rules.length;p++)l.insertRule(c.rules[p],a++)}else{for(;o<d;)a+=this.modules[o++].rules.length;a+=c.rules.length,o++}}if(l)i.adoptedStyleSheets.indexOf(this.sheet)<0&&(i.adoptedStyleSheets=[this.sheet,...i.adoptedStyleSheets]);else{let h="";for(let d=0;d<this.modules.length;d++)h+=this.modules[d].getRules()+`
`;this.styleTag.textContent=h;let c=i.head||i;this.styleTag.parentNode!=c&&c.insertBefore(this.styleTag,c.firstChild)}}setNonce(t){this.styleTag&&this.styleTag.getAttribute("nonce")!=t&&this.styleTag.setAttribute("nonce",t)}}var En={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},xa={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},Xv=typeof navigator<"u"&&/Mac/.test(navigator.platform),Pv=typeof navigator<"u"&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent);for(var de=0;de<10;de++)En[48+de]=En[96+de]=String(de);for(var de=1;de<=24;de++)En[de+111]="F"+de;for(var de=65;de<=90;de++)En[de]=String.fromCharCode(de+32),xa[de]=String.fromCharCode(de);for(var pf in En)xa.hasOwnProperty(pf)||(xa[pf]=En[pf]);function Vv(r){var t=Xv&&r.metaKey&&r.shiftKey&&!r.ctrlKey&&!r.altKey||Pv&&r.shiftKey&&r.key&&r.key.length==1||r.key=="Unidentified",i=!t&&r.key||(r.shiftKey?xa:En)[r.keyCode]||r.key||"Unidentified";return i=="Esc"&&(i="Escape"),i=="Del"&&(i="Delete"),i=="Left"&&(i="ArrowLeft"),i=="Up"&&(i="ArrowUp"),i=="Right"&&(i="ArrowRight"),i=="Down"&&(i="ArrowDown"),i}function Ta(r){let t;return r.nodeType==11?t=r.getSelection?r:r.ownerDocument:t=r,t.getSelection()}function Zf(r,t){return t?r==t||r.contains(t.nodeType!=1?t.parentNode:t):!1}function po(r,t){if(!t.anchorNode)return!1;try{return Zf(r,t.anchorNode)}catch{return!1}}function wa(r){return r.nodeType==3?ll(r,0,r.nodeValue.length).getClientRects():r.nodeType==1?r.getClientRects():[]}function ya(r,t,i,l){return i?Im(r,t,i,l,-1)||Im(r,t,i,l,1):!1}function nl(r){for(var t=0;;t++)if(r=r.previousSibling,!r)return t}function ko(r){return r.nodeType==1&&/^(DIV|P|LI|UL|OL|BLOCKQUOTE|DD|DT|H\d|SECTION|PRE)$/.test(r.nodeName)}function Im(r,t,i,l,a){for(;;){if(r==i&&t==l)return!0;if(t==(a<0?0:wi(r))){if(r.nodeName=="DIV")return!1;let o=r.parentNode;if(!o||o.nodeType!=1)return!1;t=nl(r)+(a<0?0:1),r=o}else if(r.nodeType==1){if(r=r.childNodes[t+(a<0?-1:0)],r.nodeType==1&&r.contentEditable=="false")return!1;t=a<0?wi(r):0}else return!1}}function wi(r){return r.nodeType==3?r.nodeValue.length:r.childNodes.length}function Xo(r,t){let i=t?r.left:r.right;return{left:i,right:i,top:r.top,bottom:r.bottom}}function Hv(r){let t=r.visualViewport;return t?{left:0,right:t.width,top:0,bottom:t.height}:{left:0,right:r.innerWidth,top:0,bottom:r.innerHeight}}function T0(r,t){let i=t.width/r.offsetWidth,l=t.height/r.offsetHeight;return(i>.995&&i<1.005||!isFinite(i)||Math.abs(t.width-r.offsetWidth)<1)&&(i=1),(l>.995&&l<1.005||!isFinite(l)||Math.abs(t.height-r.offsetHeight)<1)&&(l=1),{scaleX:i,scaleY:l}}function jv(r,t,i,l,a,o,h,c){let d=r.ownerDocument,p=d.defaultView||window;for(let m=r,O=!1;m&&!O;)if(m.nodeType==1){let S,b=m==d.body,T=1,z=1;if(b)S=Hv(p);else{if(/^(fixed|sticky)$/.test(getComputedStyle(m).position)&&(O=!0),m.scrollHeight<=m.clientHeight&&m.scrollWidth<=m.clientWidth){m=m.assignedSlot||m.parentNode;continue}let $=m.getBoundingClientRect();({scaleX:T,scaleY:z}=T0(m,$)),S={left:$.left,right:$.left+m.clientWidth*T,top:$.top,bottom:$.top+m.clientHeight*z}}let C=0,q=0;if(a=="nearest")t.top<S.top?(q=t.top-(S.top+h),i>0&&t.bottom>S.bottom+q&&(q=t.bottom-S.bottom+h)):t.bottom>S.bottom&&(q=t.bottom-S.bottom+h,i<0&&t.top-q<S.top&&(q=t.top-(S.top+h)));else{let $=t.bottom-t.top,j=S.bottom-S.top;q=(a=="center"&&$<=j?t.top+$/2-j/2:a=="start"||a=="center"&&i<0?t.top-h:t.bottom-j+h)-S.top}if(l=="nearest"?t.left<S.left?(C=t.left-(S.left+o),i>0&&t.right>S.right+C&&(C=t.right-S.right+o)):t.right>S.right&&(C=t.right-S.right+o,i<0&&t.left<S.left+C&&(C=t.left-(S.left+o))):C=(l=="center"?t.left+(t.right-t.left)/2-(S.right-S.left)/2:l=="start"==c?t.left-o:t.right-(S.right-S.left)+o)-S.left,C||q)if(b)p.scrollBy(C,q);else{let $=0,j=0;if(q){let W=m.scrollTop;m.scrollTop+=q/z,j=(m.scrollTop-W)*z}if(C){let W=m.scrollLeft;m.scrollLeft+=C/T,$=(m.scrollLeft-W)*T}t={left:t.left-$,top:t.top-j,right:t.right-$,bottom:t.bottom-j},$&&Math.abs($-C)<1&&(l="nearest"),j&&Math.abs(j-q)<1&&(a="nearest")}if(b)break;(t.top<S.top||t.bottom>S.bottom||t.left<S.left||t.right>S.right)&&(t={left:Math.max(t.left,S.left),right:Math.min(t.right,S.right),top:Math.max(t.top,S.top),bottom:Math.min(t.bottom,S.bottom)}),m=m.assignedSlot||m.parentNode}else if(m.nodeType==11)m=m.host;else break}function Lv(r){let t=r.ownerDocument,i,l;for(let a=r.parentNode;a&&!(a==t.body||i&&l);)if(a.nodeType==1)!l&&a.scrollHeight>a.clientHeight&&(l=a),!i&&a.scrollWidth>a.clientWidth&&(i=a),a=a.assignedSlot||a.parentNode;else if(a.nodeType==11)a=a.host;else break;return{x:i,y:l}}class Gv{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}eq(t){return this.anchorNode==t.anchorNode&&this.anchorOffset==t.anchorOffset&&this.focusNode==t.focusNode&&this.focusOffset==t.focusOffset}setRange(t){let{anchorNode:i,focusNode:l}=t;this.set(i,Math.min(t.anchorOffset,i?wi(i):0),l,Math.min(t.focusOffset,l?wi(l):0))}set(t,i,l,a){this.anchorNode=t,this.anchorOffset=i,this.focusNode=l,this.focusOffset=a}}let Wl=null;function w0(r){if(r.setActive)return r.setActive();if(Wl)return r.focus(Wl);let t=[];for(let i=r;i&&(t.push(i,i.scrollTop,i.scrollLeft),i!=i.ownerDocument);i=i.parentNode);if(r.focus(Wl==null?{get preventScroll(){return Wl={preventScroll:!0},!0}}:void 0),!Wl){Wl=!1;for(let i=0;i<t.length;){let l=t[i++],a=t[i++],o=t[i++];l.scrollTop!=a&&(l.scrollTop=a),l.scrollLeft!=o&&(l.scrollLeft=o)}}}let tO;function ll(r,t,i=t){let l=tO||(tO=document.createRange());return l.setEnd(r,i),l.setStart(r,t),l}function Kl(r,t,i,l){let a={key:t,code:t,keyCode:i,which:i,cancelable:!0};l&&({altKey:a.altKey,ctrlKey:a.ctrlKey,shiftKey:a.shiftKey,metaKey:a.metaKey}=l);let o=new KeyboardEvent("keydown",a);o.synthetic=!0,r.dispatchEvent(o);let h=new KeyboardEvent("keyup",a);return h.synthetic=!0,r.dispatchEvent(h),o.defaultPrevented||h.defaultPrevented}function $v(r){for(;r;){if(r&&(r.nodeType==9||r.nodeType==11&&r.host))return r;r=r.assignedSlot||r.parentNode}return null}function Q0(r){for(;r.attributes.length;)r.removeAttributeNode(r.attributes[0])}function Wv(r,t){let i=t.focusNode,l=t.focusOffset;if(!i||t.anchorNode!=i||t.anchorOffset!=l)return!1;for(l=Math.min(l,wi(i));;)if(l){if(i.nodeType!=1)return!1;let a=i.childNodes[l-1];a.contentEditable=="false"?l--:(i=a,l=wi(i))}else{if(i==r)return!0;l=nl(i),i=i.parentNode}}function k0(r){return r.scrollTop>Math.max(1,r.scrollHeight-r.clientHeight-4)}function A0(r,t){for(let i=r,l=t;;){if(i.nodeType==3&&l>0)return{node:i,offset:l};if(i.nodeType==1&&l>0){if(i.contentEditable=="false")return null;i=i.childNodes[l-1],l=wi(i)}else if(i.parentNode&&!ko(i))l=nl(i),i=i.parentNode;else return null}}function R0(r,t){for(let i=r,l=t;;){if(i.nodeType==3&&l<i.nodeValue.length)return{node:i,offset:l};if(i.nodeType==1&&l<i.childNodes.length){if(i.contentEditable=="false")return null;i=i.childNodes[l],l=0}else if(i.parentNode&&!ko(i))l=nl(i)+1,i=i.parentNode;else return null}}class be{constructor(t,i,l=!0){this.node=t,this.offset=i,this.precise=l}static before(t,i){return new be(t.parentNode,nl(t),i)}static after(t,i){return new be(t.parentNode,nl(t)+1,i)}}const xc=[];class Ut{constructor(){this.parent=null,this.dom=null,this.flags=2}get overrideDOMText(){return null}get posAtStart(){return this.parent?this.parent.posBefore(this):0}get posAtEnd(){return this.posAtStart+this.length}posBefore(t){let i=this.posAtStart;for(let l of this.children){if(l==t)return i;i+=l.length+l.breakAfter}throw new RangeError("Invalid child in posBefore")}posAfter(t){return this.posBefore(t)+t.length}sync(t,i){if(this.flags&2){let l=this.dom,a=null,o;for(let h of this.children){if(h.flags&7){if(!h.dom&&(o=a?a.nextSibling:l.firstChild)){let c=Ut.get(o);(!c||!c.parent&&c.canReuseDOM(h))&&h.reuseDOM(o)}h.sync(t,i),h.flags&=-8}if(o=a?a.nextSibling:l.firstChild,i&&!i.written&&i.node==l&&o!=h.dom&&(i.written=!0),h.dom.parentNode==l)for(;o&&o!=h.dom;)o=eO(o);else l.insertBefore(h.dom,o);a=h.dom}for(o=a?a.nextSibling:l.firstChild,o&&i&&i.node==l&&(i.written=!0);o;)o=eO(o)}else if(this.flags&1)for(let l of this.children)l.flags&7&&(l.sync(t,i),l.flags&=-8)}reuseDOM(t){}localPosFromDOM(t,i){let l;if(t==this.dom)l=this.dom.childNodes[i];else{let a=wi(t)==0?0:i==0?-1:1;for(;;){let o=t.parentNode;if(o==this.dom)break;a==0&&o.firstChild!=o.lastChild&&(t==o.firstChild?a=-1:a=1),t=o}a<0?l=t:l=t.nextSibling}if(l==this.dom.firstChild)return 0;for(;l&&!Ut.get(l);)l=l.nextSibling;if(!l)return this.length;for(let a=0,o=0;;a++){let h=this.children[a];if(h.dom==l)return o;o+=h.length+h.breakAfter}}domBoundsAround(t,i,l=0){let a=-1,o=-1,h=-1,c=-1;for(let d=0,p=l,m=l;d<this.children.length;d++){let O=this.children[d],S=p+O.length;if(p<t&&S>i)return O.domBoundsAround(t,i,p);if(S>=t&&a==-1&&(a=d,o=p),p>i&&O.dom.parentNode==this.dom){h=d,c=m;break}m=S,p=S+O.breakAfter}return{from:o,to:c<0?l+this.length:c,startDOM:(a?this.children[a-1].dom.nextSibling:null)||this.dom.firstChild,endDOM:h<this.children.length&&h>=0?this.children[h].dom:null}}markDirty(t=!1){this.flags|=2,this.markParentsDirty(t)}markParentsDirty(t){for(let i=this.parent;i;i=i.parent){if(t&&(i.flags|=2),i.flags&1)return;i.flags|=1,t=!1}}setParent(t){this.parent!=t&&(this.parent=t,this.flags&7&&this.markParentsDirty(!0))}setDOM(t){this.dom!=t&&(this.dom&&(this.dom.cmView=null),this.dom=t,t.cmView=this)}get rootView(){for(let t=this;;){let i=t.parent;if(!i)return t;t=i}}replaceChildren(t,i,l=xc){this.markDirty();for(let a=t;a<i;a++){let o=this.children[a];o.parent==this&&l.indexOf(o)<0&&o.destroy()}l.length<250?this.children.splice(t,i-t,...l):this.children=[].concat(this.children.slice(0,t),l,this.children.slice(i));for(let a=0;a<l.length;a++)l[a].setParent(this)}ignoreMutation(t){return!1}ignoreEvent(t){return!1}childCursor(t=this.length){return new E0(this.children,t,this.children.length)}childPos(t,i=1){return this.childCursor().findPos(t,i)}toString(){let t=this.constructor.name.replace("View","");return t+(this.children.length?"("+this.children.join()+")":this.length?"["+(t=="Text"?this.text:this.length)+"]":"")+(this.breakAfter?"#":"")}static get(t){return t.cmView}get isEditable(){return!0}get isWidget(){return!1}get isHidden(){return!1}merge(t,i,l,a,o,h){return!1}become(t){return!1}canReuseDOM(t){return t.constructor==this.constructor&&!((this.flags|t.flags)&8)}getSide(){return 0}destroy(){for(let t of this.children)t.parent==this&&t.destroy();this.parent=null}}Ut.prototype.breakAfter=0;function eO(r){let t=r.nextSibling;return r.parentNode.removeChild(r),t}class E0{constructor(t,i,l){this.children=t,this.pos=i,this.i=l,this.off=0}findPos(t,i=1){for(;;){if(t>this.pos||t==this.pos&&(i>0||this.i==0||this.children[this.i-1].breakAfter))return this.off=t-this.pos,this;let l=this.children[--this.i];this.pos-=l.length+l.breakAfter}}}function z0(r,t,i,l,a,o,h,c,d){let{children:p}=r,m=p.length?p[t]:null,O=o.length?o[o.length-1]:null,S=O?O.breakAfter:h;if(!(t==l&&m&&!h&&!S&&o.length<2&&m.merge(i,a,o.length?O:null,i==0,c,d))){if(l<p.length){let b=p[l];b&&(a<b.length||b.breakAfter&&(O!=null&&O.breakAfter))?(t==l&&(b=b.split(a),a=0),!S&&O&&b.merge(0,a,O,!0,0,d)?o[o.length-1]=b:((a||b.children.length&&!b.children[0].length)&&b.merge(0,a,null,!1,0,d),o.push(b))):b!=null&&b.breakAfter&&(O?O.breakAfter=1:h=1),l++}for(m&&(m.breakAfter=h,i>0&&(!h&&o.length&&m.merge(i,m.length,o[0],!1,c,0)?m.breakAfter=o.shift().breakAfter:(i<m.length||m.children.length&&m.children[m.children.length-1].length==0)&&m.merge(i,m.length,null,!1,c,0),t++));t<l&&o.length;)if(p[l-1].become(o[o.length-1]))l--,o.pop(),d=o.length?0:c;else if(p[t].become(o[0]))t++,o.shift(),c=o.length?0:d;else break;!o.length&&t&&l<p.length&&!p[t-1].breakAfter&&p[l].merge(0,0,p[t-1],!1,c,d)&&t--,(t<l||o.length)&&r.replaceChildren(t,l,o)}}function M0(r,t,i,l,a,o){let h=r.childCursor(),{i:c,off:d}=h.findPos(i,1),{i:p,off:m}=h.findPos(t,-1),O=t-i;for(let S of l)O+=S.length;r.length+=O,z0(r,p,m,c,d,l,0,a,o)}let ze=typeof navigator<"u"?navigator:{userAgent:"",vendor:"",platform:""},Kf=typeof document<"u"?document:{documentElement:{style:{}}};const Jf=/Edge\/(\d+)/.exec(ze.userAgent),C0=/MSIE \d/.test(ze.userAgent),Ff=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(ze.userAgent),Po=!!(C0||Ff||Jf),iO=!Po&&/gecko\/(\d+)/i.test(ze.userAgent),mf=!Po&&/Chrome\/(\d+)/.exec(ze.userAgent),Yv="webkitFontSmoothing"in Kf.documentElement.style,D0=!Po&&/Apple Computer/.test(ze.vendor),nO=D0&&(/Mobile\/\w+/.test(ze.userAgent)||ze.maxTouchPoints>2);var Z={mac:nO||/Mac/.test(ze.platform),windows:/Win/.test(ze.platform),linux:/Linux|X11/.test(ze.platform),ie:Po,ie_version:C0?Kf.documentMode||6:Ff?+Ff[1]:Jf?+Jf[1]:0,gecko:iO,gecko_version:iO?+(/Firefox\/(\d+)/.exec(ze.userAgent)||[0,0])[1]:0,chrome:!!mf,chrome_version:mf?+mf[1]:0,ios:nO,android:/Android\b/.test(ze.userAgent),safari:D0,webkit_version:Yv?+(/\bAppleWebKit\/(\d+)/.exec(ze.userAgent)||[0,0])[1]:0,tabSize:Kf.documentElement.style.tabSize!=null?"tab-size":"-moz-tab-size"};const Zv=256;class ai extends Ut{constructor(t){super(),this.text=t}get length(){return this.text.length}createDOM(t){this.setDOM(t||document.createTextNode(this.text))}sync(t,i){this.dom||this.createDOM(),this.dom.nodeValue!=this.text&&(i&&i.node==this.dom&&(i.written=!0),this.dom.nodeValue=this.text)}reuseDOM(t){t.nodeType==3&&this.createDOM(t)}merge(t,i,l){return this.flags&8||l&&(!(l instanceof ai)||this.length-(i-t)+l.length>Zv||l.flags&8)?!1:(this.text=this.text.slice(0,t)+(l?l.text:"")+this.text.slice(i),this.markDirty(),!0)}split(t){let i=new ai(this.text.slice(t));return this.text=this.text.slice(0,t),this.markDirty(),i.flags|=this.flags&8,i}localPosFromDOM(t,i){return t==this.dom?i:i?this.text.length:0}domAtPos(t){return new be(this.dom,t)}domBoundsAround(t,i,l){return{from:l,to:l+this.length,startDOM:this.dom,endDOM:this.dom.nextSibling}}coordsAt(t,i){return Kv(this.dom,t,i)}}class Ji extends Ut{constructor(t,i=[],l=0){super(),this.mark=t,this.children=i,this.length=l;for(let a of i)a.setParent(this)}setAttrs(t){if(Q0(t),this.mark.class&&(t.className=this.mark.class),this.mark.attrs)for(let i in this.mark.attrs)t.setAttribute(i,this.mark.attrs[i]);return t}canReuseDOM(t){return super.canReuseDOM(t)&&!((this.flags|t.flags)&8)}reuseDOM(t){t.nodeName==this.mark.tagName.toUpperCase()&&(this.setDOM(t),this.flags|=6)}sync(t,i){this.dom?this.flags&4&&this.setAttrs(this.dom):this.setDOM(this.setAttrs(document.createElement(this.mark.tagName))),super.sync(t,i)}merge(t,i,l,a,o,h){return l&&(!(l instanceof Ji&&l.mark.eq(this.mark))||t&&o<=0||i<this.length&&h<=0)?!1:(M0(this,t,i,l?l.children.slice():[],o-1,h-1),this.markDirty(),!0)}split(t){let i=[],l=0,a=-1,o=0;for(let c of this.children){let d=l+c.length;d>t&&i.push(l<t?c.split(t-l):c),a<0&&l>=t&&(a=o),l=d,o++}let h=this.length-t;return this.length=t,a>-1&&(this.children.length=a,this.markDirty()),new Ji(this.mark,i,h)}domAtPos(t){return q0(this,t)}coordsAt(t,i){return N0(this,t,i)}}function Kv(r,t,i){let l=r.nodeValue.length;t>l&&(t=l);let a=t,o=t,h=0;t==0&&i<0||t==l&&i>=0?Z.chrome||Z.gecko||(t?(a--,h=1):o<l&&(o++,h=-1)):i<0?a--:o<l&&o++;let c=ll(r,a,o).getClientRects();if(!c.length)return null;let d=c[(h?h<0:i>=0)?0:c.length-1];return Z.safari&&!h&&d.width==0&&(d=Array.prototype.find.call(c,p=>p.width)||d),h?Xo(d,h<0):d||null}class tl extends Ut{static create(t,i,l){return new tl(t,i,l)}constructor(t,i,l){super(),this.widget=t,this.length=i,this.side=l,this.prevWidget=null}split(t){let i=tl.create(this.widget,this.length-t,this.side);return this.length-=t,i}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}getSide(){return this.side}merge(t,i,l,a,o,h){return l&&(!(l instanceof tl)||!this.widget.compare(l.widget)||t>0&&o<=0||i<this.length&&h<=0)?!1:(this.length=t+(l?l.length:0)+(this.length-i),!0)}become(t){return t instanceof tl&&t.side==this.side&&this.widget.constructor==t.widget.constructor?(this.widget.compare(t.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,!0):!1}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get overrideDOMText(){if(this.length==0)return wt.empty;let t=this;for(;t.parent;)t=t.parent;let{view:i}=t,l=i&&i.state.doc,a=this.posAtStart;return l?l.slice(a,a+this.length):wt.empty}domAtPos(t){return(this.length?t==0:this.side>0)?be.before(this.dom):be.after(this.dom,t==this.length)}domBoundsAround(){return null}coordsAt(t,i){let l=this.widget.coordsAt(this.dom,t,i);if(l)return l;let a=this.dom.getClientRects(),o=null;if(!a.length)return null;let h=this.side?this.side<0:t>0;for(let c=h?a.length-1:0;o=a[c],!(t>0?c==0:c==a.length-1||o.top<o.bottom);c+=h?-1:1);return Xo(o,!h)}get isEditable(){return!1}get isWidget(){return!0}get isHidden(){return this.widget.isHidden}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}}class es extends Ut{constructor(t){super(),this.side=t}get length(){return 0}merge(){return!1}become(t){return t instanceof es&&t.side==this.side}split(){return new es(this.side)}sync(){if(!this.dom){let t=document.createElement("img");t.className="cm-widgetBuffer",t.setAttribute("aria-hidden","true"),this.setDOM(t)}}getSide(){return this.side}domAtPos(t){return this.side>0?be.before(this.dom):be.after(this.dom)}localPosFromDOM(){return 0}domBoundsAround(){return null}coordsAt(t){return this.dom.getBoundingClientRect()}get overrideDOMText(){return wt.empty}get isHidden(){return!0}}ai.prototype.children=tl.prototype.children=es.prototype.children=xc;function q0(r,t){let i=r.dom,{children:l}=r,a=0;for(let o=0;a<l.length;a++){let h=l[a],c=o+h.length;if(!(c==o&&h.getSide()<=0)){if(t>o&&t<c&&h.dom.parentNode==i)return h.domAtPos(t-o);if(t<=o)break;o=c}}for(let o=a;o>0;o--){let h=l[o-1];if(h.dom.parentNode==i)return h.domAtPos(h.length)}for(let o=a;o<l.length;o++){let h=l[o];if(h.dom.parentNode==i)return h.domAtPos(0)}return new be(i,0)}function _0(r,t,i){let l,{children:a}=r;i>0&&t instanceof Ji&&a.length&&(l=a[a.length-1])instanceof Ji&&l.mark.eq(t.mark)?_0(l,t.children[0],i-1):(a.push(t),t.setParent(r)),r.length+=t.length}function N0(r,t,i){let l=null,a=-1,o=null,h=-1;function c(p,m){for(let O=0,S=0;O<p.children.length&&S<=m;O++){let b=p.children[O],T=S+b.length;T>=m&&(b.children.length?c(b,m-S):(!o||o.isHidden&&(i>0||Fv(o,b)))&&(T>m||S==T&&b.getSide()>0)?(o=b,h=m-S):(S<m||S==T&&b.getSide()<0&&!b.isHidden)&&(l=b,a=m-S)),S=T}}c(r,t);let d=(i<0?l:o)||l||o;return d?d.coordsAt(Math.max(0,d==l?a:h),i):Jv(r)}function Jv(r){let t=r.dom.lastChild;if(!t)return r.dom.getBoundingClientRect();let i=wa(t);return i[i.length-1]||null}function Fv(r,t){let i=r.coordsAt(0,1),l=t.coordsAt(0,1);return i&&l&&l.top<i.bottom}function If(r,t){for(let i in r)i=="class"&&t.class?t.class+=" "+r.class:i=="style"&&t.style?t.style+=";"+r.style:t[i]=r[i];return t}const lO=Object.create(null);function Ao(r,t,i){if(r==t)return!0;r||(r=lO),t||(t=lO);let l=Object.keys(r),a=Object.keys(t);if(l.length-(i&&l.indexOf(i)>-1?1:0)!=a.length-(i&&a.indexOf(i)>-1?1:0))return!1;for(let o of l)if(o!=i&&(a.indexOf(o)==-1||r[o]!==t[o]))return!1;return!0}function tc(r,t,i){let l=!1;if(t)for(let a in t)i&&a in i||(l=!0,a=="style"?r.style.cssText="":r.removeAttribute(a));if(i)for(let a in i)t&&t[a]==i[a]||(l=!0,a=="style"?r.style.cssText=i[a]:r.setAttribute(a,i[a]));return l}function Iv(r){let t=Object.create(null);for(let i=0;i<r.attributes.length;i++){let l=r.attributes[i];t[l.name]=l.value}return t}class Vo{eq(t){return!1}updateDOM(t,i){return!1}compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}get estimatedHeight(){return-1}get lineBreaks(){return 0}ignoreEvent(t){return!0}coordsAt(t,i,l){return null}get isHidden(){return!1}get editable(){return!1}destroy(t){}}var Ti=function(r){return r[r.Text=0]="Text",r[r.WidgetBefore=1]="WidgetBefore",r[r.WidgetAfter=2]="WidgetAfter",r[r.WidgetRange=3]="WidgetRange",r}(Ti||(Ti={}));class ne extends il{constructor(t,i,l,a){super(),this.startSide=t,this.endSide=i,this.widget=l,this.spec=a}get heightRelevant(){return!1}static mark(t){return new za(t)}static widget(t){let i=Math.max(-1e4,Math.min(1e4,t.side||0)),l=!!t.block;return i+=l&&!t.inlineOrder?i>0?3e8:-4e8:i>0?1e8:-1e8,new zn(t,i,i,l,t.widget||null,!1)}static replace(t){let i=!!t.block,l,a;if(t.isBlockGap)l=-5e8,a=4e8;else{let{start:o,end:h}=U0(t,i);l=(o?i?-3e8:-1:5e8)-1,a=(h?i?2e8:1:-6e8)+1}return new zn(t,l,a,i,t.widget||null,!0)}static line(t){return new Ma(t)}static set(t,i=!1){return Pt.of(t,i)}hasHeight(){return this.widget?this.widget.estimatedHeight>-1:!1}}ne.none=Pt.empty;class za extends ne{constructor(t){let{start:i,end:l}=U0(t);super(i?-1:5e8,l?1:-6e8,null,t),this.tagName=t.tagName||"span",this.class=t.class||"",this.attrs=t.attributes||null}eq(t){var i,l;return this==t||t instanceof za&&this.tagName==t.tagName&&(this.class||((i=this.attrs)===null||i===void 0?void 0:i.class))==(t.class||((l=t.attrs)===null||l===void 0?void 0:l.class))&&Ao(this.attrs,t.attrs,"class")}range(t,i=t){if(t>=i)throw new RangeError("Mark decorations may not be empty");return super.range(t,i)}}za.prototype.point=!1;class Ma extends ne{constructor(t){super(-2e8,-2e8,null,t)}eq(t){return t instanceof Ma&&this.spec.class==t.spec.class&&Ao(this.spec.attributes,t.spec.attributes)}range(t,i=t){if(i!=t)throw new RangeError("Line decoration ranges must be zero-length");return super.range(t,i)}}Ma.prototype.mapMode=Te.TrackBefore;Ma.prototype.point=!0;class zn extends ne{constructor(t,i,l,a,o,h){super(i,l,o,t),this.block=a,this.isReplace=h,this.mapMode=a?i<=0?Te.TrackBefore:Te.TrackAfter:Te.TrackDel}get type(){return this.startSide!=this.endSide?Ti.WidgetRange:this.startSide<=0?Ti.WidgetBefore:Ti.WidgetAfter}get heightRelevant(){return this.block||!!this.widget&&(this.widget.estimatedHeight>=5||this.widget.lineBreaks>0)}eq(t){return t instanceof zn&&tb(this.widget,t.widget)&&this.block==t.block&&this.startSide==t.startSide&&this.endSide==t.endSide}range(t,i=t){if(this.isReplace&&(t>i||t==i&&this.startSide>0&&this.endSide<=0))throw new RangeError("Invalid range for replacement decoration");if(!this.isReplace&&i!=t)throw new RangeError("Widget decorations can only have zero-length ranges");return super.range(t,i)}}zn.prototype.point=!0;function U0(r,t=!1){let{inclusiveStart:i,inclusiveEnd:l}=r;return i==null&&(i=r.inclusive),l==null&&(l=r.inclusive),{start:i??t,end:l??t}}function tb(r,t){return r==t||!!(r&&t&&r.compare(t))}function mo(r,t,i,l=0){let a=i.length-1;a>=0&&i[a]+l>=r?i[a]=Math.max(i[a],t):i.push(r,t)}class Ft extends Ut{constructor(){super(...arguments),this.children=[],this.length=0,this.prevAttrs=void 0,this.attrs=null,this.breakAfter=0}merge(t,i,l,a,o,h){if(l){if(!(l instanceof Ft))return!1;this.dom||l.transferDOM(this)}return a&&this.setDeco(l?l.attrs:null),M0(this,t,i,l?l.children.slice():[],o,h),!0}split(t){let i=new Ft;if(i.breakAfter=this.breakAfter,this.length==0)return i;let{i:l,off:a}=this.childPos(t);a&&(i.append(this.children[l].split(a),0),this.children[l].merge(a,this.children[l].length,null,!1,0,0),l++);for(let o=l;o<this.children.length;o++)i.append(this.children[o],0);for(;l>0&&this.children[l-1].length==0;)this.children[--l].destroy();return this.children.length=l,this.markDirty(),this.length=t,i}transferDOM(t){this.dom&&(this.markDirty(),t.setDOM(this.dom),t.prevAttrs=this.prevAttrs===void 0?this.attrs:this.prevAttrs,this.prevAttrs=void 0,this.dom=null)}setDeco(t){Ao(this.attrs,t)||(this.dom&&(this.prevAttrs=this.attrs,this.markDirty()),this.attrs=t)}append(t,i){_0(this,t,i)}addLineDeco(t){let i=t.spec.attributes,l=t.spec.class;i&&(this.attrs=If(i,this.attrs||{})),l&&(this.attrs=If({class:l},this.attrs||{}))}domAtPos(t){return q0(this,t)}reuseDOM(t){t.nodeName=="DIV"&&(this.setDOM(t),this.flags|=6)}sync(t,i){var l;this.dom?this.flags&4&&(Q0(this.dom),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0):(this.setDOM(document.createElement("div")),this.dom.className="cm-line",this.prevAttrs=this.attrs?null:void 0),this.prevAttrs!==void 0&&(tc(this.dom,this.prevAttrs,this.attrs),this.dom.classList.add("cm-line"),this.prevAttrs=void 0),super.sync(t,i);let a=this.dom.lastChild;for(;a&&Ut.get(a)instanceof Ji;)a=a.lastChild;if(!a||!this.length||a.nodeName!="BR"&&((l=Ut.get(a))===null||l===void 0?void 0:l.isEditable)==!1&&(!Z.ios||!this.children.some(o=>o instanceof ai))){let o=document.createElement("BR");o.cmIgnore=!0,this.dom.appendChild(o)}}measureTextSize(){if(this.children.length==0||this.length>20)return null;let t=0,i;for(let l of this.children){if(!(l instanceof ai)||/[^ -~]/.test(l.text))return null;let a=wa(l.dom);if(a.length!=1)return null;t+=a[0].width,i=a[0].height}return t?{lineHeight:this.dom.getBoundingClientRect().height,charWidth:t/this.length,textHeight:i}:null}coordsAt(t,i){let l=N0(this,t,i);if(!this.children.length&&l&&this.parent){let{heightOracle:a}=this.parent.view.viewState,o=l.bottom-l.top;if(Math.abs(o-a.lineHeight)<2&&a.textHeight<o){let h=(o-a.textHeight)/2;return{top:l.top+h,bottom:l.bottom-h,left:l.left,right:l.left}}}return l}become(t){return t instanceof Ft&&this.children.length==0&&t.children.length==0&&Ao(this.attrs,t.attrs)&&this.breakAfter==t.breakAfter}covers(){return!0}static find(t,i){for(let l=0,a=0;l<t.children.length;l++){let o=t.children[l],h=a+o.length;if(h>=i){if(o instanceof Ft)return o;if(h>i)break}a=h+o.breakAfter}return null}}class Ki extends Ut{constructor(t,i,l){super(),this.widget=t,this.length=i,this.deco=l,this.breakAfter=0,this.prevWidget=null}merge(t,i,l,a,o,h){return l&&(!(l instanceof Ki)||!this.widget.compare(l.widget)||t>0&&o<=0||i<this.length&&h<=0)?!1:(this.length=t+(l?l.length:0)+(this.length-i),!0)}domAtPos(t){return t==0?be.before(this.dom):be.after(this.dom,t==this.length)}split(t){let i=this.length-t;this.length=t;let l=new Ki(this.widget,i,this.deco);return l.breakAfter=this.breakAfter,l}get children(){return xc}sync(t){(!this.dom||!this.widget.updateDOM(this.dom,t))&&(this.dom&&this.prevWidget&&this.prevWidget.destroy(this.dom),this.prevWidget=null,this.setDOM(this.widget.toDOM(t)),this.widget.editable||(this.dom.contentEditable="false"))}get overrideDOMText(){return this.parent?this.parent.view.state.doc.slice(this.posAtStart,this.posAtEnd):wt.empty}domBoundsAround(){return null}become(t){return t instanceof Ki&&t.widget.constructor==this.widget.constructor?(t.widget.compare(this.widget)||this.markDirty(!0),this.dom&&!this.prevWidget&&(this.prevWidget=this.widget),this.widget=t.widget,this.length=t.length,this.deco=t.deco,this.breakAfter=t.breakAfter,!0):!1}ignoreMutation(){return!0}ignoreEvent(t){return this.widget.ignoreEvent(t)}get isEditable(){return!1}get isWidget(){return!0}coordsAt(t,i){let l=this.widget.coordsAt(this.dom,t,i);return l||(this.widget instanceof ec?null:Xo(this.dom.getBoundingClientRect(),this.length?t==0:i<=0))}destroy(){super.destroy(),this.dom&&this.widget.destroy(this.dom)}covers(t){let{startSide:i,endSide:l}=this.deco;return i==l?!1:t<0?i<0:l>0}}class ec extends Vo{constructor(t){super(),this.height=t}toDOM(){let t=document.createElement("div");return t.className="cm-gap",this.updateDOM(t),t}eq(t){return t.height==this.height}updateDOM(t){return t.style.height=this.height+"px",!0}get editable(){return!0}get estimatedHeight(){return this.height}ignoreEvent(){return!1}}class Sa{constructor(t,i,l,a){this.doc=t,this.pos=i,this.end=l,this.disallowBlockEffectsFor=a,this.content=[],this.curLine=null,this.breakAtStart=0,this.pendingBuffer=0,this.bufferMarks=[],this.atCursorPos=!0,this.openStart=-1,this.openEnd=-1,this.text="",this.textOff=0,this.cursor=t.iter(),this.skip=i}posCovered(){if(this.content.length==0)return!this.breakAtStart&&this.doc.lineAt(this.pos).from!=this.pos;let t=this.content[this.content.length-1];return!(t.breakAfter||t instanceof Ki&&t.deco.endSide<0)}getLine(){return this.curLine||(this.content.push(this.curLine=new Ft),this.atCursorPos=!0),this.curLine}flushBuffer(t=this.bufferMarks){this.pendingBuffer&&(this.curLine.append(Jr(new es(-1),t),t.length),this.pendingBuffer=0)}addBlockWidget(t){this.flushBuffer(),this.curLine=null,this.content.push(t)}finish(t){this.pendingBuffer&&t<=this.bufferMarks.length?this.flushBuffer():this.pendingBuffer=0,!this.posCovered()&&!(t&&this.content.length&&this.content[this.content.length-1]instanceof Ki)&&this.getLine()}buildText(t,i,l){for(;t>0;){if(this.textOff==this.text.length){let{value:o,lineBreak:h,done:c}=this.cursor.next(this.skip);if(this.skip=0,c)throw new Error("Ran out of text content when drawing inline views");if(h){this.posCovered()||this.getLine(),this.content.length?this.content[this.content.length-1].breakAfter=1:this.breakAtStart=1,this.flushBuffer(),this.curLine=null,this.atCursorPos=!0,t--;continue}else this.text=o,this.textOff=0}let a=Math.min(this.text.length-this.textOff,t,512);this.flushBuffer(i.slice(i.length-l)),this.getLine().append(Jr(new ai(this.text.slice(this.textOff,this.textOff+a)),i),l),this.atCursorPos=!0,this.textOff+=a,t-=a,l=0}}span(t,i,l,a){this.buildText(i-t,l,a),this.pos=i,this.openStart<0&&(this.openStart=a)}point(t,i,l,a,o,h){if(this.disallowBlockEffectsFor[h]&&l instanceof zn){if(l.block)throw new RangeError("Block decorations may not be specified via plugins");if(i>this.doc.lineAt(this.pos).to)throw new RangeError("Decorations that replace line breaks may not be specified via plugins")}let c=i-t;if(l instanceof zn)if(l.block)l.startSide>0&&!this.posCovered()&&this.getLine(),this.addBlockWidget(new Ki(l.widget||is.block,c,l));else{let d=tl.create(l.widget||is.inline,c,c?0:l.startSide),p=this.atCursorPos&&!d.isEditable&&o<=a.length&&(t<i||l.startSide>0),m=!d.isEditable&&(t<i||o>a.length||l.startSide<=0),O=this.getLine();this.pendingBuffer==2&&!p&&!d.isEditable&&(this.pendingBuffer=0),this.flushBuffer(a),p&&(O.append(Jr(new es(1),a),o),o=a.length+Math.max(0,o-a.length)),O.append(Jr(d,a),o),this.atCursorPos=m,this.pendingBuffer=m?t<i||o>a.length?1:2:0,this.pendingBuffer&&(this.bufferMarks=a.slice())}else this.doc.lineAt(this.pos).from==this.pos&&this.getLine().addLineDeco(l);c&&(this.textOff+c<=this.text.length?this.textOff+=c:(this.skip+=c-(this.text.length-this.textOff),this.text="",this.textOff=0),this.pos=i),this.openStart<0&&(this.openStart=o)}static build(t,i,l,a,o){let h=new Sa(t,i,l,o);return h.openEnd=Pt.spans(a,i,l,h),h.openStart<0&&(h.openStart=h.openEnd),h.finish(h.openEnd),h}}function Jr(r,t){for(let i of t)r=new Ji(i,[r],r.length);return r}class is extends Vo{constructor(t){super(),this.tag=t}eq(t){return t.tag==this.tag}toDOM(){return document.createElement(this.tag)}updateDOM(t){return t.nodeName.toLowerCase()==this.tag}get isHidden(){return!0}}is.inline=new is("span");is.block=new is("div");var pe=function(r){return r[r.LTR=0]="LTR",r[r.RTL=1]="RTL",r}(pe||(pe={}));const sl=pe.LTR,Tc=pe.RTL;function B0(r){let t=[];for(let i=0;i<r.length;i++)t.push(1<<+r[i]);return t}const eb=B0("88888888888888888888888888888888888666888888787833333333337888888000000000000000000000000008888880000000000000000000000000088888888888888888888888888888888888887866668888088888663380888308888800000000000000000000000800000000000000000000000000000008"),ib=B0("4444448826627288999999999992222222222222222222222222222222222222222222222229999999999999999999994444444444644222822222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222222999999949999999229989999223333333333"),ic=Object.create(null),gi=[];for(let r of["()","[]","{}"]){let t=r.charCodeAt(0),i=r.charCodeAt(1);ic[t]=i,ic[i]=-t}function X0(r){return r<=247?eb[r]:1424<=r&&r<=1524?2:1536<=r&&r<=1785?ib[r-1536]:1774<=r&&r<=2220?4:8192<=r&&r<=8204?256:64336<=r&&r<=65023?4:1}const nb=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac\ufb50-\ufdff]/;class Rn{get dir(){return this.level%2?Tc:sl}constructor(t,i,l){this.from=t,this.to=i,this.level=l}side(t,i){return this.dir==i==t?this.to:this.from}forward(t,i){return t==(this.dir==i)}static find(t,i,l,a){let o=-1;for(let h=0;h<t.length;h++){let c=t[h];if(c.from<=i&&c.to>=i){if(c.level==l)return h;(o<0||(a!=0?a<0?c.from<i:c.to>i:t[o].level>c.level))&&(o=h)}}if(o<0)throw new RangeError("Index out of range");return o}}function P0(r,t){if(r.length!=t.length)return!1;for(let i=0;i<r.length;i++){let l=r[i],a=t[i];if(l.from!=a.from||l.to!=a.to||l.direction!=a.direction||!P0(l.inner,a.inner))return!1}return!0}const Nt=[];function lb(r,t,i,l,a){for(let o=0;o<=l.length;o++){let h=o?l[o-1].to:t,c=o<l.length?l[o].from:i,d=o?256:a;for(let p=h,m=d,O=d;p<c;p++){let S=X0(r.charCodeAt(p));S==512?S=m:S==8&&O==4&&(S=16),Nt[p]=S==4?2:S,S&7&&(O=S),m=S}for(let p=h,m=d,O=d;p<c;p++){let S=Nt[p];if(S==128)p<c-1&&m==Nt[p+1]&&m&24?S=Nt[p]=m:Nt[p]=256;else if(S==64){let b=p+1;for(;b<c&&Nt[b]==64;)b++;let T=p&&m==8||b<i&&Nt[b]==8?O==1?1:8:256;for(let z=p;z<b;z++)Nt[z]=T;p=b-1}else S==8&&O==1&&(Nt[p]=1);m=S,S&7&&(O=S)}}}function sb(r,t,i,l,a){let o=a==1?2:1;for(let h=0,c=0,d=0;h<=l.length;h++){let p=h?l[h-1].to:t,m=h<l.length?l[h].from:i;for(let O=p,S,b,T;O<m;O++)if(b=ic[S=r.charCodeAt(O)])if(b<0){for(let z=c-3;z>=0;z-=3)if(gi[z+1]==-b){let C=gi[z+2],q=C&2?a:C&4?C&1?o:a:0;q&&(Nt[O]=Nt[gi[z]]=q),c=z;break}}else{if(gi.length==189)break;gi[c++]=O,gi[c++]=S,gi[c++]=d}else if((T=Nt[O])==2||T==1){let z=T==a;d=z?0:1;for(let C=c-3;C>=0;C-=3){let q=gi[C+2];if(q&2)break;if(z)gi[C+2]|=2;else{if(q&4)break;gi[C+2]|=4}}}}}function ab(r,t,i,l){for(let a=0,o=l;a<=i.length;a++){let h=a?i[a-1].to:r,c=a<i.length?i[a].from:t;for(let d=h;d<c;){let p=Nt[d];if(p==256){let m=d+1;for(;;)if(m==c){if(a==i.length)break;m=i[a++].to,c=a<i.length?i[a].from:t}else if(Nt[m]==256)m++;else break;let O=o==1,S=(m<t?Nt[m]:l)==1,b=O==S?O?1:2:l;for(let T=m,z=a,C=z?i[z-1].to:r;T>d;)T==C&&(T=i[--z].from,C=z?i[z-1].to:r),Nt[--T]=b;d=m}else o=p,d++}}}function nc(r,t,i,l,a,o,h){let c=l%2?2:1;if(l%2==a%2)for(let d=t,p=0;d<i;){let m=!0,O=!1;if(p==o.length||d<o[p].from){let z=Nt[d];z!=c&&(m=!1,O=z==16)}let S=!m&&c==1?[]:null,b=m?l:l+1,T=d;t:for(;;)if(p<o.length&&T==o[p].from){if(O)break t;let z=o[p];if(!m)for(let C=z.to,q=p+1;;){if(C==i)break t;if(q<o.length&&o[q].from==C)C=o[q++].to;else{if(Nt[C]==c)break t;break}}if(p++,S)S.push(z);else{z.from>d&&h.push(new Rn(d,z.from,b));let C=z.direction==sl!=!(b%2);lc(r,C?l+1:l,a,z.inner,z.from,z.to,h),d=z.to}T=z.to}else{if(T==i||(m?Nt[T]!=c:Nt[T]==c))break;T++}S?nc(r,d,T,l+1,a,S,h):d<T&&h.push(new Rn(d,T,b)),d=T}else for(let d=i,p=o.length;d>t;){let m=!0,O=!1;if(!p||d>o[p-1].to){let z=Nt[d-1];z!=c&&(m=!1,O=z==16)}let S=!m&&c==1?[]:null,b=m?l:l+1,T=d;t:for(;;)if(p&&T==o[p-1].to){if(O)break t;let z=o[--p];if(!m)for(let C=z.from,q=p;;){if(C==t)break t;if(q&&o[q-1].to==C)C=o[--q].from;else{if(Nt[C-1]==c)break t;break}}if(S)S.push(z);else{z.to<d&&h.push(new Rn(z.to,d,b));let C=z.direction==sl!=!(b%2);lc(r,C?l+1:l,a,z.inner,z.from,z.to,h),d=z.from}T=z.from}else{if(T==t||(m?Nt[T-1]!=c:Nt[T-1]==c))break;T--}S?nc(r,T,d,l+1,a,S,h):T<d&&h.push(new Rn(T,d,b)),d=T}}function lc(r,t,i,l,a,o,h){let c=t%2?2:1;lb(r,a,o,l,c),sb(r,a,o,l,c),ab(a,o,l,c),nc(r,a,o,t,i,l,h)}function rb(r,t,i){if(!r)return[new Rn(0,0,t==Tc?1:0)];if(t==sl&&!i.length&&!nb.test(r))return V0(r.length);if(i.length)for(;r.length>Nt.length;)Nt[Nt.length]=256;let l=[],a=t==sl?0:1;return lc(r,a,a,i,0,r.length,l),l}function V0(r){return[new Rn(0,r,0)]}let H0="";function ob(r,t,i,l,a){var o;let h=l.head-r.from,c=Rn.find(t,h,(o=l.bidiLevel)!==null&&o!==void 0?o:-1,l.assoc),d=t[c],p=d.side(a,i);if(h==p){let S=c+=a?1:-1;if(S<0||S>=t.length)return null;d=t[c=S],h=d.side(!a,i),p=d.side(a,i)}let m=Wi(r.text,h,d.forward(a,i));(m<d.from||m>d.to)&&(m=p),H0=r.text.slice(Math.min(h,m),Math.max(h,m));let O=c==(a?t.length-1:0)?null:t[c+(a?1:-1)];return O&&m==p&&O.level+(a?0:1)<d.level?I.cursor(O.side(!a,i)+r.from,O.forward(a,i)?1:-1,O.level):I.cursor(m+r.from,d.forward(a,i)?-1:1,d.level)}function ub(r,t,i){for(let l=t;l<i;l++){let a=X0(r.charCodeAt(l));if(a==1)return sl;if(a==2||a==4)return Tc}return sl}const j0=ut.define(),L0=ut.define(),G0=ut.define(),$0=ut.define(),sc=ut.define(),W0=ut.define(),Y0=ut.define(),wc=ut.define(),Qc=ut.define(),Z0=ut.define({combine:r=>r.some(t=>t)}),hb=ut.define({combine:r=>r.some(t=>t)}),K0=ut.define();class Jl{constructor(t,i="nearest",l="nearest",a=5,o=5,h=!1){this.range=t,this.y=i,this.x=l,this.yMargin=a,this.xMargin=o,this.isSnapshot=h}map(t){return t.empty?this:new Jl(this.range.map(t),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}clip(t){return this.range.to<=t.doc.length?this:new Jl(I.cursor(t.doc.length),this.y,this.x,this.yMargin,this.xMargin,this.isSnapshot)}}const Fr=$t.define({map:(r,t)=>r.map(t)}),J0=$t.define();function vi(r,t,i){let l=r.facet($0);l.length?l[0](t):window.onerror&&window.onerror(String(t),i,void 0,void 0,t)||(i?console.error(i+":",t):console.error(t))}const $i=ut.define({combine:r=>r.length?r[0]:!0});let fb=0;const Yl=ut.define({combine(r){return r.filter((t,i)=>{for(let l=0;l<i;l++)if(r[l].plugin==t.plugin)return!1;return!0})}});class ns{constructor(t,i,l,a,o){this.id=t,this.create=i,this.domEventHandlers=l,this.domEventObservers=a,this.baseExtensions=o(this),this.extension=this.baseExtensions.concat(Yl.of({plugin:this,arg:void 0}))}of(t){return this.baseExtensions.concat(Yl.of({plugin:this,arg:t}))}static define(t,i){const{eventHandlers:l,eventObservers:a,provide:o,decorations:h}=i||{};return new ns(fb++,t,l,a,c=>{let d=[];return h&&d.push(Qa.of(p=>{let m=p.plugin(c);return m?h(m):ne.none})),o&&d.push(o(c)),d})}static fromClass(t,i){return ns.define((l,a)=>new t(l,a),i)}}class Of{constructor(t){this.spec=t,this.mustUpdate=null,this.value=null}get plugin(){return this.spec&&this.spec.plugin}update(t){if(this.value){if(this.mustUpdate){let i=this.mustUpdate;if(this.mustUpdate=null,this.value.update)try{this.value.update(i)}catch(l){if(vi(i.state,l,"CodeMirror plugin crashed"),this.value.destroy)try{this.value.destroy()}catch{}this.deactivate()}}}else if(this.spec)try{this.value=this.spec.plugin.create(t,this.spec.arg)}catch(i){vi(t.state,i,"CodeMirror plugin crashed"),this.deactivate()}return this}destroy(t){var i;if(!((i=this.value)===null||i===void 0)&&i.destroy)try{this.value.destroy()}catch(l){vi(t.state,l,"CodeMirror plugin crashed")}}deactivate(){this.spec=this.value=null}}const F0=ut.define(),kc=ut.define(),Qa=ut.define(),I0=ut.define(),Ac=ut.define(),t1=ut.define();function sO(r,t){let i=r.state.facet(t1);if(!i.length)return i;let l=i.map(o=>o instanceof Function?o(r):o),a=[];return Pt.spans(l,t.from,t.to,{point(){},span(o,h,c,d){let p=o-t.from,m=h-t.from,O=a;for(let S=c.length-1;S>=0;S--,d--){let b=c[S].spec.bidiIsolate,T;if(b==null&&(b=ub(t.text,p,m)),d>0&&O.length&&(T=O[O.length-1]).to==p&&T.direction==b)T.to=m,O=T.inner;else{let z={from:p,to:m,direction:b,inner:[]};O.push(z),O=z.inner}}}}),a}const e1=ut.define();function i1(r){let t=0,i=0,l=0,a=0;for(let o of r.state.facet(e1)){let h=o(r);h&&(h.left!=null&&(t=Math.max(t,h.left)),h.right!=null&&(i=Math.max(i,h.right)),h.top!=null&&(l=Math.max(l,h.top)),h.bottom!=null&&(a=Math.max(a,h.bottom)))}return{left:t,right:i,top:l,bottom:a}}const ca=ut.define();class ei{constructor(t,i,l,a){this.fromA=t,this.toA=i,this.fromB=l,this.toB=a}join(t){return new ei(Math.min(this.fromA,t.fromA),Math.max(this.toA,t.toA),Math.min(this.fromB,t.fromB),Math.max(this.toB,t.toB))}addToSet(t){let i=t.length,l=this;for(;i>0;i--){let a=t[i-1];if(!(a.fromA>l.toA)){if(a.toA<l.fromA)break;l=l.join(a),t.splice(i-1,1)}}return t.splice(i,0,l),t}static extendWithRanges(t,i){if(i.length==0)return t;let l=[];for(let a=0,o=0,h=0,c=0;;a++){let d=a==t.length?null:t[a],p=h-c,m=d?d.fromB:1e9;for(;o<i.length&&i[o]<m;){let O=i[o],S=i[o+1],b=Math.max(c,O),T=Math.min(m,S);if(b<=T&&new ei(b+p,T+p,b,T).addToSet(l),S>m)break;o+=2}if(!d)return l;new ei(d.fromA,d.toA,d.fromB,d.toB).addToSet(l),h=d.toA,c=d.toB}}}class Ro{constructor(t,i,l){this.view=t,this.state=i,this.transactions=l,this.flags=0,this.startState=t.state,this.changes=oe.empty(this.startState.doc.length);for(let o of l)this.changes=this.changes.compose(o.changes);let a=[];this.changes.iterChangedRanges((o,h,c,d)=>a.push(new ei(o,h,c,d))),this.changedRanges=a}static create(t,i,l){return new Ro(t,i,l)}get viewportChanged(){return(this.flags&4)>0}get viewportMoved(){return(this.flags&8)>0}get heightChanged(){return(this.flags&2)>0}get geometryChanged(){return this.docChanged||(this.flags&18)>0}get focusChanged(){return(this.flags&1)>0}get docChanged(){return!this.changes.empty}get selectionSet(){return this.transactions.some(t=>t.selection)}get empty(){return this.flags==0&&this.transactions.length==0}}class aO extends Ut{get length(){return this.view.state.doc.length}constructor(t){super(),this.view=t,this.decorations=[],this.dynamicDecorationMap=[!1],this.domChanged=null,this.hasComposition=null,this.markedForComposition=new Set,this.editContextFormatting=ne.none,this.lastCompositionAfterCursor=!1,this.minWidth=0,this.minWidthFrom=0,this.minWidthTo=0,this.impreciseAnchor=null,this.impreciseHead=null,this.forceSelection=!1,this.lastUpdate=Date.now(),this.setDOM(t.contentDOM),this.children=[new Ft],this.children[0].setParent(this),this.updateDeco(),this.updateInner([new ei(0,0,0,t.state.doc.length)],0,null)}update(t){var i;let l=t.changedRanges;this.minWidth>0&&l.length&&(l.every(({fromA:p,toA:m})=>m<this.minWidthFrom||p>this.minWidthTo)?(this.minWidthFrom=t.changes.mapPos(this.minWidthFrom,1),this.minWidthTo=t.changes.mapPos(this.minWidthTo,1)):this.minWidth=this.minWidthFrom=this.minWidthTo=0),this.updateEditContextFormatting(t);let a=-1;this.view.inputState.composing>=0&&!this.view.observer.editContext&&(!((i=this.domChanged)===null||i===void 0)&&i.newSel?a=this.domChanged.newSel.head:!yb(t.changes,this.hasComposition)&&!t.selectionSet&&(a=t.state.selection.main.head));let o=a>-1?db(this.view,t.changes,a):null;if(this.domChanged=null,this.hasComposition){this.markedForComposition.clear();let{from:p,to:m}=this.hasComposition;l=new ei(p,m,t.changes.mapPos(p,-1),t.changes.mapPos(m,1)).addToSet(l.slice())}this.hasComposition=o?{from:o.range.fromB,to:o.range.toB}:null,(Z.ie||Z.chrome)&&!o&&t&&t.state.doc.lines!=t.startState.doc.lines&&(this.forceSelection=!0);let h=this.decorations,c=this.updateDeco(),d=mb(h,c,t.changes);return l=ei.extendWithRanges(l,d),!(this.flags&7)&&l.length==0?!1:(this.updateInner(l,t.startState.doc.length,o),t.transactions.length&&(this.lastUpdate=Date.now()),!0)}updateInner(t,i,l){this.view.viewState.mustMeasureContent=!0,this.updateChildren(t,i,l);let{observer:a}=this.view;a.ignore(()=>{this.dom.style.height=this.view.viewState.contentHeight/this.view.scaleY+"px",this.dom.style.flexBasis=this.minWidth?this.minWidth+"px":"";let h=Z.chrome||Z.ios?{node:a.selectionRange.focusNode,written:!1}:void 0;this.sync(this.view,h),this.flags&=-8,h&&(h.written||a.selectionRange.focusNode!=h.node)&&(this.forceSelection=!0),this.dom.style.height=""}),this.markedForComposition.forEach(h=>h.flags&=-9);let o=[];if(this.view.viewport.from||this.view.viewport.to<this.view.state.doc.length)for(let h of this.children)h instanceof Ki&&h.widget instanceof ec&&o.push(h.dom);a.updateGaps(o)}updateChildren(t,i,l){let a=l?l.range.addToSet(t.slice()):t,o=this.childCursor(i);for(let h=a.length-1;;h--){let c=h>=0?a[h]:null;if(!c)break;let{fromA:d,toA:p,fromB:m,toB:O}=c,S,b,T,z;if(l&&l.range.fromB<O&&l.range.toB>m){let W=Sa.build(this.view.state.doc,m,l.range.fromB,this.decorations,this.dynamicDecorationMap),X=Sa.build(this.view.state.doc,l.range.toB,O,this.decorations,this.dynamicDecorationMap);b=W.breakAtStart,T=W.openStart,z=X.openEnd;let J=this.compositionView(l);X.breakAtStart?J.breakAfter=1:X.content.length&&J.merge(J.length,J.length,X.content[0],!1,X.openStart,0)&&(J.breakAfter=X.content[0].breakAfter,X.content.shift()),W.content.length&&J.merge(0,0,W.content[W.content.length-1],!0,0,W.openEnd)&&W.content.pop(),S=W.content.concat(J).concat(X.content)}else({content:S,breakAtStart:b,openStart:T,openEnd:z}=Sa.build(this.view.state.doc,m,O,this.decorations,this.dynamicDecorationMap));let{i:C,off:q}=o.findPos(p,1),{i:$,off:j}=o.findPos(d,-1);z0(this,$,j,C,q,S,b,T,z)}l&&this.fixCompositionDOM(l)}updateEditContextFormatting(t){this.editContextFormatting=this.editContextFormatting.map(t.changes);for(let i of t.transactions)for(let l of i.effects)l.is(J0)&&(this.editContextFormatting=l.value)}compositionView(t){let i=new ai(t.text.nodeValue);i.flags|=8;for(let{deco:a}of t.marks)i=new Ji(a,[i],i.length);let l=new Ft;return l.append(i,0),l}fixCompositionDOM(t){let i=(o,h)=>{h.flags|=8|(h.children.some(d=>d.flags&7)?1:0),this.markedForComposition.add(h);let c=Ut.get(o);c&&c!=h&&(c.dom=null),h.setDOM(o)},l=this.childPos(t.range.fromB,1),a=this.children[l.i];i(t.line,a);for(let o=t.marks.length-1;o>=-1;o--)l=a.childPos(l.off,1),a=a.children[l.i],i(o>=0?t.marks[o].node:t.text,a)}updateSelection(t=!1,i=!1){(t||!this.view.observer.selectionRange.focusNode)&&this.view.observer.readSelectionRange();let l=this.view.root.activeElement,a=l==this.dom,o=!a&&!(this.view.state.facet($i)||this.dom.tabIndex>-1)&&po(this.dom,this.view.observer.selectionRange)&&!(l&&this.dom.contains(l));if(!(a||i||o))return;let h=this.forceSelection;this.forceSelection=!1;let c=this.view.state.selection.main,d=this.moveToLine(this.domAtPos(c.anchor)),p=c.empty?d:this.moveToLine(this.domAtPos(c.head));if(Z.gecko&&c.empty&&!this.hasComposition&&cb(d)){let O=document.createTextNode("");this.view.observer.ignore(()=>d.node.insertBefore(O,d.node.childNodes[d.offset]||null)),d=p=new be(O,0),h=!0}let m=this.view.observer.selectionRange;(h||!m.focusNode||(!ya(d.node,d.offset,m.anchorNode,m.anchorOffset)||!ya(p.node,p.offset,m.focusNode,m.focusOffset))&&!this.suppressWidgetCursorChange(m,c))&&(this.view.observer.ignore(()=>{Z.android&&Z.chrome&&this.dom.contains(m.focusNode)&&Ob(m.focusNode,this.dom)&&(this.dom.blur(),this.dom.focus({preventScroll:!0}));let O=Ta(this.view.root);if(O)if(c.empty){if(Z.gecko){let S=gb(d.node,d.offset);if(S&&S!=3){let b=(S==1?A0:R0)(d.node,d.offset);b&&(d=new be(b.node,b.offset))}}O.collapse(d.node,d.offset),c.bidiLevel!=null&&O.caretBidiLevel!==void 0&&(O.caretBidiLevel=c.bidiLevel)}else if(O.extend){O.collapse(d.node,d.offset);try{O.extend(p.node,p.offset)}catch{}}else{let S=document.createRange();c.anchor>c.head&&([d,p]=[p,d]),S.setEnd(p.node,p.offset),S.setStart(d.node,d.offset),O.removeAllRanges(),O.addRange(S)}o&&this.view.root.activeElement==this.dom&&(this.dom.blur(),l&&l.focus())}),this.view.observer.setSelectionRange(d,p)),this.impreciseAnchor=d.precise?null:new be(m.anchorNode,m.anchorOffset),this.impreciseHead=p.precise?null:new be(m.focusNode,m.focusOffset)}suppressWidgetCursorChange(t,i){return this.hasComposition&&i.empty&&ya(t.focusNode,t.focusOffset,t.anchorNode,t.anchorOffset)&&this.posFromDOM(t.focusNode,t.focusOffset)==i.head}enforceCursorAssoc(){if(this.hasComposition)return;let{view:t}=this,i=t.state.selection.main,l=Ta(t.root),{anchorNode:a,anchorOffset:o}=t.observer.selectionRange;if(!l||!i.empty||!i.assoc||!l.modify)return;let h=Ft.find(this,i.head);if(!h)return;let c=h.posAtStart;if(i.head==c||i.head==c+h.length)return;let d=this.coordsAt(i.head,-1),p=this.coordsAt(i.head,1);if(!d||!p||d.bottom>p.top)return;let m=this.domAtPos(i.head+i.assoc);l.collapse(m.node,m.offset),l.modify("move",i.assoc<0?"forward":"backward","lineboundary"),t.observer.readSelectionRange();let O=t.observer.selectionRange;t.docView.posFromDOM(O.anchorNode,O.anchorOffset)!=i.from&&l.collapse(a,o)}moveToLine(t){let i=this.dom,l;if(t.node!=i)return t;for(let a=t.offset;!l&&a<i.childNodes.length;a++){let o=Ut.get(i.childNodes[a]);o instanceof Ft&&(l=o.domAtPos(0))}for(let a=t.offset-1;!l&&a>=0;a--){let o=Ut.get(i.childNodes[a]);o instanceof Ft&&(l=o.domAtPos(o.length))}return l?new be(l.node,l.offset,!0):t}nearest(t){for(let i=t;i;){let l=Ut.get(i);if(l&&l.rootView==this)return l;i=i.parentNode}return null}posFromDOM(t,i){let l=this.nearest(t);if(!l)throw new RangeError("Trying to find position for a DOM position outside of the document");return l.localPosFromDOM(t,i)+l.posAtStart}domAtPos(t){let{i,off:l}=this.childCursor().findPos(t,-1);for(;i<this.children.length-1;){let a=this.children[i];if(l<a.length||a instanceof Ft)break;i++,l=0}return this.children[i].domAtPos(l)}coordsAt(t,i){let l=null,a=0;for(let o=this.length,h=this.children.length-1;h>=0;h--){let c=this.children[h],d=o-c.breakAfter,p=d-c.length;if(d<t)break;if(p<=t&&(p<t||c.covers(-1))&&(d>t||c.covers(1))&&(!l||c instanceof Ft&&!(l instanceof Ft&&i>=0)))l=c,a=p;else if(l&&p==t&&d==t&&c instanceof Ki&&Math.abs(i)<2){if(c.deco.startSide<0)break;h&&(l=null)}o=p}return l?l.coordsAt(t-a,i):null}coordsForChar(t){let{i,off:l}=this.childPos(t,1),a=this.children[i];if(!(a instanceof Ft))return null;for(;a.children.length;){let{i:c,off:d}=a.childPos(l,1);for(;;c++){if(c==a.children.length)return null;if((a=a.children[c]).length)break}l=d}if(!(a instanceof ai))return null;let o=Wi(a.text,l);if(o==l)return null;let h=ll(a.dom,l,o).getClientRects();for(let c=0;c<h.length;c++){let d=h[c];if(c==h.length-1||d.top<d.bottom&&d.left<d.right)return d}return null}measureVisibleLineHeights(t){let i=[],{from:l,to:a}=t,o=this.view.contentDOM.clientWidth,h=o>Math.max(this.view.scrollDOM.clientWidth,this.minWidth)+1,c=-1,d=this.view.textDirection==pe.LTR;for(let p=0,m=0;m<this.children.length;m++){let O=this.children[m],S=p+O.length;if(S>a)break;if(p>=l){let b=O.dom.getBoundingClientRect();if(i.push(b.height),h){let T=O.dom.lastChild,z=T?wa(T):[];if(z.length){let C=z[z.length-1],q=d?C.right-b.left:b.right-C.left;q>c&&(c=q,this.minWidth=o,this.minWidthFrom=p,this.minWidthTo=S)}}}p=S+O.breakAfter}return i}textDirectionAt(t){let{i}=this.childPos(t,1);return getComputedStyle(this.children[i].dom).direction=="rtl"?pe.RTL:pe.LTR}measureTextSize(){for(let o of this.children)if(o instanceof Ft){let h=o.measureTextSize();if(h)return h}let t=document.createElement("div"),i,l,a;return t.className="cm-line",t.style.width="99999px",t.style.position="absolute",t.textContent="abc def ghi jkl mno pqr stu",this.view.observer.ignore(()=>{this.dom.appendChild(t);let o=wa(t.firstChild)[0];i=t.getBoundingClientRect().height,l=o?o.width/27:7,a=o?o.height:i,t.remove()}),{lineHeight:i,charWidth:l,textHeight:a}}childCursor(t=this.length){let i=this.children.length;return i&&(t-=this.children[--i].length),new E0(this.children,t,i)}computeBlockGapDeco(){let t=[],i=this.view.viewState;for(let l=0,a=0;;a++){let o=a==i.viewports.length?null:i.viewports[a],h=o?o.from-1:this.length;if(h>l){let c=(i.lineBlockAt(h).bottom-i.lineBlockAt(l).top)/this.view.scaleY;t.push(ne.replace({widget:new ec(c),block:!0,inclusive:!0,isBlockGap:!0}).range(l,h))}if(!o)break;l=o.to+1}return ne.set(t)}updateDeco(){let t=1,i=this.view.state.facet(Qa).map(o=>(this.dynamicDecorationMap[t++]=typeof o=="function")?o(this.view):o),l=!1,a=this.view.state.facet(I0).map((o,h)=>{let c=typeof o=="function";return c&&(l=!0),c?o(this.view):o});for(a.length&&(this.dynamicDecorationMap[t++]=l,i.push(Pt.join(a))),this.decorations=[this.editContextFormatting,...i,this.computeBlockGapDeco(),this.view.viewState.lineGapDeco];t<this.decorations.length;)this.dynamicDecorationMap[t++]=!1;return this.decorations}scrollIntoView(t){if(t.isSnapshot){let p=this.view.viewState.lineBlockAt(t.range.head);this.view.scrollDOM.scrollTop=p.top-t.yMargin,this.view.scrollDOM.scrollLeft=t.xMargin;return}for(let p of this.view.state.facet(K0))try{if(p(this.view,t.range,t))return!0}catch(m){vi(this.view.state,m,"scroll handler")}let{range:i}=t,l=this.coordsAt(i.head,i.empty?i.assoc:i.head>i.anchor?-1:1),a;if(!l)return;!i.empty&&(a=this.coordsAt(i.anchor,i.anchor>i.head?-1:1))&&(l={left:Math.min(l.left,a.left),top:Math.min(l.top,a.top),right:Math.max(l.right,a.right),bottom:Math.max(l.bottom,a.bottom)});let o=i1(this.view),h={left:l.left-o.left,top:l.top-o.top,right:l.right+o.right,bottom:l.bottom+o.bottom},{offsetWidth:c,offsetHeight:d}=this.view.scrollDOM;jv(this.view.scrollDOM,h,i.head<i.anchor?-1:1,t.x,t.y,Math.max(Math.min(t.xMargin,c),-c),Math.max(Math.min(t.yMargin,d),-d),this.view.textDirection==pe.LTR)}}function cb(r){return r.node.nodeType==1&&r.node.firstChild&&(r.offset==0||r.node.childNodes[r.offset-1].contentEditable=="false")&&(r.offset==r.node.childNodes.length||r.node.childNodes[r.offset].contentEditable=="false")}function n1(r,t){let i=r.observer.selectionRange;if(!i.focusNode)return null;let l=A0(i.focusNode,i.focusOffset),a=R0(i.focusNode,i.focusOffset),o=l||a;if(a&&l&&a.node!=l.node){let c=Ut.get(a.node);if(!c||c instanceof ai&&c.text!=a.node.nodeValue)o=a;else if(r.docView.lastCompositionAfterCursor){let d=Ut.get(l.node);!d||d instanceof ai&&d.text!=l.node.nodeValue||(o=a)}}if(r.docView.lastCompositionAfterCursor=o!=l,!o)return null;let h=t-o.offset;return{from:h,to:h+o.node.nodeValue.length,node:o.node}}function db(r,t,i){let l=n1(r,i);if(!l)return null;let{node:a,from:o,to:h}=l,c=a.nodeValue;if(/[\n\r]/.test(c)||r.state.doc.sliceString(l.from,l.to)!=c)return null;let d=t.invertedDesc,p=new ei(d.mapPos(o),d.mapPos(h),o,h),m=[];for(let O=a.parentNode;;O=O.parentNode){let S=Ut.get(O);if(S instanceof Ji)m.push({node:O,deco:S.mark});else{if(S instanceof Ft||O.nodeName=="DIV"&&O.parentNode==r.contentDOM)return{range:p,text:a,marks:m,line:O};if(O!=r.contentDOM)m.push({node:O,deco:new za({inclusive:!0,attributes:Iv(O),tagName:O.tagName.toLowerCase()})});else return null}}}function gb(r,t){return r.nodeType!=1?0:(t&&r.childNodes[t-1].contentEditable=="false"?1:0)|(t<r.childNodes.length&&r.childNodes[t].contentEditable=="false"?2:0)}let pb=class{constructor(){this.changes=[]}compareRange(t,i){mo(t,i,this.changes)}comparePoint(t,i){mo(t,i,this.changes)}boundChange(t){mo(t,t,this.changes)}};function mb(r,t,i){let l=new pb;return Pt.compare(r,t,i,l),l.changes}function Ob(r,t){for(let i=r;i&&i!=t;i=i.assignedSlot||i.parentNode)if(i.nodeType==1&&i.contentEditable=="false")return!0;return!1}function yb(r,t){let i=!1;return t&&r.iterChangedRanges((l,a)=>{l<t.to&&a>t.from&&(i=!0)}),i}function Sb(r,t,i=1){let l=r.charCategorizer(t),a=r.doc.lineAt(t),o=t-a.from;if(a.length==0)return I.cursor(t);o==0?i=1:o==a.length&&(i=-1);let h=o,c=o;i<0?h=Wi(a.text,o,!1):c=Wi(a.text,o);let d=l(a.text.slice(h,c));for(;h>0;){let p=Wi(a.text,h,!1);if(l(a.text.slice(p,h))!=d)break;h=p}for(;c<a.length;){let p=Wi(a.text,c);if(l(a.text.slice(c,p))!=d)break;c=p}return I.range(h+a.from,c+a.from)}function vb(r,t){return t.left>r?t.left-r:Math.max(0,r-t.right)}function bb(r,t){return t.top>r?t.top-r:Math.max(0,r-t.bottom)}function yf(r,t){return r.top<t.bottom-1&&r.bottom>t.top+1}function rO(r,t){return t<r.top?{top:t,left:r.left,right:r.right,bottom:r.bottom}:r}function oO(r,t){return t>r.bottom?{top:r.top,left:r.left,right:r.right,bottom:t}:r}function ac(r,t,i){let l,a,o,h,c=!1,d,p,m,O;for(let T=r.firstChild;T;T=T.nextSibling){let z=wa(T);for(let C=0;C<z.length;C++){let q=z[C];a&&yf(a,q)&&(q=rO(oO(q,a.bottom),a.top));let $=vb(t,q),j=bb(i,q);if($==0&&j==0)return T.nodeType==3?uO(T,t,i):ac(T,t,i);if(!l||h>j||h==j&&o>$){l=T,a=q,o=$,h=j;let W=j?i<q.top?-1:1:$?t<q.left?-1:1:0;c=!W||(W>0?C<z.length-1:C>0)}$==0?i>q.bottom&&(!m||m.bottom<q.bottom)?(d=T,m=q):i<q.top&&(!O||O.top>q.top)&&(p=T,O=q):m&&yf(m,q)?m=oO(m,q.bottom):O&&yf(O,q)&&(O=rO(O,q.top))}}if(m&&m.bottom>=i?(l=d,a=m):O&&O.top<=i&&(l=p,a=O),!l)return{node:r,offset:0};let S=Math.max(a.left,Math.min(a.right,t));if(l.nodeType==3)return uO(l,S,i);if(c&&l.contentEditable!="false")return ac(l,S,i);let b=Array.prototype.indexOf.call(r.childNodes,l)+(t>=(a.left+a.right)/2?1:0);return{node:r,offset:b}}function uO(r,t,i){let l=r.nodeValue.length,a=-1,o=1e9,h=0;for(let c=0;c<l;c++){let d=ll(r,c,c+1).getClientRects();for(let p=0;p<d.length;p++){let m=d[p];if(m.top==m.bottom)continue;h||(h=t-m.left);let O=(m.top>i?m.top-i:i-m.bottom)-1;if(m.left-1<=t&&m.right+1>=t&&O<o){let S=t>=(m.left+m.right)/2,b=S;if((Z.chrome||Z.gecko)&&ll(r,c).getBoundingClientRect().left==m.right&&(b=!S),O<=0)return{node:r,offset:c+(b?1:0)};a=c+(b?1:0),o=O}}}return{node:r,offset:a>-1?a:h>0?r.nodeValue.length:0}}function l1(r,t,i,l=-1){var a,o;let h=r.contentDOM.getBoundingClientRect(),c=h.top+r.viewState.paddingTop,d,{docHeight:p}=r.viewState,{x:m,y:O}=t,S=O-c;if(S<0)return 0;if(S>p)return r.state.doc.length;for(let W=r.viewState.heightOracle.textHeight/2,X=!1;d=r.elementAtHeight(S),d.type!=Ti.Text;)for(;S=l>0?d.bottom+W:d.top-W,!(S>=0&&S<=p);){if(X)return i?null:0;X=!0,l=-l}O=c+S;let b=d.from;if(b<r.viewport.from)return r.viewport.from==0?0:i?null:hO(r,h,d,m,O);if(b>r.viewport.to)return r.viewport.to==r.state.doc.length?r.state.doc.length:i?null:hO(r,h,d,m,O);let T=r.dom.ownerDocument,z=r.root.elementFromPoint?r.root:T,C=z.elementFromPoint(m,O);C&&!r.contentDOM.contains(C)&&(C=null),C||(m=Math.max(h.left+1,Math.min(h.right-1,m)),C=z.elementFromPoint(m,O),C&&!r.contentDOM.contains(C)&&(C=null));let q,$=-1;if(C&&((a=r.docView.nearest(C))===null||a===void 0?void 0:a.isEditable)!=!1){if(T.caretPositionFromPoint){let W=T.caretPositionFromPoint(m,O);W&&({offsetNode:q,offset:$}=W)}else if(T.caretRangeFromPoint){let W=T.caretRangeFromPoint(m,O);W&&({startContainer:q,startOffset:$}=W,(!r.contentDOM.contains(q)||Z.safari&&xb(q,$,m)||Z.chrome&&Tb(q,$,m))&&(q=void 0))}q&&($=Math.min(wi(q),$))}if(!q||!r.docView.dom.contains(q)){let W=Ft.find(r.docView,b);if(!W)return S>d.top+d.height/2?d.to:d.from;({node:q,offset:$}=ac(W.dom,m,O))}let j=r.docView.nearest(q);if(!j)return null;if(j.isWidget&&((o=j.dom)===null||o===void 0?void 0:o.nodeType)==1){let W=j.dom.getBoundingClientRect();return t.y<W.top||t.y<=W.bottom&&t.x<=(W.left+W.right)/2?j.posAtStart:j.posAtEnd}else return j.localPosFromDOM(q,$)+j.posAtStart}function hO(r,t,i,l,a){let o=Math.round((l-t.left)*r.defaultCharacterWidth);if(r.lineWrapping&&i.height>r.defaultLineHeight*1.5){let c=r.viewState.heightOracle.textHeight,d=Math.floor((a-i.top-(r.defaultLineHeight-c)*.5)/c);o+=d*r.viewState.heightOracle.lineLength}let h=r.state.sliceDoc(i.from,i.to);return i.from+Uv(h,o,r.state.tabSize)}function xb(r,t,i){let l;if(r.nodeType!=3||t!=(l=r.nodeValue.length))return!1;for(let a=r.nextSibling;a;a=a.nextSibling)if(a.nodeType!=1||a.nodeName!="BR")return!1;return ll(r,l-1,l).getBoundingClientRect().left>i}function Tb(r,t,i){if(t!=0)return!1;for(let a=r;;){let o=a.parentNode;if(!o||o.nodeType!=1||o.firstChild!=a)return!1;if(o.classList.contains("cm-line"))break;a=o}let l=r.nodeType==1?r.getBoundingClientRect():ll(r,0,Math.max(r.nodeValue.length,1)).getBoundingClientRect();return i-l.left>5}function wb(r,t,i){let l=r.lineBlockAt(t);if(Array.isArray(l.type)){let a;for(let o of l.type){if(o.from>t)break;if(!(o.to<t)){if(o.from<t&&o.to>t)return o;(!a||o.type==Ti.Text&&(a.type!=o.type||(i<0?o.from<t:o.to>t)))&&(a=o)}}return a||l}return l}function Qb(r,t,i,l){let a=wb(r,t.head,t.assoc||-1),o=!l||a.type!=Ti.Text||!(r.lineWrapping||a.widgetLineBreaks)?null:r.coordsAtPos(t.assoc<0&&t.head>a.from?t.head-1:t.head);if(o){let h=r.dom.getBoundingClientRect(),c=r.textDirectionAt(a.from),d=r.posAtCoords({x:i==(c==pe.LTR)?h.right-1:h.left+1,y:(o.top+o.bottom)/2});if(d!=null)return I.cursor(d,i?-1:1)}return I.cursor(i?a.to:a.from,i?-1:1)}function fO(r,t,i,l){let a=r.state.doc.lineAt(t.head),o=r.bidiSpans(a),h=r.textDirectionAt(a.from);for(let c=t,d=null;;){let p=ob(a,o,h,c,i),m=H0;if(!p){if(a.number==(i?r.state.doc.lines:1))return c;m=`
`,a=r.state.doc.line(a.number+(i?1:-1)),o=r.bidiSpans(a),p=r.visualLineSide(a,!i)}if(d){if(!d(m))return c}else{if(!l)return p;d=l(m)}c=p}}function kb(r,t,i){let l=r.state.charCategorizer(t),a=l(i);return o=>{let h=l(o);return a==Yi.Space&&(a=h),a==h}}function Ab(r,t,i,l){let a=t.head,o=i?1:-1;if(a==(i?r.state.doc.length:0))return I.cursor(a,t.assoc);let h=t.goalColumn,c,d=r.contentDOM.getBoundingClientRect(),p=r.coordsAtPos(a,t.assoc||-1),m=r.documentTop;if(p)h==null&&(h=p.left-d.left),c=o<0?p.top:p.bottom;else{let b=r.viewState.lineBlockAt(a);h==null&&(h=Math.min(d.right-d.left,r.defaultCharacterWidth*(a-b.from))),c=(o<0?b.top:b.bottom)+m}let O=d.left+h,S=l??r.viewState.heightOracle.textHeight>>1;for(let b=0;;b+=10){let T=c+(S+b)*o,z=l1(r,{x:O,y:T},!1,o);if(T<d.top||T>d.bottom||(o<0?z<a:z>a)){let C=r.docView.coordsForChar(z),q=!C||T<C.top?-1:1;return I.cursor(z,q,void 0,h)}}}function Oo(r,t,i){for(;;){let l=0;for(let a of r)a.between(t-1,t+1,(o,h,c)=>{if(t>o&&t<h){let d=l||i||(t-o<h-t?-1:1);t=d<0?o:h,l=d}});if(!l)return t}}function Sf(r,t,i){let l=Oo(r.state.facet(Ac).map(a=>a(r)),i.from,t.head>i.from?-1:1);return l==i.from?i:I.cursor(l,l<i.from?1:-1)}const da="￿";class Rb{constructor(t,i){this.points=t,this.text="",this.lineSeparator=i.facet(At.lineSeparator)}append(t){this.text+=t}lineBreak(){this.text+=da}readRange(t,i){if(!t)return this;let l=t.parentNode;for(let a=t;;){this.findPointBefore(l,a);let o=this.text.length;this.readNode(a);let h=a.nextSibling;if(h==i)break;let c=Ut.get(a),d=Ut.get(h);(c&&d?c.breakAfter:(c?c.breakAfter:ko(a))||ko(h)&&(a.nodeName!="BR"||a.cmIgnore)&&this.text.length>o)&&this.lineBreak(),a=h}return this.findPointBefore(l,i),this}readTextNode(t){let i=t.nodeValue;for(let l of this.points)l.node==t&&(l.pos=this.text.length+Math.min(l.offset,i.length));for(let l=0,a=this.lineSeparator?null:/\r\n?|\n/g;;){let o=-1,h=1,c;if(this.lineSeparator?(o=i.indexOf(this.lineSeparator,l),h=this.lineSeparator.length):(c=a.exec(i))&&(o=c.index,h=c[0].length),this.append(i.slice(l,o<0?i.length:o)),o<0)break;if(this.lineBreak(),h>1)for(let d of this.points)d.node==t&&d.pos>this.text.length&&(d.pos-=h-1);l=o+h}}readNode(t){if(t.cmIgnore)return;let i=Ut.get(t),l=i&&i.overrideDOMText;if(l!=null){this.findPointInside(t,l.length);for(let a=l.iter();!a.next().done;)a.lineBreak?this.lineBreak():this.append(a.value)}else t.nodeType==3?this.readTextNode(t):t.nodeName=="BR"?t.nextSibling&&this.lineBreak():t.nodeType==1&&this.readRange(t.firstChild,null)}findPointBefore(t,i){for(let l of this.points)l.node==t&&t.childNodes[l.offset]==i&&(l.pos=this.text.length)}findPointInside(t,i){for(let l of this.points)(t.nodeType==3?l.node==t:t.contains(l.node))&&(l.pos=this.text.length+(Eb(t,l.node,l.offset)?i:0))}}function Eb(r,t,i){for(;;){if(!t||i<wi(t))return!1;if(t==r)return!0;i=nl(t)+1,t=t.parentNode}}class cO{constructor(t,i){this.node=t,this.offset=i,this.pos=-1}}class zb{constructor(t,i,l,a){this.typeOver=a,this.bounds=null,this.text="",this.domChanged=i>-1;let{impreciseHead:o,impreciseAnchor:h}=t.docView;if(t.state.readOnly&&i>-1)this.newSel=null;else if(i>-1&&(this.bounds=t.docView.domBoundsAround(i,l,0))){let c=o||h?[]:Db(t),d=new Rb(c,t.state);d.readRange(this.bounds.startDOM,this.bounds.endDOM),this.text=d.text,this.newSel=qb(c,this.bounds.from)}else{let c=t.observer.selectionRange,d=o&&o.node==c.focusNode&&o.offset==c.focusOffset||!Zf(t.contentDOM,c.focusNode)?t.state.selection.main.head:t.docView.posFromDOM(c.focusNode,c.focusOffset),p=h&&h.node==c.anchorNode&&h.offset==c.anchorOffset||!Zf(t.contentDOM,c.anchorNode)?t.state.selection.main.anchor:t.docView.posFromDOM(c.anchorNode,c.anchorOffset),m=t.viewport;if((Z.ios||Z.chrome)&&t.state.selection.main.empty&&d!=p&&(m.from>0||m.to<t.state.doc.length)){let O=Math.min(d,p),S=Math.max(d,p),b=m.from-O,T=m.to-S;(b==0||b==1||O==0)&&(T==0||T==-1||S==t.state.doc.length)&&(d=0,p=t.state.doc.length)}this.newSel=I.single(p,d)}}}function s1(r,t){let i,{newSel:l}=t,a=r.state.selection.main,o=r.inputState.lastKeyTime>Date.now()-100?r.inputState.lastKeyCode:-1;if(t.bounds){let{from:h,to:c}=t.bounds,d=a.from,p=null;(o===8||Z.android&&t.text.length<c-h)&&(d=a.to,p="end");let m=Cb(r.state.doc.sliceString(h,c,da),t.text,d-h,p);m&&(Z.chrome&&o==13&&m.toB==m.from+2&&t.text.slice(m.from,m.toB)==da+da&&m.toB--,i={from:h+m.from,to:h+m.toA,insert:wt.of(t.text.slice(m.from,m.toB).split(da))})}else l&&(!r.hasFocus&&r.state.facet($i)||l.main.eq(a))&&(l=null);if(!i&&!l)return!1;if(!i&&t.typeOver&&!a.empty&&l&&l.main.empty?i={from:a.from,to:a.to,insert:r.state.doc.slice(a.from,a.to)}:(Z.mac||Z.android)&&i&&i.from==i.to&&i.from==a.head-1&&/^\. ?$/.test(i.insert.toString())&&r.contentDOM.getAttribute("autocorrect")=="off"?(l&&i.insert.length==2&&(l=I.single(l.main.anchor-1,l.main.head-1)),i={from:i.from,to:i.to,insert:wt.of([i.insert.toString().replace("."," ")])}):i&&i.from>=a.from&&i.to<=a.to&&(i.from!=a.from||i.to!=a.to)&&a.to-a.from-(i.to-i.from)<=4?i={from:a.from,to:a.to,insert:r.state.doc.slice(a.from,i.from).append(i.insert).append(r.state.doc.slice(i.to,a.to))}:Z.chrome&&i&&i.from==i.to&&i.from==a.head&&i.insert.toString()==`
 `&&r.lineWrapping&&(l&&(l=I.single(l.main.anchor-1,l.main.head-1)),i={from:a.from,to:a.to,insert:wt.of([" "])}),i)return Rc(r,i,l,o);if(l&&!l.main.eq(a)){let h=!1,c="select";return r.inputState.lastSelectionTime>Date.now()-50&&(r.inputState.lastSelectionOrigin=="select"&&(h=!0),c=r.inputState.lastSelectionOrigin),r.dispatch({selection:l,scrollIntoView:h,userEvent:c}),!0}else return!1}function Rc(r,t,i,l=-1){if(Z.ios&&r.inputState.flushIOSKey(t))return!0;let a=r.state.selection.main;if(Z.android&&(t.to==a.to&&(t.from==a.from||t.from==a.from-1&&r.state.sliceDoc(t.from,a.from)==" ")&&t.insert.length==1&&t.insert.lines==2&&Kl(r.contentDOM,"Enter",13)||(t.from==a.from-1&&t.to==a.to&&t.insert.length==0||l==8&&t.insert.length<t.to-t.from&&t.to>a.head)&&Kl(r.contentDOM,"Backspace",8)||t.from==a.from&&t.to==a.to+1&&t.insert.length==0&&Kl(r.contentDOM,"Delete",46)))return!0;let o=t.insert.toString();r.inputState.composing>=0&&r.inputState.composing++;let h,c=()=>h||(h=Mb(r,t,i));return r.state.facet(W0).some(d=>d(r,t.from,t.to,o,c))||r.dispatch(c()),!0}function Mb(r,t,i){let l,a=r.state,o=a.selection.main;if(t.from>=o.from&&t.to<=o.to&&t.to-t.from>=(o.to-o.from)/3&&(!i||i.main.empty&&i.main.from==t.from+t.insert.length)&&r.inputState.composing<0){let c=o.from<t.from?a.sliceDoc(o.from,t.from):"",d=o.to>t.to?a.sliceDoc(t.to,o.to):"";l=a.replaceSelection(r.state.toText(c+t.insert.sliceString(0,void 0,r.state.lineBreak)+d))}else{let c=a.changes(t),d=i&&i.main.to<=c.newLength?i.main:void 0;if(a.selection.ranges.length>1&&r.inputState.composing>=0&&t.to<=o.to&&t.to>=o.to-10){let p=r.state.sliceDoc(t.from,t.to),m,O=i&&n1(r,i.main.head);if(O){let T=t.insert.length-(t.to-t.from);m={from:O.from,to:O.to-T}}else m=r.state.doc.lineAt(o.head);let S=o.to-t.to,b=o.to-o.from;l=a.changeByRange(T=>{if(T.from==o.from&&T.to==o.to)return{changes:c,range:d||T.map(c)};let z=T.to-S,C=z-p.length;if(T.to-T.from!=b||r.state.sliceDoc(C,z)!=p||T.to>=m.from&&T.from<=m.to)return{range:T};let q=a.changes({from:C,to:z,insert:t.insert}),$=T.to-o.to;return{changes:q,range:d?I.range(Math.max(0,d.anchor+$),Math.max(0,d.head+$)):T.map(q)}})}else l={changes:c,selection:d&&a.selection.replaceRange(d)}}let h="input.type";return(r.composing||r.inputState.compositionPendingChange&&r.inputState.compositionEndedAt>Date.now()-50)&&(r.inputState.compositionPendingChange=!1,h+=".compose",r.inputState.compositionFirstChange&&(h+=".start",r.inputState.compositionFirstChange=!1)),a.update(l,{userEvent:h,scrollIntoView:!0})}function Cb(r,t,i,l){let a=Math.min(r.length,t.length),o=0;for(;o<a&&r.charCodeAt(o)==t.charCodeAt(o);)o++;if(o==a&&r.length==t.length)return null;let h=r.length,c=t.length;for(;h>0&&c>0&&r.charCodeAt(h-1)==t.charCodeAt(c-1);)h--,c--;if(l=="end"){let d=Math.max(0,o-Math.min(h,c));i-=h+d-o}if(h<o&&r.length<t.length){let d=i<=o&&i>=h?o-i:0;o-=d,c=o+(c-h),h=o}else if(c<o){let d=i<=o&&i>=c?o-i:0;o-=d,h=o+(h-c),c=o}return{from:o,toA:h,toB:c}}function Db(r){let t=[];if(r.root.activeElement!=r.contentDOM)return t;let{anchorNode:i,anchorOffset:l,focusNode:a,focusOffset:o}=r.observer.selectionRange;return i&&(t.push(new cO(i,l)),(a!=i||o!=l)&&t.push(new cO(a,o))),t}function qb(r,t){if(r.length==0)return null;let i=r[0].pos,l=r.length==2?r[1].pos:i;return i>-1&&l>-1?I.single(i+t,l+t):null}class _b{setSelectionOrigin(t){this.lastSelectionOrigin=t,this.lastSelectionTime=Date.now()}constructor(t){this.view=t,this.lastKeyCode=0,this.lastKeyTime=0,this.lastTouchTime=0,this.lastFocusTime=0,this.lastScrollTop=0,this.lastScrollLeft=0,this.pendingIOSKey=void 0,this.tabFocusMode=-1,this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastContextMenu=0,this.scrollHandlers=[],this.handlers=Object.create(null),this.composing=-1,this.compositionFirstChange=null,this.compositionEndedAt=0,this.compositionPendingKey=!1,this.compositionPendingChange=!1,this.mouseSelection=null,this.draggedContent=null,this.handleEvent=this.handleEvent.bind(this),this.notifiedFocused=t.hasFocus,Z.safari&&t.contentDOM.addEventListener("input",()=>null),Z.gecko&&Jb(t.contentDOM.ownerDocument)}handleEvent(t){!jb(this.view,t)||this.ignoreDuringComposition(t)||t.type=="keydown"&&this.keydown(t)||(this.view.updateState!=0?Promise.resolve().then(()=>this.runHandlers(t.type,t)):this.runHandlers(t.type,t))}runHandlers(t,i){let l=this.handlers[t];if(l){for(let a of l.observers)a(this.view,i);for(let a of l.handlers){if(i.defaultPrevented)break;if(a(this.view,i)){i.preventDefault();break}}}}ensureHandlers(t){let i=Nb(t),l=this.handlers,a=this.view.contentDOM;for(let o in i)if(o!="scroll"){let h=!i[o].handlers.length,c=l[o];c&&h!=!c.handlers.length&&(a.removeEventListener(o,this.handleEvent),c=null),c||a.addEventListener(o,this.handleEvent,{passive:h})}for(let o in l)o!="scroll"&&!i[o]&&a.removeEventListener(o,this.handleEvent);this.handlers=i}keydown(t){if(this.lastKeyCode=t.keyCode,this.lastKeyTime=Date.now(),t.keyCode==9&&this.tabFocusMode>-1&&(!this.tabFocusMode||Date.now()<=this.tabFocusMode))return!0;if(this.tabFocusMode>0&&t.keyCode!=27&&r1.indexOf(t.keyCode)<0&&(this.tabFocusMode=-1),Z.android&&Z.chrome&&!t.synthetic&&(t.keyCode==13||t.keyCode==8))return this.view.observer.delayAndroidKey(t.key,t.keyCode),!0;let i;return Z.ios&&!t.synthetic&&!t.altKey&&!t.metaKey&&((i=a1.find(l=>l.keyCode==t.keyCode))&&!t.ctrlKey||Ub.indexOf(t.key)>-1&&t.ctrlKey&&!t.shiftKey)?(this.pendingIOSKey=i||t,setTimeout(()=>this.flushIOSKey(),250),!0):(t.keyCode!=229&&this.view.observer.forceFlush(),!1)}flushIOSKey(t){let i=this.pendingIOSKey;return!i||i.key=="Enter"&&t&&t.from<t.to&&/^\S+$/.test(t.insert.toString())?!1:(this.pendingIOSKey=void 0,Kl(this.view.contentDOM,i.key,i.keyCode,i instanceof KeyboardEvent?i:void 0))}ignoreDuringComposition(t){return/^key/.test(t.type)?this.composing>0?!0:Z.safari&&!Z.ios&&this.compositionPendingKey&&Date.now()-this.compositionEndedAt<100?(this.compositionPendingKey=!1,!0):!1:!1}startMouseSelection(t){this.mouseSelection&&this.mouseSelection.destroy(),this.mouseSelection=t}update(t){this.view.observer.update(t),this.mouseSelection&&this.mouseSelection.update(t),this.draggedContent&&t.docChanged&&(this.draggedContent=this.draggedContent.map(t.changes)),t.transactions.length&&(this.lastKeyCode=this.lastSelectionTime=0)}destroy(){this.mouseSelection&&this.mouseSelection.destroy()}}function dO(r,t){return(i,l)=>{try{return t.call(r,l,i)}catch(a){vi(i.state,a)}}}function Nb(r){let t=Object.create(null);function i(l){return t[l]||(t[l]={observers:[],handlers:[]})}for(let l of r){let a=l.spec,o=a&&a.plugin.domEventHandlers,h=a&&a.plugin.domEventObservers;if(o)for(let c in o){let d=o[c];d&&i(c).handlers.push(dO(l.value,d))}if(h)for(let c in h){let d=h[c];d&&i(c).observers.push(dO(l.value,d))}}for(let l in ri)i(l).handlers.push(ri[l]);for(let l in ii)i(l).observers.push(ii[l]);return t}const a1=[{key:"Backspace",keyCode:8,inputType:"deleteContentBackward"},{key:"Enter",keyCode:13,inputType:"insertParagraph"},{key:"Enter",keyCode:13,inputType:"insertLineBreak"},{key:"Delete",keyCode:46,inputType:"deleteContentForward"}],Ub="dthko",r1=[16,17,18,20,91,92,224,225],Ir=6;function to(r){return Math.max(0,r)*.7+8}function Bb(r,t){return Math.max(Math.abs(r.clientX-t.clientX),Math.abs(r.clientY-t.clientY))}class Xb{constructor(t,i,l,a){this.view=t,this.startEvent=i,this.style=l,this.mustSelect=a,this.scrollSpeed={x:0,y:0},this.scrolling=-1,this.lastEvent=i,this.scrollParents=Lv(t.contentDOM),this.atoms=t.state.facet(Ac).map(h=>h(t));let o=t.contentDOM.ownerDocument;o.addEventListener("mousemove",this.move=this.move.bind(this)),o.addEventListener("mouseup",this.up=this.up.bind(this)),this.extend=i.shiftKey,this.multiple=t.state.facet(At.allowMultipleSelections)&&Pb(t,i),this.dragging=Hb(t,i)&&h1(i)==1?null:!1}start(t){this.dragging===!1&&this.select(t)}move(t){if(t.buttons==0)return this.destroy();if(this.dragging||this.dragging==null&&Bb(this.startEvent,t)<10)return;this.select(this.lastEvent=t);let i=0,l=0,a=0,o=0,h=this.view.win.innerWidth,c=this.view.win.innerHeight;this.scrollParents.x&&({left:a,right:h}=this.scrollParents.x.getBoundingClientRect()),this.scrollParents.y&&({top:o,bottom:c}=this.scrollParents.y.getBoundingClientRect());let d=i1(this.view);t.clientX-d.left<=a+Ir?i=-to(a-t.clientX):t.clientX+d.right>=h-Ir&&(i=to(t.clientX-h)),t.clientY-d.top<=o+Ir?l=-to(o-t.clientY):t.clientY+d.bottom>=c-Ir&&(l=to(t.clientY-c)),this.setScrollSpeed(i,l)}up(t){this.dragging==null&&this.select(this.lastEvent),this.dragging||t.preventDefault(),this.destroy()}destroy(){this.setScrollSpeed(0,0);let t=this.view.contentDOM.ownerDocument;t.removeEventListener("mousemove",this.move),t.removeEventListener("mouseup",this.up),this.view.inputState.mouseSelection=this.view.inputState.draggedContent=null}setScrollSpeed(t,i){this.scrollSpeed={x:t,y:i},t||i?this.scrolling<0&&(this.scrolling=setInterval(()=>this.scroll(),50)):this.scrolling>-1&&(clearInterval(this.scrolling),this.scrolling=-1)}scroll(){let{x:t,y:i}=this.scrollSpeed;t&&this.scrollParents.x&&(this.scrollParents.x.scrollLeft+=t,t=0),i&&this.scrollParents.y&&(this.scrollParents.y.scrollTop+=i,i=0),(t||i)&&this.view.win.scrollBy(t,i),this.dragging===!1&&this.select(this.lastEvent)}skipAtoms(t){let i=null;for(let l=0;l<t.ranges.length;l++){let a=t.ranges[l],o=null;if(a.empty){let h=Oo(this.atoms,a.from,0);h!=a.from&&(o=I.cursor(h,-1))}else{let h=Oo(this.atoms,a.from,-1),c=Oo(this.atoms,a.to,1);(h!=a.from||c!=a.to)&&(o=I.range(a.from==a.anchor?h:c,a.from==a.head?h:c))}o&&(i||(i=t.ranges.slice()),i[l]=o)}return i?I.create(i,t.mainIndex):t}select(t){let{view:i}=this,l=this.skipAtoms(this.style.get(t,this.extend,this.multiple));(this.mustSelect||!l.eq(i.state.selection,this.dragging===!1))&&this.view.dispatch({selection:l,userEvent:"select.pointer"}),this.mustSelect=!1}update(t){t.transactions.some(i=>i.isUserEvent("input.type"))?this.destroy():this.style.update(t)&&setTimeout(()=>this.select(this.lastEvent),20)}}function Pb(r,t){let i=r.state.facet(j0);return i.length?i[0](t):Z.mac?t.metaKey:t.ctrlKey}function Vb(r,t){let i=r.state.facet(L0);return i.length?i[0](t):Z.mac?!t.altKey:!t.ctrlKey}function Hb(r,t){let{main:i}=r.state.selection;if(i.empty)return!1;let l=Ta(r.root);if(!l||l.rangeCount==0)return!0;let a=l.getRangeAt(0).getClientRects();for(let o=0;o<a.length;o++){let h=a[o];if(h.left<=t.clientX&&h.right>=t.clientX&&h.top<=t.clientY&&h.bottom>=t.clientY)return!0}return!1}function jb(r,t){if(!t.bubbles)return!0;if(t.defaultPrevented)return!1;for(let i=t.target,l;i!=r.contentDOM;i=i.parentNode)if(!i||i.nodeType==11||(l=Ut.get(i))&&l.ignoreEvent(t))return!1;return!0}const ri=Object.create(null),ii=Object.create(null),o1=Z.ie&&Z.ie_version<15||Z.ios&&Z.webkit_version<604;function Lb(r){let t=r.dom.parentNode;if(!t)return;let i=t.appendChild(document.createElement("textarea"));i.style.cssText="position: fixed; left: -10000px; top: 10px",i.focus(),setTimeout(()=>{r.focus(),i.remove(),u1(r,i.value)},50)}function Ho(r,t,i){for(let l of r.facet(t))i=l(i,r);return i}function u1(r,t){t=Ho(r.state,wc,t);let{state:i}=r,l,a=1,o=i.toText(t),h=o.lines==i.selection.ranges.length;if(rc!=null&&i.selection.ranges.every(d=>d.empty)&&rc==o.toString()){let d=-1;l=i.changeByRange(p=>{let m=i.doc.lineAt(p.from);if(m.from==d)return{range:p};d=m.from;let O=i.toText((h?o.line(a++).text:t)+i.lineBreak);return{changes:{from:m.from,insert:O},range:I.cursor(p.from+O.length)}})}else h?l=i.changeByRange(d=>{let p=o.line(a++);return{changes:{from:d.from,to:d.to,insert:p.text},range:I.cursor(d.from+p.length)}}):l=i.replaceSelection(o);r.dispatch(l,{userEvent:"input.paste",scrollIntoView:!0})}ii.scroll=r=>{r.inputState.lastScrollTop=r.scrollDOM.scrollTop,r.inputState.lastScrollLeft=r.scrollDOM.scrollLeft};ri.keydown=(r,t)=>(r.inputState.setSelectionOrigin("select"),t.keyCode==27&&r.inputState.tabFocusMode!=0&&(r.inputState.tabFocusMode=Date.now()+2e3),!1);ii.touchstart=(r,t)=>{r.inputState.lastTouchTime=Date.now(),r.inputState.setSelectionOrigin("select.pointer")};ii.touchmove=r=>{r.inputState.setSelectionOrigin("select.pointer")};ri.mousedown=(r,t)=>{if(r.observer.flush(),r.inputState.lastTouchTime>Date.now()-2e3)return!1;let i=null;for(let l of r.state.facet(G0))if(i=l(r,t),i)break;if(!i&&t.button==0&&(i=Wb(r,t)),i){let l=!r.hasFocus;r.inputState.startMouseSelection(new Xb(r,t,i,l)),l&&r.observer.ignore(()=>{w0(r.contentDOM);let o=r.root.activeElement;o&&!o.contains(r.contentDOM)&&o.blur()});let a=r.inputState.mouseSelection;if(a)return a.start(t),a.dragging===!1}return!1};function gO(r,t,i,l){if(l==1)return I.cursor(t,i);if(l==2)return Sb(r.state,t,i);{let a=Ft.find(r.docView,t),o=r.state.doc.lineAt(a?a.posAtEnd:t),h=a?a.posAtStart:o.from,c=a?a.posAtEnd:o.to;return c<r.state.doc.length&&c==o.to&&c++,I.range(h,c)}}let pO=(r,t,i)=>t>=i.top&&t<=i.bottom&&r>=i.left&&r<=i.right;function Gb(r,t,i,l){let a=Ft.find(r.docView,t);if(!a)return 1;let o=t-a.posAtStart;if(o==0)return 1;if(o==a.length)return-1;let h=a.coordsAt(o,-1);if(h&&pO(i,l,h))return-1;let c=a.coordsAt(o,1);return c&&pO(i,l,c)?1:h&&h.bottom>=l?-1:1}function mO(r,t){let i=r.posAtCoords({x:t.clientX,y:t.clientY},!1);return{pos:i,bias:Gb(r,i,t.clientX,t.clientY)}}const $b=Z.ie&&Z.ie_version<=11;let OO=null,yO=0,SO=0;function h1(r){if(!$b)return r.detail;let t=OO,i=SO;return OO=r,SO=Date.now(),yO=!t||i>Date.now()-400&&Math.abs(t.clientX-r.clientX)<2&&Math.abs(t.clientY-r.clientY)<2?(yO+1)%3:1}function Wb(r,t){let i=mO(r,t),l=h1(t),a=r.state.selection;return{update(o){o.docChanged&&(i.pos=o.changes.mapPos(i.pos),a=a.map(o.changes))},get(o,h,c){let d=mO(r,o),p,m=gO(r,d.pos,d.bias,l);if(i.pos!=d.pos&&!h){let O=gO(r,i.pos,i.bias,l),S=Math.min(O.from,m.from),b=Math.max(O.to,m.to);m=S<m.from?I.range(S,b):I.range(b,S)}return h?a.replaceRange(a.main.extend(m.from,m.to)):c&&l==1&&a.ranges.length>1&&(p=Yb(a,d.pos))?p:c?a.addRange(m):I.create([m])}}}function Yb(r,t){for(let i=0;i<r.ranges.length;i++){let{from:l,to:a}=r.ranges[i];if(l<=t&&a>=t)return I.create(r.ranges.slice(0,i).concat(r.ranges.slice(i+1)),r.mainIndex==i?0:r.mainIndex-(r.mainIndex>i?1:0))}return null}ri.dragstart=(r,t)=>{let{selection:{main:i}}=r.state;if(t.target.draggable){let a=r.docView.nearest(t.target);if(a&&a.isWidget){let o=a.posAtStart,h=o+a.length;(o>=i.to||h<=i.from)&&(i=I.range(o,h))}}let{inputState:l}=r;return l.mouseSelection&&(l.mouseSelection.dragging=!0),l.draggedContent=i,t.dataTransfer&&(t.dataTransfer.setData("Text",Ho(r.state,Qc,r.state.sliceDoc(i.from,i.to))),t.dataTransfer.effectAllowed="copyMove"),!1};ri.dragend=r=>(r.inputState.draggedContent=null,!1);function vO(r,t,i,l){if(i=Ho(r.state,wc,i),!i)return;let a=r.posAtCoords({x:t.clientX,y:t.clientY},!1),{draggedContent:o}=r.inputState,h=l&&o&&Vb(r,t)?{from:o.from,to:o.to}:null,c={from:a,insert:i},d=r.state.changes(h?[h,c]:c);r.focus(),r.dispatch({changes:d,selection:{anchor:d.mapPos(a,-1),head:d.mapPos(a,1)},userEvent:h?"move.drop":"input.drop"}),r.inputState.draggedContent=null}ri.drop=(r,t)=>{if(!t.dataTransfer)return!1;if(r.state.readOnly)return!0;let i=t.dataTransfer.files;if(i&&i.length){let l=Array(i.length),a=0,o=()=>{++a==i.length&&vO(r,t,l.filter(h=>h!=null).join(r.state.lineBreak),!1)};for(let h=0;h<i.length;h++){let c=new FileReader;c.onerror=o,c.onload=()=>{/[\x00-\x08\x0e-\x1f]{2}/.test(c.result)||(l[h]=c.result),o()},c.readAsText(i[h])}return!0}else{let l=t.dataTransfer.getData("Text");if(l)return vO(r,t,l,!0),!0}return!1};ri.paste=(r,t)=>{if(r.state.readOnly)return!0;r.observer.flush();let i=o1?null:t.clipboardData;return i?(u1(r,i.getData("text/plain")||i.getData("text/uri-list")),!0):(Lb(r),!1)};function Zb(r,t){let i=r.dom.parentNode;if(!i)return;let l=i.appendChild(document.createElement("textarea"));l.style.cssText="position: fixed; left: -10000px; top: 10px",l.value=t,l.focus(),l.selectionEnd=t.length,l.selectionStart=0,setTimeout(()=>{l.remove(),r.focus()},50)}function Kb(r){let t=[],i=[],l=!1;for(let a of r.selection.ranges)a.empty||(t.push(r.sliceDoc(a.from,a.to)),i.push(a));if(!t.length){let a=-1;for(let{from:o}of r.selection.ranges){let h=r.doc.lineAt(o);h.number>a&&(t.push(h.text),i.push({from:h.from,to:Math.min(r.doc.length,h.to+1)})),a=h.number}l=!0}return{text:Ho(r,Qc,t.join(r.lineBreak)),ranges:i,linewise:l}}let rc=null;ri.copy=ri.cut=(r,t)=>{let{text:i,ranges:l,linewise:a}=Kb(r.state);if(!i&&!a)return!1;rc=a?i:null,t.type=="cut"&&!r.state.readOnly&&r.dispatch({changes:l,scrollIntoView:!0,userEvent:"delete.cut"});let o=o1?null:t.clipboardData;return o?(o.clearData(),o.setData("text/plain",i),!0):(Zb(r,i),!1)};const f1=rl.define();function c1(r,t){let i=[];for(let l of r.facet(Y0)){let a=l(r,t);a&&i.push(a)}return i.length?r.update({effects:i,annotations:f1.of(!0)}):null}function d1(r){setTimeout(()=>{let t=r.hasFocus;if(t!=r.inputState.notifiedFocused){let i=c1(r.state,t);i?r.dispatch(i):r.update([])}},10)}ii.focus=r=>{r.inputState.lastFocusTime=Date.now(),!r.scrollDOM.scrollTop&&(r.inputState.lastScrollTop||r.inputState.lastScrollLeft)&&(r.scrollDOM.scrollTop=r.inputState.lastScrollTop,r.scrollDOM.scrollLeft=r.inputState.lastScrollLeft),d1(r)};ii.blur=r=>{r.observer.clearSelectionRange(),d1(r)};ii.compositionstart=ii.compositionupdate=r=>{r.observer.editContext||(r.inputState.compositionFirstChange==null&&(r.inputState.compositionFirstChange=!0),r.inputState.composing<0&&(r.inputState.composing=0))};ii.compositionend=r=>{r.observer.editContext||(r.inputState.composing=-1,r.inputState.compositionEndedAt=Date.now(),r.inputState.compositionPendingKey=!0,r.inputState.compositionPendingChange=r.observer.pendingRecords().length>0,r.inputState.compositionFirstChange=null,Z.chrome&&Z.android?r.observer.flushSoon():r.inputState.compositionPendingChange?Promise.resolve().then(()=>r.observer.flush()):setTimeout(()=>{r.inputState.composing<0&&r.docView.hasComposition&&r.update([])},50))};ii.contextmenu=r=>{r.inputState.lastContextMenu=Date.now()};ri.beforeinput=(r,t)=>{var i,l;if(t.inputType=="insertReplacementText"&&r.observer.editContext){let o=(i=t.dataTransfer)===null||i===void 0?void 0:i.getData("text/plain"),h=t.getTargetRanges();if(o&&h.length){let c=h[0],d=r.posAtDOM(c.startContainer,c.startOffset),p=r.posAtDOM(c.endContainer,c.endOffset);return Rc(r,{from:d,to:p,insert:r.state.toText(o)},null),!0}}let a;if(Z.chrome&&Z.android&&(a=a1.find(o=>o.inputType==t.inputType))&&(r.observer.delayAndroidKey(a.key,a.keyCode),a.key=="Backspace"||a.key=="Delete")){let o=((l=window.visualViewport)===null||l===void 0?void 0:l.height)||0;setTimeout(()=>{var h;(((h=window.visualViewport)===null||h===void 0?void 0:h.height)||0)>o+10&&r.hasFocus&&(r.contentDOM.blur(),r.focus())},100)}return Z.ios&&t.inputType=="deleteContentForward"&&r.observer.flushSoon(),Z.safari&&t.inputType=="insertText"&&r.inputState.composing>=0&&setTimeout(()=>ii.compositionend(r,t),20),!1};const bO=new Set;function Jb(r){bO.has(r)||(bO.add(r),r.addEventListener("copy",()=>{}),r.addEventListener("cut",()=>{}))}const xO=["pre-wrap","normal","pre-line","break-spaces"];let ls=!1;function TO(){ls=!1}class Fb{constructor(t){this.lineWrapping=t,this.doc=wt.empty,this.heightSamples={},this.lineHeight=14,this.charWidth=7,this.textHeight=14,this.lineLength=30}heightForGap(t,i){let l=this.doc.lineAt(i).number-this.doc.lineAt(t).number+1;return this.lineWrapping&&(l+=Math.max(0,Math.ceil((i-t-l*this.lineLength*.5)/this.lineLength))),this.lineHeight*l}heightForLine(t){return this.lineWrapping?(1+Math.max(0,Math.ceil((t-this.lineLength)/(this.lineLength-5))))*this.lineHeight:this.lineHeight}setDoc(t){return this.doc=t,this}mustRefreshForWrapping(t){return xO.indexOf(t)>-1!=this.lineWrapping}mustRefreshForHeights(t){let i=!1;for(let l=0;l<t.length;l++){let a=t[l];a<0?l++:this.heightSamples[Math.floor(a*10)]||(i=!0,this.heightSamples[Math.floor(a*10)]=!0)}return i}refresh(t,i,l,a,o,h){let c=xO.indexOf(t)>-1,d=Math.round(i)!=Math.round(this.lineHeight)||this.lineWrapping!=c;if(this.lineWrapping=c,this.lineHeight=i,this.charWidth=l,this.textHeight=a,this.lineLength=o,d){this.heightSamples={};for(let p=0;p<h.length;p++){let m=h[p];m<0?p++:this.heightSamples[Math.floor(m*10)]=!0}}return d}}class Ib{constructor(t,i){this.from=t,this.heights=i,this.index=0}get more(){return this.index<this.heights.length}}class Si{constructor(t,i,l,a,o){this.from=t,this.length=i,this.top=l,this.height=a,this._content=o}get type(){return typeof this._content=="number"?Ti.Text:Array.isArray(this._content)?this._content:this._content.type}get to(){return this.from+this.length}get bottom(){return this.top+this.height}get widget(){return this._content instanceof zn?this._content.widget:null}get widgetLineBreaks(){return typeof this._content=="number"?this._content:0}join(t){let i=(Array.isArray(this._content)?this._content:[this]).concat(Array.isArray(t._content)?t._content:[t]);return new Si(this.from,this.length+t.length,this.top,this.height+t.height,i)}}var Xt=function(r){return r[r.ByPos=0]="ByPos",r[r.ByHeight=1]="ByHeight",r[r.ByPosNoHeight=2]="ByPosNoHeight",r}(Xt||(Xt={}));const yo=.001;class we{constructor(t,i,l=2){this.length=t,this.height=i,this.flags=l}get outdated(){return(this.flags&2)>0}set outdated(t){this.flags=(t?2:0)|this.flags&-3}setHeight(t){this.height!=t&&(Math.abs(this.height-t)>yo&&(ls=!0),this.height=t)}replace(t,i,l){return we.of(l)}decomposeLeft(t,i){i.push(this)}decomposeRight(t,i){i.push(this)}applyChanges(t,i,l,a){let o=this,h=l.doc;for(let c=a.length-1;c>=0;c--){let{fromA:d,toA:p,fromB:m,toB:O}=a[c],S=o.lineAt(d,Xt.ByPosNoHeight,l.setDoc(i),0,0),b=S.to>=p?S:o.lineAt(p,Xt.ByPosNoHeight,l,0,0);for(O+=b.to-p,p=b.to;c>0&&S.from<=a[c-1].toA;)d=a[c-1].fromA,m=a[c-1].fromB,c--,d<S.from&&(S=o.lineAt(d,Xt.ByPosNoHeight,l,0,0));m+=S.from-d,d=S.from;let T=Ec.build(l.setDoc(h),t,m,O);o=Eo(o,o.replace(d,p,T))}return o.updateHeight(l,0)}static empty(){return new je(0,0)}static of(t){if(t.length==1)return t[0];let i=0,l=t.length,a=0,o=0;for(;;)if(i==l)if(a>o*2){let c=t[i-1];c.break?t.splice(--i,1,c.left,null,c.right):t.splice(--i,1,c.left,c.right),l+=1+c.break,a-=c.size}else if(o>a*2){let c=t[l];c.break?t.splice(l,1,c.left,null,c.right):t.splice(l,1,c.left,c.right),l+=2+c.break,o-=c.size}else break;else if(a<o){let c=t[i++];c&&(a+=c.size)}else{let c=t[--l];c&&(o+=c.size)}let h=0;return t[i-1]==null?(h=1,i--):t[i]==null&&(h=1,l++),new tx(we.of(t.slice(0,i)),h,we.of(t.slice(l)))}}function Eo(r,t){return r==t?r:(r.constructor!=t.constructor&&(ls=!0),t)}we.prototype.size=1;class g1 extends we{constructor(t,i,l){super(t,i),this.deco=l}blockAt(t,i,l,a){return new Si(a,this.length,l,this.height,this.deco||0)}lineAt(t,i,l,a,o){return this.blockAt(0,l,a,o)}forEachLine(t,i,l,a,o,h){t<=o+this.length&&i>=o&&h(this.blockAt(0,l,a,o))}updateHeight(t,i=0,l=!1,a){return a&&a.from<=i&&a.more&&this.setHeight(a.heights[a.index++]),this.outdated=!1,this}toString(){return`block(${this.length})`}}class je extends g1{constructor(t,i){super(t,i,null),this.collapsed=0,this.widgetHeight=0,this.breaks=0}blockAt(t,i,l,a){return new Si(a,this.length,l,this.height,this.breaks)}replace(t,i,l){let a=l[0];return l.length==1&&(a instanceof je||a instanceof ce&&a.flags&4)&&Math.abs(this.length-a.length)<10?(a instanceof ce?a=new je(a.length,this.height):a.height=this.height,this.outdated||(a.outdated=!1),a):we.of(l)}updateHeight(t,i=0,l=!1,a){return a&&a.from<=i&&a.more?this.setHeight(a.heights[a.index++]):(l||this.outdated)&&this.setHeight(Math.max(this.widgetHeight,t.heightForLine(this.length-this.collapsed))+this.breaks*t.lineHeight),this.outdated=!1,this}toString(){return`line(${this.length}${this.collapsed?-this.collapsed:""}${this.widgetHeight?":"+this.widgetHeight:""})`}}class ce extends we{constructor(t){super(t,0)}heightMetrics(t,i){let l=t.doc.lineAt(i).number,a=t.doc.lineAt(i+this.length).number,o=a-l+1,h,c=0;if(t.lineWrapping){let d=Math.min(this.height,t.lineHeight*o);h=d/o,this.length>o+1&&(c=(this.height-d)/(this.length-o-1))}else h=this.height/o;return{firstLine:l,lastLine:a,perLine:h,perChar:c}}blockAt(t,i,l,a){let{firstLine:o,lastLine:h,perLine:c,perChar:d}=this.heightMetrics(i,a);if(i.lineWrapping){let p=a+(t<i.lineHeight?0:Math.round(Math.max(0,Math.min(1,(t-l)/this.height))*this.length)),m=i.doc.lineAt(p),O=c+m.length*d,S=Math.max(l,t-O/2);return new Si(m.from,m.length,S,O,0)}else{let p=Math.max(0,Math.min(h-o,Math.floor((t-l)/c))),{from:m,length:O}=i.doc.line(o+p);return new Si(m,O,l+c*p,c,0)}}lineAt(t,i,l,a,o){if(i==Xt.ByHeight)return this.blockAt(t,l,a,o);if(i==Xt.ByPosNoHeight){let{from:b,to:T}=l.doc.lineAt(t);return new Si(b,T-b,0,0,0)}let{firstLine:h,perLine:c,perChar:d}=this.heightMetrics(l,o),p=l.doc.lineAt(t),m=c+p.length*d,O=p.number-h,S=a+c*O+d*(p.from-o-O);return new Si(p.from,p.length,Math.max(a,Math.min(S,a+this.height-m)),m,0)}forEachLine(t,i,l,a,o,h){t=Math.max(t,o),i=Math.min(i,o+this.length);let{firstLine:c,perLine:d,perChar:p}=this.heightMetrics(l,o);for(let m=t,O=a;m<=i;){let S=l.doc.lineAt(m);if(m==t){let T=S.number-c;O+=d*T+p*(t-o-T)}let b=d+p*S.length;h(new Si(S.from,S.length,O,b,0)),O+=b,m=S.to+1}}replace(t,i,l){let a=this.length-i;if(a>0){let o=l[l.length-1];o instanceof ce?l[l.length-1]=new ce(o.length+a):l.push(null,new ce(a-1))}if(t>0){let o=l[0];o instanceof ce?l[0]=new ce(t+o.length):l.unshift(new ce(t-1),null)}return we.of(l)}decomposeLeft(t,i){i.push(new ce(t-1),null)}decomposeRight(t,i){i.push(null,new ce(this.length-t-1))}updateHeight(t,i=0,l=!1,a){let o=i+this.length;if(a&&a.from<=i+this.length&&a.more){let h=[],c=Math.max(i,a.from),d=-1;for(a.from>i&&h.push(new ce(a.from-i-1).updateHeight(t,i));c<=o&&a.more;){let m=t.doc.lineAt(c).length;h.length&&h.push(null);let O=a.heights[a.index++];d==-1?d=O:Math.abs(O-d)>=yo&&(d=-2);let S=new je(m,O);S.outdated=!1,h.push(S),c+=m+1}c<=o&&h.push(null,new ce(o-c).updateHeight(t,c));let p=we.of(h);return(d<0||Math.abs(p.height-this.height)>=yo||Math.abs(d-this.heightMetrics(t,i).perLine)>=yo)&&(ls=!0),Eo(this,p)}else(l||this.outdated)&&(this.setHeight(t.heightForGap(i,i+this.length)),this.outdated=!1);return this}toString(){return`gap(${this.length})`}}class tx extends we{constructor(t,i,l){super(t.length+i+l.length,t.height+l.height,i|(t.outdated||l.outdated?2:0)),this.left=t,this.right=l,this.size=t.size+l.size}get break(){return this.flags&1}blockAt(t,i,l,a){let o=l+this.left.height;return t<o?this.left.blockAt(t,i,l,a):this.right.blockAt(t,i,o,a+this.left.length+this.break)}lineAt(t,i,l,a,o){let h=a+this.left.height,c=o+this.left.length+this.break,d=i==Xt.ByHeight?t<h:t<c,p=d?this.left.lineAt(t,i,l,a,o):this.right.lineAt(t,i,l,h,c);if(this.break||(d?p.to<c:p.from>c))return p;let m=i==Xt.ByPosNoHeight?Xt.ByPosNoHeight:Xt.ByPos;return d?p.join(this.right.lineAt(c,m,l,h,c)):this.left.lineAt(c,m,l,a,o).join(p)}forEachLine(t,i,l,a,o,h){let c=a+this.left.height,d=o+this.left.length+this.break;if(this.break)t<d&&this.left.forEachLine(t,i,l,a,o,h),i>=d&&this.right.forEachLine(t,i,l,c,d,h);else{let p=this.lineAt(d,Xt.ByPos,l,a,o);t<p.from&&this.left.forEachLine(t,p.from-1,l,a,o,h),p.to>=t&&p.from<=i&&h(p),i>p.to&&this.right.forEachLine(p.to+1,i,l,c,d,h)}}replace(t,i,l){let a=this.left.length+this.break;if(i<a)return this.balanced(this.left.replace(t,i,l),this.right);if(t>this.left.length)return this.balanced(this.left,this.right.replace(t-a,i-a,l));let o=[];t>0&&this.decomposeLeft(t,o);let h=o.length;for(let c of l)o.push(c);if(t>0&&wO(o,h-1),i<this.length){let c=o.length;this.decomposeRight(i,o),wO(o,c)}return we.of(o)}decomposeLeft(t,i){let l=this.left.length;if(t<=l)return this.left.decomposeLeft(t,i);i.push(this.left),this.break&&(l++,t>=l&&i.push(null)),t>l&&this.right.decomposeLeft(t-l,i)}decomposeRight(t,i){let l=this.left.length,a=l+this.break;if(t>=a)return this.right.decomposeRight(t-a,i);t<l&&this.left.decomposeRight(t,i),this.break&&t<a&&i.push(null),i.push(this.right)}balanced(t,i){return t.size>2*i.size||i.size>2*t.size?we.of(this.break?[t,null,i]:[t,i]):(this.left=Eo(this.left,t),this.right=Eo(this.right,i),this.setHeight(t.height+i.height),this.outdated=t.outdated||i.outdated,this.size=t.size+i.size,this.length=t.length+this.break+i.length,this)}updateHeight(t,i=0,l=!1,a){let{left:o,right:h}=this,c=i+o.length+this.break,d=null;return a&&a.from<=i+o.length&&a.more?d=o=o.updateHeight(t,i,l,a):o.updateHeight(t,i,l),a&&a.from<=c+h.length&&a.more?d=h=h.updateHeight(t,c,l,a):h.updateHeight(t,c,l),d?this.balanced(o,h):(this.height=this.left.height+this.right.height,this.outdated=!1,this)}toString(){return this.left+(this.break?" ":"-")+this.right}}function wO(r,t){let i,l;r[t]==null&&(i=r[t-1])instanceof ce&&(l=r[t+1])instanceof ce&&r.splice(t-1,3,new ce(i.length+1+l.length))}const ex=5;class Ec{constructor(t,i){this.pos=t,this.oracle=i,this.nodes=[],this.lineStart=-1,this.lineEnd=-1,this.covering=null,this.writtenTo=t}get isCovered(){return this.covering&&this.nodes[this.nodes.length-1]==this.covering}span(t,i){if(this.lineStart>-1){let l=Math.min(i,this.lineEnd),a=this.nodes[this.nodes.length-1];a instanceof je?a.length+=l-this.pos:(l>this.pos||!this.isCovered)&&this.nodes.push(new je(l-this.pos,-1)),this.writtenTo=l,i>l&&(this.nodes.push(null),this.writtenTo++,this.lineStart=-1)}this.pos=i}point(t,i,l){if(t<i||l.heightRelevant){let a=l.widget?l.widget.estimatedHeight:0,o=l.widget?l.widget.lineBreaks:0;a<0&&(a=this.oracle.lineHeight);let h=i-t;l.block?this.addBlock(new g1(h,a,l)):(h||o||a>=ex)&&this.addLineDeco(a,o,h)}else i>t&&this.span(t,i);this.lineEnd>-1&&this.lineEnd<this.pos&&(this.lineEnd=this.oracle.doc.lineAt(this.pos).to)}enterLine(){if(this.lineStart>-1)return;let{from:t,to:i}=this.oracle.doc.lineAt(this.pos);this.lineStart=t,this.lineEnd=i,this.writtenTo<t&&((this.writtenTo<t-1||this.nodes[this.nodes.length-1]==null)&&this.nodes.push(this.blankContent(this.writtenTo,t-1)),this.nodes.push(null)),this.pos>t&&this.nodes.push(new je(this.pos-t,-1)),this.writtenTo=this.pos}blankContent(t,i){let l=new ce(i-t);return this.oracle.doc.lineAt(t).to==i&&(l.flags|=4),l}ensureLine(){this.enterLine();let t=this.nodes.length?this.nodes[this.nodes.length-1]:null;if(t instanceof je)return t;let i=new je(0,-1);return this.nodes.push(i),i}addBlock(t){this.enterLine();let i=t.deco;i&&i.startSide>0&&!this.isCovered&&this.ensureLine(),this.nodes.push(t),this.writtenTo=this.pos=this.pos+t.length,i&&i.endSide>0&&(this.covering=t)}addLineDeco(t,i,l){let a=this.ensureLine();a.length+=l,a.collapsed+=l,a.widgetHeight=Math.max(a.widgetHeight,t),a.breaks+=i,this.writtenTo=this.pos=this.pos+l}finish(t){let i=this.nodes.length==0?null:this.nodes[this.nodes.length-1];this.lineStart>-1&&!(i instanceof je)&&!this.isCovered?this.nodes.push(new je(0,-1)):(this.writtenTo<this.pos||i==null)&&this.nodes.push(this.blankContent(this.writtenTo,this.pos));let l=t;for(let a of this.nodes)a instanceof je&&a.updateHeight(this.oracle,l),l+=a?a.length:1;return this.nodes}static build(t,i,l,a){let o=new Ec(l,t);return Pt.spans(i,l,a,o,0),o.finish(l)}}function ix(r,t,i){let l=new nx;return Pt.compare(r,t,i,l,0),l.changes}class nx{constructor(){this.changes=[]}compareRange(){}comparePoint(t,i,l,a){(t<i||l&&l.heightRelevant||a&&a.heightRelevant)&&mo(t,i,this.changes,5)}}function lx(r,t){let i=r.getBoundingClientRect(),l=r.ownerDocument,a=l.defaultView||window,o=Math.max(0,i.left),h=Math.min(a.innerWidth,i.right),c=Math.max(0,i.top),d=Math.min(a.innerHeight,i.bottom);for(let p=r.parentNode;p&&p!=l.body;)if(p.nodeType==1){let m=p,O=window.getComputedStyle(m);if((m.scrollHeight>m.clientHeight||m.scrollWidth>m.clientWidth)&&O.overflow!="visible"){let S=m.getBoundingClientRect();o=Math.max(o,S.left),h=Math.min(h,S.right),c=Math.max(c,S.top),d=Math.min(p==r.parentNode?a.innerHeight:d,S.bottom)}p=O.position=="absolute"||O.position=="fixed"?m.offsetParent:m.parentNode}else if(p.nodeType==11)p=p.host;else break;return{left:o-i.left,right:Math.max(o,h)-i.left,top:c-(i.top+t),bottom:Math.max(c,d)-(i.top+t)}}function sx(r){let t=r.getBoundingClientRect(),i=r.ownerDocument.defaultView||window;return t.left<i.innerWidth&&t.right>0&&t.top<i.innerHeight&&t.bottom>0}function ax(r,t){let i=r.getBoundingClientRect();return{left:0,right:i.right-i.left,top:t,bottom:i.bottom-(i.top+t)}}class vf{constructor(t,i,l,a){this.from=t,this.to=i,this.size=l,this.displaySize=a}static same(t,i){if(t.length!=i.length)return!1;for(let l=0;l<t.length;l++){let a=t[l],o=i[l];if(a.from!=o.from||a.to!=o.to||a.size!=o.size)return!1}return!0}draw(t,i){return ne.replace({widget:new rx(this.displaySize*(i?t.scaleY:t.scaleX),i)}).range(this.from,this.to)}}class rx extends Vo{constructor(t,i){super(),this.size=t,this.vertical=i}eq(t){return t.size==this.size&&t.vertical==this.vertical}toDOM(){let t=document.createElement("div");return this.vertical?t.style.height=this.size+"px":(t.style.width=this.size+"px",t.style.height="2px",t.style.display="inline-block"),t}get estimatedHeight(){return this.vertical?this.size:-1}}class QO{constructor(t){this.state=t,this.pixelViewport={left:0,right:window.innerWidth,top:0,bottom:0},this.inView=!0,this.paddingTop=0,this.paddingBottom=0,this.contentDOMWidth=0,this.contentDOMHeight=0,this.editorHeight=0,this.editorWidth=0,this.scrollTop=0,this.scrolledToBottom=!1,this.scaleX=1,this.scaleY=1,this.scrollAnchorPos=0,this.scrollAnchorHeight=-1,this.scaler=kO,this.scrollTarget=null,this.printing=!1,this.mustMeasureContent=!0,this.defaultTextDirection=pe.LTR,this.visibleRanges=[],this.mustEnforceCursorAssoc=!1;let i=t.facet(kc).some(l=>typeof l!="function"&&l.class=="cm-lineWrapping");this.heightOracle=new Fb(i),this.stateDeco=t.facet(Qa).filter(l=>typeof l!="function"),this.heightMap=we.empty().applyChanges(this.stateDeco,wt.empty,this.heightOracle.setDoc(t.doc),[new ei(0,0,0,t.doc.length)]);for(let l=0;l<2&&(this.viewport=this.getViewport(0,null),!!this.updateForViewport());l++);this.updateViewportLines(),this.lineGaps=this.ensureLineGaps([]),this.lineGapDeco=ne.set(this.lineGaps.map(l=>l.draw(this,!1))),this.computeVisibleRanges()}updateForViewport(){let t=[this.viewport],{main:i}=this.state.selection;for(let l=0;l<=1;l++){let a=l?i.head:i.anchor;if(!t.some(({from:o,to:h})=>a>=o&&a<=h)){let{from:o,to:h}=this.lineBlockAt(a);t.push(new eo(o,h))}}return this.viewports=t.sort((l,a)=>l.from-a.from),this.updateScaler()}updateScaler(){let t=this.scaler;return this.scaler=this.heightMap.height<=7e6?kO:new zc(this.heightOracle,this.heightMap,this.viewports),t.eq(this.scaler)?0:2}updateViewportLines(){this.viewportLines=[],this.heightMap.forEachLine(this.viewport.from,this.viewport.to,this.heightOracle.setDoc(this.state.doc),0,0,t=>{this.viewportLines.push(ga(t,this.scaler))})}update(t,i=null){this.state=t.state;let l=this.stateDeco;this.stateDeco=this.state.facet(Qa).filter(m=>typeof m!="function");let a=t.changedRanges,o=ei.extendWithRanges(a,ix(l,this.stateDeco,t?t.changes:oe.empty(this.state.doc.length))),h=this.heightMap.height,c=this.scrolledToBottom?null:this.scrollAnchorAt(this.scrollTop);TO(),this.heightMap=this.heightMap.applyChanges(this.stateDeco,t.startState.doc,this.heightOracle.setDoc(this.state.doc),o),(this.heightMap.height!=h||ls)&&(t.flags|=2),c?(this.scrollAnchorPos=t.changes.mapPos(c.from,-1),this.scrollAnchorHeight=c.top):(this.scrollAnchorPos=-1,this.scrollAnchorHeight=h);let d=o.length?this.mapViewport(this.viewport,t.changes):this.viewport;(i&&(i.range.head<d.from||i.range.head>d.to)||!this.viewportIsAppropriate(d))&&(d=this.getViewport(0,i));let p=d.from!=this.viewport.from||d.to!=this.viewport.to;this.viewport=d,t.flags|=this.updateForViewport(),(p||!t.changes.empty||t.flags&2)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(this.mapLineGaps(this.lineGaps,t.changes))),t.flags|=this.computeVisibleRanges(t.changes),i&&(this.scrollTarget=i),!this.mustEnforceCursorAssoc&&t.selectionSet&&t.view.lineWrapping&&t.state.selection.main.empty&&t.state.selection.main.assoc&&!t.state.facet(hb)&&(this.mustEnforceCursorAssoc=!0)}measure(t){let i=t.contentDOM,l=window.getComputedStyle(i),a=this.heightOracle,o=l.whiteSpace;this.defaultTextDirection=l.direction=="rtl"?pe.RTL:pe.LTR;let h=this.heightOracle.mustRefreshForWrapping(o),c=i.getBoundingClientRect(),d=h||this.mustMeasureContent||this.contentDOMHeight!=c.height;this.contentDOMHeight=c.height,this.mustMeasureContent=!1;let p=0,m=0;if(c.width&&c.height){let{scaleX:W,scaleY:X}=T0(i,c);(W>.005&&Math.abs(this.scaleX-W)>.005||X>.005&&Math.abs(this.scaleY-X)>.005)&&(this.scaleX=W,this.scaleY=X,p|=16,h=d=!0)}let O=(parseInt(l.paddingTop)||0)*this.scaleY,S=(parseInt(l.paddingBottom)||0)*this.scaleY;(this.paddingTop!=O||this.paddingBottom!=S)&&(this.paddingTop=O,this.paddingBottom=S,p|=18),this.editorWidth!=t.scrollDOM.clientWidth&&(a.lineWrapping&&(d=!0),this.editorWidth=t.scrollDOM.clientWidth,p|=16);let b=t.scrollDOM.scrollTop*this.scaleY;this.scrollTop!=b&&(this.scrollAnchorHeight=-1,this.scrollTop=b),this.scrolledToBottom=k0(t.scrollDOM);let T=(this.printing?ax:lx)(i,this.paddingTop),z=T.top-this.pixelViewport.top,C=T.bottom-this.pixelViewport.bottom;this.pixelViewport=T;let q=this.pixelViewport.bottom>this.pixelViewport.top&&this.pixelViewport.right>this.pixelViewport.left;if(q!=this.inView&&(this.inView=q,q&&(d=!0)),!this.inView&&!this.scrollTarget&&!sx(t.dom))return 0;let $=c.width;if((this.contentDOMWidth!=$||this.editorHeight!=t.scrollDOM.clientHeight)&&(this.contentDOMWidth=c.width,this.editorHeight=t.scrollDOM.clientHeight,p|=16),d){let W=t.docView.measureVisibleLineHeights(this.viewport);if(a.mustRefreshForHeights(W)&&(h=!0),h||a.lineWrapping&&Math.abs($-this.contentDOMWidth)>a.charWidth){let{lineHeight:X,charWidth:J,textHeight:Y}=t.docView.measureTextSize();h=X>0&&a.refresh(o,X,J,Y,$/J,W),h&&(t.docView.minWidth=0,p|=16)}z>0&&C>0?m=Math.max(z,C):z<0&&C<0&&(m=Math.min(z,C)),TO();for(let X of this.viewports){let J=X.from==this.viewport.from?W:t.docView.measureVisibleLineHeights(X);this.heightMap=(h?we.empty().applyChanges(this.stateDeco,wt.empty,this.heightOracle,[new ei(0,0,0,t.state.doc.length)]):this.heightMap).updateHeight(a,0,h,new Ib(X.from,J))}ls&&(p|=2)}let j=!this.viewportIsAppropriate(this.viewport,m)||this.scrollTarget&&(this.scrollTarget.range.head<this.viewport.from||this.scrollTarget.range.head>this.viewport.to);return j&&(p&2&&(p|=this.updateScaler()),this.viewport=this.getViewport(m,this.scrollTarget),p|=this.updateForViewport()),(p&2||j)&&this.updateViewportLines(),(this.lineGaps.length||this.viewport.to-this.viewport.from>4e3)&&this.updateLineGaps(this.ensureLineGaps(h?[]:this.lineGaps,t)),p|=this.computeVisibleRanges(),this.mustEnforceCursorAssoc&&(this.mustEnforceCursorAssoc=!1,t.docView.enforceCursorAssoc()),p}get visibleTop(){return this.scaler.fromDOM(this.pixelViewport.top)}get visibleBottom(){return this.scaler.fromDOM(this.pixelViewport.bottom)}getViewport(t,i){let l=.5-Math.max(-.5,Math.min(.5,t/1e3/2)),a=this.heightMap,o=this.heightOracle,{visibleTop:h,visibleBottom:c}=this,d=new eo(a.lineAt(h-l*1e3,Xt.ByHeight,o,0,0).from,a.lineAt(c+(1-l)*1e3,Xt.ByHeight,o,0,0).to);if(i){let{head:p}=i.range;if(p<d.from||p>d.to){let m=Math.min(this.editorHeight,this.pixelViewport.bottom-this.pixelViewport.top),O=a.lineAt(p,Xt.ByPos,o,0,0),S;i.y=="center"?S=(O.top+O.bottom)/2-m/2:i.y=="start"||i.y=="nearest"&&p<d.from?S=O.top:S=O.bottom-m,d=new eo(a.lineAt(S-1e3/2,Xt.ByHeight,o,0,0).from,a.lineAt(S+m+1e3/2,Xt.ByHeight,o,0,0).to)}}return d}mapViewport(t,i){let l=i.mapPos(t.from,-1),a=i.mapPos(t.to,1);return new eo(this.heightMap.lineAt(l,Xt.ByPos,this.heightOracle,0,0).from,this.heightMap.lineAt(a,Xt.ByPos,this.heightOracle,0,0).to)}viewportIsAppropriate({from:t,to:i},l=0){if(!this.inView)return!0;let{top:a}=this.heightMap.lineAt(t,Xt.ByPos,this.heightOracle,0,0),{bottom:o}=this.heightMap.lineAt(i,Xt.ByPos,this.heightOracle,0,0),{visibleTop:h,visibleBottom:c}=this;return(t==0||a<=h-Math.max(10,Math.min(-l,250)))&&(i==this.state.doc.length||o>=c+Math.max(10,Math.min(l,250)))&&a>h-2*1e3&&o<c+2*1e3}mapLineGaps(t,i){if(!t.length||i.empty)return t;let l=[];for(let a of t)i.touchesRange(a.from,a.to)||l.push(new vf(i.mapPos(a.from),i.mapPos(a.to),a.size,a.displaySize));return l}ensureLineGaps(t,i){let l=this.heightOracle.lineWrapping,a=l?1e4:2e3,o=a>>1,h=a<<1;if(this.defaultTextDirection!=pe.LTR&&!l)return[];let c=[],d=(m,O,S,b)=>{if(O-m<o)return;let T=this.state.selection.main,z=[T.from];T.empty||z.push(T.to);for(let q of z)if(q>m&&q<O){d(m,q-10,S,b),d(q+10,O,S,b);return}let C=ux(t,q=>q.from>=S.from&&q.to<=S.to&&Math.abs(q.from-m)<o&&Math.abs(q.to-O)<o&&!z.some($=>q.from<$&&q.to>$));if(!C){if(O<S.to&&i&&l&&i.visibleRanges.some(j=>j.from<=O&&j.to>=O)){let j=i.moveToLineBoundary(I.cursor(O),!1,!0).head;j>m&&(O=j)}let q=this.gapSize(S,m,O,b),$=l||q<2e6?q:2e6;C=new vf(m,O,q,$)}c.push(C)},p=m=>{if(m.length<h||m.type!=Ti.Text)return;let O=ox(m.from,m.to,this.stateDeco);if(O.total<h)return;let S=this.scrollTarget?this.scrollTarget.range.head:null,b,T;if(l){let z=a/this.heightOracle.lineLength*this.heightOracle.lineHeight,C,q;if(S!=null){let $=no(O,S),j=((this.visibleBottom-this.visibleTop)/2+z)/m.height;C=$-j,q=$+j}else C=(this.visibleTop-m.top-z)/m.height,q=(this.visibleBottom-m.top+z)/m.height;b=io(O,C),T=io(O,q)}else{let z=O.total*this.heightOracle.charWidth,C=a*this.heightOracle.charWidth,q=0;if(z>2e6)for(let J of t)J.from>=m.from&&J.from<m.to&&J.size!=J.displaySize&&J.from*this.heightOracle.charWidth+q<this.pixelViewport.left&&(q=J.size-J.displaySize);let $=this.pixelViewport.left+q,j=this.pixelViewport.right+q,W,X;if(S!=null){let J=no(O,S),Y=((j-$)/2+C)/z;W=J-Y,X=J+Y}else W=($-C)/z,X=(j+C)/z;b=io(O,W),T=io(O,X)}b>m.from&&d(m.from,b,m,O),T<m.to&&d(T,m.to,m,O)};for(let m of this.viewportLines)Array.isArray(m.type)?m.type.forEach(p):p(m);return c}gapSize(t,i,l,a){let o=no(a,l)-no(a,i);return this.heightOracle.lineWrapping?t.height*o:a.total*this.heightOracle.charWidth*o}updateLineGaps(t){vf.same(t,this.lineGaps)||(this.lineGaps=t,this.lineGapDeco=ne.set(t.map(i=>i.draw(this,this.heightOracle.lineWrapping))))}computeVisibleRanges(t){let i=this.stateDeco;this.lineGaps.length&&(i=i.concat(this.lineGapDeco));let l=[];Pt.spans(i,this.viewport.from,this.viewport.to,{span(o,h){l.push({from:o,to:h})},point(){}},20);let a=0;if(l.length!=this.visibleRanges.length)a=12;else for(let o=0;o<l.length&&!(a&8);o++){let h=this.visibleRanges[o],c=l[o];(h.from!=c.from||h.to!=c.to)&&(a|=4,t&&t.mapPos(h.from,-1)==c.from&&t.mapPos(h.to,1)==c.to||(a|=8))}return this.visibleRanges=l,a}lineBlockAt(t){return t>=this.viewport.from&&t<=this.viewport.to&&this.viewportLines.find(i=>i.from<=t&&i.to>=t)||ga(this.heightMap.lineAt(t,Xt.ByPos,this.heightOracle,0,0),this.scaler)}lineBlockAtHeight(t){return t>=this.viewportLines[0].top&&t<=this.viewportLines[this.viewportLines.length-1].bottom&&this.viewportLines.find(i=>i.top<=t&&i.bottom>=t)||ga(this.heightMap.lineAt(this.scaler.fromDOM(t),Xt.ByHeight,this.heightOracle,0,0),this.scaler)}scrollAnchorAt(t){let i=this.lineBlockAtHeight(t+8);return i.from>=this.viewport.from||this.viewportLines[0].top-t>200?i:this.viewportLines[0]}elementAtHeight(t){return ga(this.heightMap.blockAt(this.scaler.fromDOM(t),this.heightOracle,0,0),this.scaler)}get docHeight(){return this.scaler.toDOM(this.heightMap.height)}get contentHeight(){return this.docHeight+this.paddingTop+this.paddingBottom}}class eo{constructor(t,i){this.from=t,this.to=i}}function ox(r,t,i){let l=[],a=r,o=0;return Pt.spans(i,r,t,{span(){},point(h,c){h>a&&(l.push({from:a,to:h}),o+=h-a),a=c}},20),a<t&&(l.push({from:a,to:t}),o+=t-a),{total:o,ranges:l}}function io({total:r,ranges:t},i){if(i<=0)return t[0].from;if(i>=1)return t[t.length-1].to;let l=Math.floor(r*i);for(let a=0;;a++){let{from:o,to:h}=t[a],c=h-o;if(l<=c)return o+l;l-=c}}function no(r,t){let i=0;for(let{from:l,to:a}of r.ranges){if(t<=a){i+=t-l;break}i+=a-l}return i/r.total}function ux(r,t){for(let i of r)if(t(i))return i}const kO={toDOM(r){return r},fromDOM(r){return r},scale:1,eq(r){return r==this}};class zc{constructor(t,i,l){let a=0,o=0,h=0;this.viewports=l.map(({from:c,to:d})=>{let p=i.lineAt(c,Xt.ByPos,t,0,0).top,m=i.lineAt(d,Xt.ByPos,t,0,0).bottom;return a+=m-p,{from:c,to:d,top:p,bottom:m,domTop:0,domBottom:0}}),this.scale=(7e6-a)/(i.height-a);for(let c of this.viewports)c.domTop=h+(c.top-o)*this.scale,h=c.domBottom=c.domTop+(c.bottom-c.top),o=c.bottom}toDOM(t){for(let i=0,l=0,a=0;;i++){let o=i<this.viewports.length?this.viewports[i]:null;if(!o||t<o.top)return a+(t-l)*this.scale;if(t<=o.bottom)return o.domTop+(t-o.top);l=o.bottom,a=o.domBottom}}fromDOM(t){for(let i=0,l=0,a=0;;i++){let o=i<this.viewports.length?this.viewports[i]:null;if(!o||t<o.domTop)return l+(t-a)/this.scale;if(t<=o.domBottom)return o.top+(t-o.domTop);l=o.bottom,a=o.domBottom}}eq(t){return t instanceof zc?this.scale==t.scale&&this.viewports.length==t.viewports.length&&this.viewports.every((i,l)=>i.from==t.viewports[l].from&&i.to==t.viewports[l].to):!1}}function ga(r,t){if(t.scale==1)return r;let i=t.toDOM(r.top),l=t.toDOM(r.bottom);return new Si(r.from,r.length,i,l-i,Array.isArray(r._content)?r._content.map(a=>ga(a,t)):r._content)}const lo=ut.define({combine:r=>r.join(" ")}),oc=ut.define({combine:r=>r.indexOf(!0)>-1}),uc=ts.newName(),p1=ts.newName(),m1=ts.newName(),O1={"&light":"."+p1,"&dark":"."+m1};function hc(r,t,i){return new ts(t,{finish(l){return/&/.test(l)?l.replace(/&\w*/,a=>{if(a=="&")return r;if(!i||!i[a])throw new RangeError(`Unsupported selector: ${a}`);return i[a]}):r+" "+l}})}const hx=hc("."+uc,{"&":{position:"relative !important",boxSizing:"border-box","&.cm-focused":{outline:"1px dotted #212121"},display:"flex !important",flexDirection:"column"},".cm-scroller":{display:"flex !important",alignItems:"flex-start !important",fontFamily:"monospace",lineHeight:1.4,height:"100%",overflowX:"auto",position:"relative",zIndex:0,overflowAnchor:"none"},".cm-content":{margin:0,flexGrow:2,flexShrink:0,display:"block",whiteSpace:"pre",wordWrap:"normal",boxSizing:"border-box",minHeight:"100%",padding:"4px 0",outline:"none","&[contenteditable=true]":{WebkitUserModify:"read-write-plaintext-only"}},".cm-lineWrapping":{whiteSpace_fallback:"pre-wrap",whiteSpace:"break-spaces",wordBreak:"break-word",overflowWrap:"anywhere",flexShrink:1},"&light .cm-content":{caretColor:"black"},"&dark .cm-content":{caretColor:"white"},".cm-line":{display:"block",padding:"0 2px 0 6px"},".cm-layer":{position:"absolute",left:0,top:0,contain:"size style","& > *":{position:"absolute"}},"&light .cm-selectionBackground":{background:"#d9d9d9"},"&dark .cm-selectionBackground":{background:"#222"},"&light.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#d7d4f0"},"&dark.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground":{background:"#233"},".cm-cursorLayer":{pointerEvents:"none"},"&.cm-focused > .cm-scroller > .cm-cursorLayer":{animation:"steps(1) cm-blink 1.2s infinite"},"@keyframes cm-blink":{"0%":{},"50%":{opacity:0},"100%":{}},"@keyframes cm-blink2":{"0%":{},"50%":{opacity:0},"100%":{}},".cm-cursor, .cm-dropCursor":{borderLeft:"1.2px solid black",marginLeft:"-0.6px",pointerEvents:"none"},".cm-cursor":{display:"none"},"&dark .cm-cursor":{borderLeftColor:"#ddd"},".cm-dropCursor":{position:"absolute"},"&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor":{display:"block"},".cm-iso":{unicodeBidi:"isolate"},".cm-announced":{position:"fixed",top:"-10000px"},"@media print":{".cm-announced":{display:"none"}},"&light .cm-activeLine":{backgroundColor:"#cceeff44"},"&dark .cm-activeLine":{backgroundColor:"#99eeff33"},"&light .cm-specialChar":{color:"red"},"&dark .cm-specialChar":{color:"#f78"},".cm-gutters":{flexShrink:0,display:"flex",height:"100%",boxSizing:"border-box",insetInlineStart:0,zIndex:200},"&light .cm-gutters":{backgroundColor:"#f5f5f5",color:"#6c6c6c",borderRight:"1px solid #ddd"},"&dark .cm-gutters":{backgroundColor:"#333338",color:"#ccc"},".cm-gutter":{display:"flex !important",flexDirection:"column",flexShrink:0,boxSizing:"border-box",minHeight:"100%",overflow:"hidden"},".cm-gutterElement":{boxSizing:"border-box"},".cm-lineNumbers .cm-gutterElement":{padding:"0 3px 0 5px",minWidth:"20px",textAlign:"right",whiteSpace:"nowrap"},"&light .cm-activeLineGutter":{backgroundColor:"#e2f2ff"},"&dark .cm-activeLineGutter":{backgroundColor:"#222227"},".cm-panels":{boxSizing:"border-box",position:"sticky",left:0,right:0,zIndex:300},"&light .cm-panels":{backgroundColor:"#f5f5f5",color:"black"},"&light .cm-panels-top":{borderBottom:"1px solid #ddd"},"&light .cm-panels-bottom":{borderTop:"1px solid #ddd"},"&dark .cm-panels":{backgroundColor:"#333338",color:"white"},".cm-dialog":{padding:"2px 19px 4px 6px",position:"relative","& label":{fontSize:"80%"}},".cm-dialog-close":{position:"absolute",top:"3px",right:"4px",backgroundColor:"inherit",border:"none",font:"inherit",fontSize:"14px",padding:"0"},".cm-tab":{display:"inline-block",overflow:"hidden",verticalAlign:"bottom"},".cm-widgetBuffer":{verticalAlign:"text-top",height:"1em",width:0,display:"inline"},".cm-placeholder":{color:"#888",display:"inline-block",verticalAlign:"top",userSelect:"none"},".cm-highlightSpace":{backgroundImage:"radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%)",backgroundPosition:"center"},".cm-highlightTab":{backgroundImage:`url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>')`,backgroundSize:"auto 100%",backgroundPosition:"right 90%",backgroundRepeat:"no-repeat"},".cm-trailingSpace":{backgroundColor:"#ff332255"},".cm-button":{verticalAlign:"middle",color:"inherit",fontSize:"70%",padding:".2em 1em",borderRadius:"1px"},"&light .cm-button":{backgroundImage:"linear-gradient(#eff1f5, #d9d9df)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#b4b4b4, #d0d3d6)"}},"&dark .cm-button":{backgroundImage:"linear-gradient(#393939, #111)",border:"1px solid #888","&:active":{backgroundImage:"linear-gradient(#111, #333)"}},".cm-textfield":{verticalAlign:"middle",color:"inherit",fontSize:"70%",border:"1px solid silver",padding:".2em .5em"},"&light .cm-textfield":{backgroundColor:"white"},"&dark .cm-textfield":{border:"1px solid #555",backgroundColor:"inherit"}},O1),fx={childList:!0,characterData:!0,subtree:!0,attributes:!0,characterDataOldValue:!0},bf=Z.ie&&Z.ie_version<=11;class cx{constructor(t){this.view=t,this.active=!1,this.editContext=null,this.selectionRange=new Gv,this.selectionChanged=!1,this.delayedFlush=-1,this.resizeTimeout=-1,this.queue=[],this.delayedAndroidKey=null,this.flushingAndroidKey=-1,this.lastChange=0,this.scrollTargets=[],this.intersection=null,this.resizeScroll=null,this.intersecting=!1,this.gapIntersection=null,this.gaps=[],this.printQuery=null,this.parentCheck=-1,this.dom=t.contentDOM,this.observer=new MutationObserver(i=>{for(let l of i)this.queue.push(l);(Z.ie&&Z.ie_version<=11||Z.ios&&t.composing)&&i.some(l=>l.type=="childList"&&l.removedNodes.length||l.type=="characterData"&&l.oldValue.length>l.target.nodeValue.length)?this.flushSoon():this.flush()}),window.EditContext&&t.constructor.EDIT_CONTEXT!==!1&&!(Z.chrome&&Z.chrome_version<126)&&(this.editContext=new gx(t),t.state.facet($i)&&(t.contentDOM.editContext=this.editContext.editContext)),bf&&(this.onCharData=i=>{this.queue.push({target:i.target,type:"characterData",oldValue:i.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this),this.onResize=this.onResize.bind(this),this.onPrint=this.onPrint.bind(this),this.onScroll=this.onScroll.bind(this),window.matchMedia&&(this.printQuery=window.matchMedia("print")),typeof ResizeObserver=="function"&&(this.resizeScroll=new ResizeObserver(()=>{var i;((i=this.view.docView)===null||i===void 0?void 0:i.lastUpdate)<Date.now()-75&&this.onResize()}),this.resizeScroll.observe(t.scrollDOM)),this.addWindowListeners(this.win=t.win),this.start(),typeof IntersectionObserver=="function"&&(this.intersection=new IntersectionObserver(i=>{this.parentCheck<0&&(this.parentCheck=setTimeout(this.listenForScroll.bind(this),1e3)),i.length>0&&i[i.length-1].intersectionRatio>0!=this.intersecting&&(this.intersecting=!this.intersecting,this.intersecting!=this.view.inView&&this.onScrollChanged(document.createEvent("Event")))},{threshold:[0,.001]}),this.intersection.observe(this.dom),this.gapIntersection=new IntersectionObserver(i=>{i.length>0&&i[i.length-1].intersectionRatio>0&&this.onScrollChanged(document.createEvent("Event"))},{})),this.listenForScroll(),this.readSelectionRange()}onScrollChanged(t){this.view.inputState.runHandlers("scroll",t),this.intersecting&&this.view.measure()}onScroll(t){this.intersecting&&this.flush(!1),this.editContext&&this.view.requestMeasure(this.editContext.measureReq),this.onScrollChanged(t)}onResize(){this.resizeTimeout<0&&(this.resizeTimeout=setTimeout(()=>{this.resizeTimeout=-1,this.view.requestMeasure()},50))}onPrint(t){(t.type=="change"||!t.type)&&!t.matches||(this.view.viewState.printing=!0,this.view.measure(),setTimeout(()=>{this.view.viewState.printing=!1,this.view.requestMeasure()},500))}updateGaps(t){if(this.gapIntersection&&(t.length!=this.gaps.length||this.gaps.some((i,l)=>i!=t[l]))){this.gapIntersection.disconnect();for(let i of t)this.gapIntersection.observe(i);this.gaps=t}}onSelectionChange(t){let i=this.selectionChanged;if(!this.readSelectionRange()||this.delayedAndroidKey)return;let{view:l}=this,a=this.selectionRange;if(l.state.facet($i)?l.root.activeElement!=this.dom:!po(this.dom,a))return;let o=a.anchorNode&&l.docView.nearest(a.anchorNode);if(o&&o.ignoreEvent(t)){i||(this.selectionChanged=!1);return}(Z.ie&&Z.ie_version<=11||Z.android&&Z.chrome)&&!l.state.selection.main.empty&&a.focusNode&&ya(a.focusNode,a.focusOffset,a.anchorNode,a.anchorOffset)?this.flushSoon():this.flush(!1)}readSelectionRange(){let{view:t}=this,i=Ta(t.root);if(!i)return!1;let l=Z.safari&&t.root.nodeType==11&&t.root.activeElement==this.dom&&dx(this.view,i)||i;if(!l||this.selectionRange.eq(l))return!1;let a=po(this.dom,l);return a&&!this.selectionChanged&&t.inputState.lastFocusTime>Date.now()-200&&t.inputState.lastTouchTime<Date.now()-300&&Wv(this.dom,l)?(this.view.inputState.lastFocusTime=0,t.docView.updateSelection(),!1):(this.selectionRange.setRange(l),a&&(this.selectionChanged=!0),!0)}setSelectionRange(t,i){this.selectionRange.set(t.node,t.offset,i.node,i.offset),this.selectionChanged=!1}clearSelectionRange(){this.selectionRange.set(null,0,null,0)}listenForScroll(){this.parentCheck=-1;let t=0,i=null;for(let l=this.dom;l;)if(l.nodeType==1)!i&&t<this.scrollTargets.length&&this.scrollTargets[t]==l?t++:i||(i=this.scrollTargets.slice(0,t)),i&&i.push(l),l=l.assignedSlot||l.parentNode;else if(l.nodeType==11)l=l.host;else break;if(t<this.scrollTargets.length&&!i&&(i=this.scrollTargets.slice(0,t)),i){for(let l of this.scrollTargets)l.removeEventListener("scroll",this.onScroll);for(let l of this.scrollTargets=i)l.addEventListener("scroll",this.onScroll)}}ignore(t){if(!this.active)return t();try{return this.stop(),t()}finally{this.start(),this.clear()}}start(){this.active||(this.observer.observe(this.dom,fx),bf&&this.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.active=!0)}stop(){this.active&&(this.active=!1,this.observer.disconnect(),bf&&this.dom.removeEventListener("DOMCharacterDataModified",this.onCharData))}clear(){this.processRecords(),this.queue.length=0,this.selectionChanged=!1}delayAndroidKey(t,i){var l;if(!this.delayedAndroidKey){let a=()=>{let o=this.delayedAndroidKey;o&&(this.clearDelayedAndroidKey(),this.view.inputState.lastKeyCode=o.keyCode,this.view.inputState.lastKeyTime=Date.now(),!this.flush()&&o.force&&Kl(this.dom,o.key,o.keyCode))};this.flushingAndroidKey=this.view.win.requestAnimationFrame(a)}(!this.delayedAndroidKey||t=="Enter")&&(this.delayedAndroidKey={key:t,keyCode:i,force:this.lastChange<Date.now()-50||!!(!((l=this.delayedAndroidKey)===null||l===void 0)&&l.force)})}clearDelayedAndroidKey(){this.win.cancelAnimationFrame(this.flushingAndroidKey),this.delayedAndroidKey=null,this.flushingAndroidKey=-1}flushSoon(){this.delayedFlush<0&&(this.delayedFlush=this.view.win.requestAnimationFrame(()=>{this.delayedFlush=-1,this.flush()}))}forceFlush(){this.delayedFlush>=0&&(this.view.win.cancelAnimationFrame(this.delayedFlush),this.delayedFlush=-1),this.flush()}pendingRecords(){for(let t of this.observer.takeRecords())this.queue.push(t);return this.queue}processRecords(){let t=this.pendingRecords();t.length&&(this.queue=[]);let i=-1,l=-1,a=!1;for(let o of t){let h=this.readMutation(o);h&&(h.typeOver&&(a=!0),i==-1?{from:i,to:l}=h:(i=Math.min(h.from,i),l=Math.max(h.to,l)))}return{from:i,to:l,typeOver:a}}readChange(){let{from:t,to:i,typeOver:l}=this.processRecords(),a=this.selectionChanged&&po(this.dom,this.selectionRange);if(t<0&&!a)return null;t>-1&&(this.lastChange=Date.now()),this.view.inputState.lastFocusTime=0,this.selectionChanged=!1;let o=new zb(this.view,t,i,l);return this.view.docView.domChanged={newSel:o.newSel?o.newSel.main:null},o}flush(t=!0){if(this.delayedFlush>=0||this.delayedAndroidKey)return!1;t&&this.readSelectionRange();let i=this.readChange();if(!i)return this.view.requestMeasure(),!1;let l=this.view.state,a=s1(this.view,i);return this.view.state==l&&(i.domChanged||i.newSel&&!i.newSel.main.eq(this.view.state.selection.main))&&this.view.update([]),a}readMutation(t){let i=this.view.docView.nearest(t.target);if(!i||i.ignoreMutation(t))return null;if(i.markDirty(t.type=="attributes"),t.type=="attributes"&&(i.flags|=4),t.type=="childList"){let l=AO(i,t.previousSibling||t.target.previousSibling,-1),a=AO(i,t.nextSibling||t.target.nextSibling,1);return{from:l?i.posAfter(l):i.posAtStart,to:a?i.posBefore(a):i.posAtEnd,typeOver:!1}}else return t.type=="characterData"?{from:i.posAtStart,to:i.posAtEnd,typeOver:t.target.nodeValue==t.oldValue}:null}setWindow(t){t!=this.win&&(this.removeWindowListeners(this.win),this.win=t,this.addWindowListeners(this.win))}addWindowListeners(t){t.addEventListener("resize",this.onResize),this.printQuery?this.printQuery.addEventListener?this.printQuery.addEventListener("change",this.onPrint):this.printQuery.addListener(this.onPrint):t.addEventListener("beforeprint",this.onPrint),t.addEventListener("scroll",this.onScroll),t.document.addEventListener("selectionchange",this.onSelectionChange)}removeWindowListeners(t){t.removeEventListener("scroll",this.onScroll),t.removeEventListener("resize",this.onResize),this.printQuery?this.printQuery.removeEventListener?this.printQuery.removeEventListener("change",this.onPrint):this.printQuery.removeListener(this.onPrint):t.removeEventListener("beforeprint",this.onPrint),t.document.removeEventListener("selectionchange",this.onSelectionChange)}update(t){this.editContext&&(this.editContext.update(t),t.startState.facet($i)!=t.state.facet($i)&&(t.view.contentDOM.editContext=t.state.facet($i)?this.editContext.editContext:null))}destroy(){var t,i,l;this.stop(),(t=this.intersection)===null||t===void 0||t.disconnect(),(i=this.gapIntersection)===null||i===void 0||i.disconnect(),(l=this.resizeScroll)===null||l===void 0||l.disconnect();for(let a of this.scrollTargets)a.removeEventListener("scroll",this.onScroll);this.removeWindowListeners(this.win),clearTimeout(this.parentCheck),clearTimeout(this.resizeTimeout),this.win.cancelAnimationFrame(this.delayedFlush),this.win.cancelAnimationFrame(this.flushingAndroidKey),this.editContext&&(this.view.contentDOM.editContext=null,this.editContext.destroy())}}function AO(r,t,i){for(;t;){let l=Ut.get(t);if(l&&l.parent==r)return l;let a=t.parentNode;t=a!=r.dom?a:i>0?t.nextSibling:t.previousSibling}return null}function RO(r,t){let i=t.startContainer,l=t.startOffset,a=t.endContainer,o=t.endOffset,h=r.docView.domAtPos(r.state.selection.main.anchor);return ya(h.node,h.offset,a,o)&&([i,l,a,o]=[a,o,i,l]),{anchorNode:i,anchorOffset:l,focusNode:a,focusOffset:o}}function dx(r,t){if(t.getComposedRanges){let a=t.getComposedRanges(r.root)[0];if(a)return RO(r,a)}let i=null;function l(a){a.preventDefault(),a.stopImmediatePropagation(),i=a.getTargetRanges()[0]}return r.contentDOM.addEventListener("beforeinput",l,!0),r.dom.ownerDocument.execCommand("indent"),r.contentDOM.removeEventListener("beforeinput",l,!0),i?RO(r,i):null}class gx{constructor(t){this.from=0,this.to=0,this.pendingContextChange=null,this.handlers=Object.create(null),this.composing=null,this.resetRange(t.state);let i=this.editContext=new window.EditContext({text:t.state.doc.sliceString(this.from,this.to),selectionStart:this.toContextPos(Math.max(this.from,Math.min(this.to,t.state.selection.main.anchor))),selectionEnd:this.toContextPos(t.state.selection.main.head)});this.handlers.textupdate=l=>{let a=t.state.selection.main,{anchor:o,head:h}=a,c=this.toEditorPos(l.updateRangeStart),d=this.toEditorPos(l.updateRangeEnd);t.inputState.composing>=0&&!this.composing&&(this.composing={contextBase:l.updateRangeStart,editorBase:c,drifted:!1});let p={from:c,to:d,insert:wt.of(l.text.split(`
`))};if(p.from==this.from&&o<this.from?p.from=o:p.to==this.to&&o>this.to&&(p.to=o),p.from==p.to&&!p.insert.length){let m=I.single(this.toEditorPos(l.selectionStart),this.toEditorPos(l.selectionEnd));m.main.eq(a)||t.dispatch({selection:m,userEvent:"select"});return}if((Z.mac||Z.android)&&p.from==h-1&&/^\. ?$/.test(l.text)&&t.contentDOM.getAttribute("autocorrect")=="off"&&(p={from:c,to:d,insert:wt.of([l.text.replace("."," ")])}),this.pendingContextChange=p,!t.state.readOnly){let m=this.to-this.from+(p.to-p.from+p.insert.length);Rc(t,p,I.single(this.toEditorPos(l.selectionStart,m),this.toEditorPos(l.selectionEnd,m)))}this.pendingContextChange&&(this.revertPending(t.state),this.setSelection(t.state))},this.handlers.characterboundsupdate=l=>{let a=[],o=null;for(let h=this.toEditorPos(l.rangeStart),c=this.toEditorPos(l.rangeEnd);h<c;h++){let d=t.coordsForChar(h);o=d&&new DOMRect(d.left,d.top,d.right-d.left,d.bottom-d.top)||o||new DOMRect,a.push(o)}i.updateCharacterBounds(l.rangeStart,a)},this.handlers.textformatupdate=l=>{let a=[];for(let o of l.getTextFormats()){let h=o.underlineStyle,c=o.underlineThickness;if(h!="None"&&c!="None"){let d=this.toEditorPos(o.rangeStart),p=this.toEditorPos(o.rangeEnd);if(d<p){let m=`text-decoration: underline ${h=="Dashed"?"dashed ":h=="Squiggle"?"wavy ":""}${c=="Thin"?1:2}px`;a.push(ne.mark({attributes:{style:m}}).range(d,p))}}}t.dispatch({effects:J0.of(ne.set(a))})},this.handlers.compositionstart=()=>{t.inputState.composing<0&&(t.inputState.composing=0,t.inputState.compositionFirstChange=!0)},this.handlers.compositionend=()=>{if(t.inputState.composing=-1,t.inputState.compositionFirstChange=null,this.composing){let{drifted:l}=this.composing;this.composing=null,l&&this.reset(t.state)}};for(let l in this.handlers)i.addEventListener(l,this.handlers[l]);this.measureReq={read:l=>{this.editContext.updateControlBounds(l.contentDOM.getBoundingClientRect());let a=Ta(l.root);a&&a.rangeCount&&this.editContext.updateSelectionBounds(a.getRangeAt(0).getBoundingClientRect())}}}applyEdits(t){let i=0,l=!1,a=this.pendingContextChange;return t.changes.iterChanges((o,h,c,d,p)=>{if(l)return;let m=p.length-(h-o);if(a&&h>=a.to)if(a.from==o&&a.to==h&&a.insert.eq(p)){a=this.pendingContextChange=null,i+=m,this.to+=m;return}else a=null,this.revertPending(t.state);if(o+=i,h+=i,h<=this.from)this.from+=m,this.to+=m;else if(o<this.to){if(o<this.from||h>this.to||this.to-this.from+p.length>3e4){l=!0;return}this.editContext.updateText(this.toContextPos(o),this.toContextPos(h),p.toString()),this.to+=m}i+=m}),a&&!l&&this.revertPending(t.state),!l}update(t){let i=this.pendingContextChange,l=t.startState.selection.main;this.composing&&(this.composing.drifted||!t.changes.touchesRange(l.from,l.to)&&t.transactions.some(a=>!a.isUserEvent("input.type")&&a.changes.touchesRange(this.from,this.to)))?(this.composing.drifted=!0,this.composing.editorBase=t.changes.mapPos(this.composing.editorBase)):!this.applyEdits(t)||!this.rangeIsValid(t.state)?(this.pendingContextChange=null,this.reset(t.state)):(t.docChanged||t.selectionSet||i)&&this.setSelection(t.state),(t.geometryChanged||t.docChanged||t.selectionSet)&&t.view.requestMeasure(this.measureReq)}resetRange(t){let{head:i}=t.selection.main;this.from=Math.max(0,i-1e4),this.to=Math.min(t.doc.length,i+1e4)}reset(t){this.resetRange(t),this.editContext.updateText(0,this.editContext.text.length,t.doc.sliceString(this.from,this.to)),this.setSelection(t)}revertPending(t){let i=this.pendingContextChange;this.pendingContextChange=null,this.editContext.updateText(this.toContextPos(i.from),this.toContextPos(i.from+i.insert.length),t.doc.sliceString(i.from,i.to))}setSelection(t){let{main:i}=t.selection,l=this.toContextPos(Math.max(this.from,Math.min(this.to,i.anchor))),a=this.toContextPos(i.head);(this.editContext.selectionStart!=l||this.editContext.selectionEnd!=a)&&this.editContext.updateSelection(l,a)}rangeIsValid(t){let{head:i}=t.selection.main;return!(this.from>0&&i-this.from<500||this.to<t.doc.length&&this.to-i<500||this.to-this.from>1e4*3)}toEditorPos(t,i=this.to-this.from){t=Math.min(t,i);let l=this.composing;return l&&l.drifted?l.editorBase+(t-l.contextBase):t+this.from}toContextPos(t){let i=this.composing;return i&&i.drifted?i.contextBase+(t-i.editorBase):t-this.from}destroy(){for(let t in this.handlers)this.editContext.removeEventListener(t,this.handlers[t])}}class yt{get state(){return this.viewState.state}get viewport(){return this.viewState.viewport}get visibleRanges(){return this.viewState.visibleRanges}get inView(){return this.viewState.inView}get composing(){return!!this.inputState&&this.inputState.composing>0}get compositionStarted(){return!!this.inputState&&this.inputState.composing>=0}get root(){return this._root}get win(){return this.dom.ownerDocument.defaultView||window}constructor(t={}){var i;this.plugins=[],this.pluginMap=new Map,this.editorAttrs={},this.contentAttrs={},this.bidiCache=[],this.destroyed=!1,this.updateState=2,this.measureScheduled=-1,this.measureRequests=[],this.contentDOM=document.createElement("div"),this.scrollDOM=document.createElement("div"),this.scrollDOM.tabIndex=-1,this.scrollDOM.className="cm-scroller",this.scrollDOM.appendChild(this.contentDOM),this.announceDOM=document.createElement("div"),this.announceDOM.className="cm-announced",this.announceDOM.setAttribute("aria-live","polite"),this.dom=document.createElement("div"),this.dom.appendChild(this.announceDOM),this.dom.appendChild(this.scrollDOM),t.parent&&t.parent.appendChild(this.dom);let{dispatch:l}=t;this.dispatchTransactions=t.dispatchTransactions||l&&(a=>a.forEach(o=>l(o,this)))||(a=>this.update(a)),this.dispatch=this.dispatch.bind(this),this._root=t.root||$v(t.parent)||document,this.viewState=new QO(t.state||At.create(t)),t.scrollTo&&t.scrollTo.is(Fr)&&(this.viewState.scrollTarget=t.scrollTo.value.clip(this.viewState.state)),this.plugins=this.state.facet(Yl).map(a=>new Of(a));for(let a of this.plugins)a.update(this);this.observer=new cx(this),this.inputState=new _b(this),this.inputState.ensureHandlers(this.plugins),this.docView=new aO(this),this.mountStyles(),this.updateAttrs(),this.updateState=0,this.requestMeasure(),!((i=document.fonts)===null||i===void 0)&&i.ready&&document.fonts.ready.then(()=>this.requestMeasure())}dispatch(...t){let i=t.length==1&&t[0]instanceof ge?t:t.length==1&&Array.isArray(t[0])?t[0]:[this.state.update(...t)];this.dispatchTransactions(i,this)}update(t){if(this.updateState!=0)throw new Error("Calls to EditorView.update are not allowed while an update is in progress");let i=!1,l=!1,a,o=this.state;for(let S of t){if(S.startState!=o)throw new RangeError("Trying to update state with a transaction that doesn't start from the previous state.");o=S.state}if(this.destroyed){this.viewState.state=o;return}let h=this.hasFocus,c=0,d=null;t.some(S=>S.annotation(f1))?(this.inputState.notifiedFocused=h,c=1):h!=this.inputState.notifiedFocused&&(this.inputState.notifiedFocused=h,d=c1(o,h),d||(c=1));let p=this.observer.delayedAndroidKey,m=null;if(p?(this.observer.clearDelayedAndroidKey(),m=this.observer.readChange(),(m&&!this.state.doc.eq(o.doc)||!this.state.selection.eq(o.selection))&&(m=null)):this.observer.clear(),o.facet(At.phrases)!=this.state.facet(At.phrases))return this.setState(o);a=Ro.create(this,o,t),a.flags|=c;let O=this.viewState.scrollTarget;try{this.updateState=2;for(let S of t){if(O&&(O=O.map(S.changes)),S.scrollIntoView){let{main:b}=S.state.selection;O=new Jl(b.empty?b:I.cursor(b.head,b.head>b.anchor?-1:1))}for(let b of S.effects)b.is(Fr)&&(O=b.value.clip(this.state))}this.viewState.update(a,O),this.bidiCache=zo.update(this.bidiCache,a.changes),a.empty||(this.updatePlugins(a),this.inputState.update(a)),i=this.docView.update(a),this.state.facet(ca)!=this.styleModules&&this.mountStyles(),l=this.updateAttrs(),this.showAnnouncements(t),this.docView.updateSelection(i,t.some(S=>S.isUserEvent("select.pointer")))}finally{this.updateState=0}if(a.startState.facet(lo)!=a.state.facet(lo)&&(this.viewState.mustMeasureContent=!0),(i||l||O||this.viewState.mustEnforceCursorAssoc||this.viewState.mustMeasureContent)&&this.requestMeasure(),i&&this.docViewUpdate(),!a.empty)for(let S of this.state.facet(sc))try{S(a)}catch(b){vi(this.state,b,"update listener")}(d||m)&&Promise.resolve().then(()=>{d&&this.state==d.startState&&this.dispatch(d),m&&!s1(this,m)&&p.force&&Kl(this.contentDOM,p.key,p.keyCode)})}setState(t){if(this.updateState!=0)throw new Error("Calls to EditorView.setState are not allowed while an update is in progress");if(this.destroyed){this.viewState.state=t;return}this.updateState=2;let i=this.hasFocus;try{for(let l of this.plugins)l.destroy(this);this.viewState=new QO(t),this.plugins=t.facet(Yl).map(l=>new Of(l)),this.pluginMap.clear();for(let l of this.plugins)l.update(this);this.docView.destroy(),this.docView=new aO(this),this.inputState.ensureHandlers(this.plugins),this.mountStyles(),this.updateAttrs(),this.bidiCache=[]}finally{this.updateState=0}i&&this.focus(),this.requestMeasure()}updatePlugins(t){let i=t.startState.facet(Yl),l=t.state.facet(Yl);if(i!=l){let a=[];for(let o of l){let h=i.indexOf(o);if(h<0)a.push(new Of(o));else{let c=this.plugins[h];c.mustUpdate=t,a.push(c)}}for(let o of this.plugins)o.mustUpdate!=t&&o.destroy(this);this.plugins=a,this.pluginMap.clear()}else for(let a of this.plugins)a.mustUpdate=t;for(let a=0;a<this.plugins.length;a++)this.plugins[a].update(this);i!=l&&this.inputState.ensureHandlers(this.plugins)}docViewUpdate(){for(let t of this.plugins){let i=t.value;if(i&&i.docViewUpdate)try{i.docViewUpdate(this)}catch(l){vi(this.state,l,"doc view update listener")}}}measure(t=!0){if(this.destroyed)return;if(this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.observer.delayedAndroidKey){this.measureScheduled=-1,this.requestMeasure();return}this.measureScheduled=0,t&&this.observer.forceFlush();let i=null,l=this.scrollDOM,a=l.scrollTop*this.scaleY,{scrollAnchorPos:o,scrollAnchorHeight:h}=this.viewState;Math.abs(a-this.viewState.scrollTop)>1&&(h=-1),this.viewState.scrollAnchorHeight=-1;try{for(let c=0;;c++){if(h<0)if(k0(l))o=-1,h=this.viewState.heightMap.height;else{let b=this.viewState.scrollAnchorAt(a);o=b.from,h=b.top}this.updateState=1;let d=this.viewState.measure(this);if(!d&&!this.measureRequests.length&&this.viewState.scrollTarget==null)break;if(c>5){console.warn(this.measureRequests.length?"Measure loop restarted more than 5 times":"Viewport failed to stabilize");break}let p=[];d&4||([this.measureRequests,p]=[p,this.measureRequests]);let m=p.map(b=>{try{return b.read(this)}catch(T){return vi(this.state,T),EO}}),O=Ro.create(this,this.state,[]),S=!1;O.flags|=d,i?i.flags|=d:i=O,this.updateState=2,O.empty||(this.updatePlugins(O),this.inputState.update(O),this.updateAttrs(),S=this.docView.update(O),S&&this.docViewUpdate());for(let b=0;b<p.length;b++)if(m[b]!=EO)try{let T=p[b];T.write&&T.write(m[b],this)}catch(T){vi(this.state,T)}if(S&&this.docView.updateSelection(!0),!O.viewportChanged&&this.measureRequests.length==0){if(this.viewState.editorHeight)if(this.viewState.scrollTarget){this.docView.scrollIntoView(this.viewState.scrollTarget),this.viewState.scrollTarget=null,h=-1;continue}else{let T=(o<0?this.viewState.heightMap.height:this.viewState.lineBlockAt(o).top)-h;if(T>1||T<-1){a=a+T,l.scrollTop=a/this.scaleY,h=-1;continue}}break}}}finally{this.updateState=0,this.measureScheduled=-1}if(i&&!i.empty)for(let c of this.state.facet(sc))c(i)}get themeClasses(){return uc+" "+(this.state.facet(oc)?m1:p1)+" "+this.state.facet(lo)}updateAttrs(){let t=zO(this,F0,{class:"cm-editor"+(this.hasFocus?" cm-focused ":" ")+this.themeClasses}),i={spellcheck:"false",autocorrect:"off",autocapitalize:"off",writingsuggestions:"false",translate:"no",contenteditable:this.state.facet($i)?"true":"false",class:"cm-content",style:`${Z.tabSize}: ${this.state.tabSize}`,role:"textbox","aria-multiline":"true"};this.state.readOnly&&(i["aria-readonly"]="true"),zO(this,kc,i);let l=this.observer.ignore(()=>{let a=tc(this.contentDOM,this.contentAttrs,i),o=tc(this.dom,this.editorAttrs,t);return a||o});return this.editorAttrs=t,this.contentAttrs=i,l}showAnnouncements(t){let i=!0;for(let l of t)for(let a of l.effects)if(a.is(yt.announce)){i&&(this.announceDOM.textContent=""),i=!1;let o=this.announceDOM.appendChild(document.createElement("div"));o.textContent=a.value}}mountStyles(){this.styleModules=this.state.facet(ca);let t=this.state.facet(yt.cspNonce);ts.mount(this.root,this.styleModules.concat(hx).reverse(),t?{nonce:t}:void 0)}readMeasured(){if(this.updateState==2)throw new Error("Reading the editor layout isn't allowed during an update");this.updateState==0&&this.measureScheduled>-1&&this.measure(!1)}requestMeasure(t){if(this.measureScheduled<0&&(this.measureScheduled=this.win.requestAnimationFrame(()=>this.measure())),t){if(this.measureRequests.indexOf(t)>-1)return;if(t.key!=null){for(let i=0;i<this.measureRequests.length;i++)if(this.measureRequests[i].key===t.key){this.measureRequests[i]=t;return}}this.measureRequests.push(t)}}plugin(t){let i=this.pluginMap.get(t);return(i===void 0||i&&i.plugin!=t)&&this.pluginMap.set(t,i=this.plugins.find(l=>l.plugin==t)||null),i&&i.update(this).value}get documentTop(){return this.contentDOM.getBoundingClientRect().top+this.viewState.paddingTop}get documentPadding(){return{top:this.viewState.paddingTop,bottom:this.viewState.paddingBottom}}get scaleX(){return this.viewState.scaleX}get scaleY(){return this.viewState.scaleY}elementAtHeight(t){return this.readMeasured(),this.viewState.elementAtHeight(t)}lineBlockAtHeight(t){return this.readMeasured(),this.viewState.lineBlockAtHeight(t)}get viewportLineBlocks(){return this.viewState.viewportLines}lineBlockAt(t){return this.viewState.lineBlockAt(t)}get contentHeight(){return this.viewState.contentHeight}moveByChar(t,i,l){return Sf(this,t,fO(this,t,i,l))}moveByGroup(t,i){return Sf(this,t,fO(this,t,i,l=>kb(this,t.head,l)))}visualLineSide(t,i){let l=this.bidiSpans(t),a=this.textDirectionAt(t.from),o=l[i?l.length-1:0];return I.cursor(o.side(i,a)+t.from,o.forward(!i,a)?1:-1)}moveToLineBoundary(t,i,l=!0){return Qb(this,t,i,l)}moveVertically(t,i,l){return Sf(this,t,Ab(this,t,i,l))}domAtPos(t){return this.docView.domAtPos(t)}posAtDOM(t,i=0){return this.docView.posFromDOM(t,i)}posAtCoords(t,i=!0){return this.readMeasured(),l1(this,t,i)}coordsAtPos(t,i=1){this.readMeasured();let l=this.docView.coordsAt(t,i);if(!l||l.left==l.right)return l;let a=this.state.doc.lineAt(t),o=this.bidiSpans(a),h=o[Rn.find(o,t-a.from,-1,i)];return Xo(l,h.dir==pe.LTR==i>0)}coordsForChar(t){return this.readMeasured(),this.docView.coordsForChar(t)}get defaultCharacterWidth(){return this.viewState.heightOracle.charWidth}get defaultLineHeight(){return this.viewState.heightOracle.lineHeight}get textDirection(){return this.viewState.defaultTextDirection}textDirectionAt(t){return!this.state.facet(Z0)||t<this.viewport.from||t>this.viewport.to?this.textDirection:(this.readMeasured(),this.docView.textDirectionAt(t))}get lineWrapping(){return this.viewState.heightOracle.lineWrapping}bidiSpans(t){if(t.length>px)return V0(t.length);let i=this.textDirectionAt(t.from),l;for(let o of this.bidiCache)if(o.from==t.from&&o.dir==i&&(o.fresh||P0(o.isolates,l=sO(this,t))))return o.order;l||(l=sO(this,t));let a=rb(t.text,i,l);return this.bidiCache.push(new zo(t.from,t.to,i,l,!0,a)),a}get hasFocus(){var t;return(this.dom.ownerDocument.hasFocus()||Z.safari&&((t=this.inputState)===null||t===void 0?void 0:t.lastContextMenu)>Date.now()-3e4)&&this.root.activeElement==this.contentDOM}focus(){this.observer.ignore(()=>{w0(this.contentDOM),this.docView.updateSelection()})}setRoot(t){this._root!=t&&(this._root=t,this.observer.setWindow((t.nodeType==9?t:t.ownerDocument).defaultView||window),this.mountStyles())}destroy(){this.root.activeElement==this.contentDOM&&this.contentDOM.blur();for(let t of this.plugins)t.destroy(this);this.plugins=[],this.inputState.destroy(),this.docView.destroy(),this.dom.remove(),this.observer.destroy(),this.measureScheduled>-1&&this.win.cancelAnimationFrame(this.measureScheduled),this.destroyed=!0}static scrollIntoView(t,i={}){return Fr.of(new Jl(typeof t=="number"?I.cursor(t):t,i.y,i.x,i.yMargin,i.xMargin))}scrollSnapshot(){let{scrollTop:t,scrollLeft:i}=this.scrollDOM,l=this.viewState.scrollAnchorAt(t);return Fr.of(new Jl(I.cursor(l.from),"start","start",l.top-t,i,!0))}setTabFocusMode(t){t==null?this.inputState.tabFocusMode=this.inputState.tabFocusMode<0?0:-1:typeof t=="boolean"?this.inputState.tabFocusMode=t?0:-1:this.inputState.tabFocusMode!=0&&(this.inputState.tabFocusMode=Date.now()+t)}static domEventHandlers(t){return ns.define(()=>({}),{eventHandlers:t})}static domEventObservers(t){return ns.define(()=>({}),{eventObservers:t})}static theme(t,i){let l=ts.newName(),a=[lo.of(l),ca.of(hc(`.${l}`,t))];return i&&i.dark&&a.push(oc.of(!0)),a}static baseTheme(t){return vc.lowest(ca.of(hc("."+uc,t,O1)))}static findFromDOM(t){var i;let l=t.querySelector(".cm-content"),a=l&&Ut.get(l)||Ut.get(t);return((i=a==null?void 0:a.rootView)===null||i===void 0?void 0:i.view)||null}}yt.styleModule=ca;yt.inputHandler=W0;yt.clipboardInputFilter=wc;yt.clipboardOutputFilter=Qc;yt.scrollHandler=K0;yt.focusChangeEffect=Y0;yt.perLineTextDirection=Z0;yt.exceptionSink=$0;yt.updateListener=sc;yt.editable=$i;yt.mouseSelectionStyle=G0;yt.dragMovesSelection=L0;yt.clickAddsSelectionRange=j0;yt.decorations=Qa;yt.outerDecorations=I0;yt.atomicRanges=Ac;yt.bidiIsolatedRanges=t1;yt.scrollMargins=e1;yt.darkTheme=oc;yt.cspNonce=ut.define({combine:r=>r.length?r[0]:""});yt.contentAttributes=kc;yt.editorAttributes=F0;yt.lineWrapping=yt.contentAttributes.of({class:"cm-lineWrapping"});yt.announce=$t.define();const px=4096,EO={};class zo{constructor(t,i,l,a,o,h){this.from=t,this.to=i,this.dir=l,this.isolates=a,this.fresh=o,this.order=h}static update(t,i){if(i.empty&&!t.some(o=>o.fresh))return t;let l=[],a=t.length?t[t.length-1].dir:pe.LTR;for(let o=Math.max(0,t.length-10);o<t.length;o++){let h=t[o];h.dir==a&&!i.touchesRange(h.from,h.to)&&l.push(new zo(i.mapPos(h.from,1),i.mapPos(h.to,-1),h.dir,h.isolates,!1,h.order))}return l}}function zO(r,t,i){for(let l=r.state.facet(t),a=l.length-1;a>=0;a--){let o=l[a],h=typeof o=="function"?o(r):o;h&&If(h,i)}return i}const mx=Z.mac?"mac":Z.windows?"win":Z.linux?"linux":"key";function Ox(r,t){const i=r.split(/-(?!$)/);let l=i[i.length-1];l=="Space"&&(l=" ");let a,o,h,c;for(let d=0;d<i.length-1;++d){const p=i[d];if(/^(cmd|meta|m)$/i.test(p))c=!0;else if(/^a(lt)?$/i.test(p))a=!0;else if(/^(c|ctrl|control)$/i.test(p))o=!0;else if(/^s(hift)?$/i.test(p))h=!0;else if(/^mod$/i.test(p))t=="mac"?c=!0:o=!0;else throw new Error("Unrecognized modifier name: "+p)}return a&&(l="Alt-"+l),o&&(l="Ctrl-"+l),c&&(l="Meta-"+l),h&&(l="Shift-"+l),l}function so(r,t,i){return t.altKey&&(r="Alt-"+r),t.ctrlKey&&(r="Ctrl-"+r),t.metaKey&&(r="Meta-"+r),i!==!1&&t.shiftKey&&(r="Shift-"+r),r}const yx=vc.default(yt.domEventHandlers({keydown(r,t){return xx(Sx(t.state),r,t,"editor")}})),y1=ut.define({enables:yx}),MO=new WeakMap;function Sx(r){let t=r.facet(y1),i=MO.get(t);return i||MO.set(t,i=bx(t.reduce((l,a)=>l.concat(a),[]))),i}let kn=null;const vx=4e3;function bx(r,t=mx){let i=Object.create(null),l=Object.create(null),a=(h,c)=>{let d=l[h];if(d==null)l[h]=c;else if(d!=c)throw new Error("Key binding "+h+" is used both as a regular binding and as a multi-stroke prefix")},o=(h,c,d,p,m)=>{var O,S;let b=i[h]||(i[h]=Object.create(null)),T=c.split(/ (?!$)/).map(q=>Ox(q,t));for(let q=1;q<T.length;q++){let $=T.slice(0,q).join(" ");a($,!0),b[$]||(b[$]={preventDefault:!0,stopPropagation:!1,run:[j=>{let W=kn={view:j,prefix:$,scope:h};return setTimeout(()=>{kn==W&&(kn=null)},vx),!0}]})}let z=T.join(" ");a(z,!1);let C=b[z]||(b[z]={preventDefault:!1,stopPropagation:!1,run:((S=(O=b._any)===null||O===void 0?void 0:O.run)===null||S===void 0?void 0:S.slice())||[]});d&&C.run.push(d),p&&(C.preventDefault=!0),m&&(C.stopPropagation=!0)};for(let h of r){let c=h.scope?h.scope.split(" "):["editor"];if(h.any)for(let p of c){let m=i[p]||(i[p]=Object.create(null));m._any||(m._any={preventDefault:!1,stopPropagation:!1,run:[]});let{any:O}=h;for(let S in m)m[S].run.push(b=>O(b,fc))}let d=h[t]||h.key;if(d)for(let p of c)o(p,d,h.run,h.preventDefault,h.stopPropagation),h.shift&&o(p,"Shift-"+d,h.shift,h.preventDefault,h.stopPropagation)}return i}let fc=null;function xx(r,t,i,l){fc=t;let a=Vv(t),o=Tv(a,0),h=wv(o)==a.length&&a!=" ",c="",d=!1,p=!1,m=!1;kn&&kn.view==i&&kn.scope==l&&(c=kn.prefix+" ",r1.indexOf(t.keyCode)<0&&(p=!0,kn=null));let O=new Set,S=C=>{if(C){for(let q of C.run)if(!O.has(q)&&(O.add(q),q(i)))return C.stopPropagation&&(m=!0),!0;C.preventDefault&&(C.stopPropagation&&(m=!0),p=!0)}return!1},b=r[l],T,z;return b&&(S(b[c+so(a,t,!h)])?d=!0:h&&(t.altKey||t.metaKey||t.ctrlKey)&&!(Z.windows&&t.ctrlKey&&t.altKey)&&(T=En[t.keyCode])&&T!=a?(S(b[c+so(T,t,!0)])||t.shiftKey&&(z=xa[t.keyCode])!=a&&z!=T&&S(b[c+so(z,t,!1)]))&&(d=!0):h&&t.shiftKey&&S(b[c+so(a,t,!0)])&&(d=!0),!d&&S(b._any)&&(d=!0)),p&&(d=!0),d&&m&&t.stopPropagation(),fc=null,d}class ss extends il{compare(t){return this==t||this.constructor==t.constructor&&this.eq(t)}eq(t){return!1}destroy(t){}}ss.prototype.elementClass="";ss.prototype.toDOM=void 0;ss.prototype.mapMode=Te.TrackBefore;ss.prototype.startSide=ss.prototype.endSide=-1;ss.prototype.point=!0;const S1=1024;let Tx=0;class xf{constructor(t,i){this.from=t,this.to=i}}class Rt{constructor(t={}){this.id=Tx++,this.perNode=!!t.perNode,this.deserialize=t.deserialize||(()=>{throw new Error("This node type doesn't define a deserialize function")})}add(t){if(this.perNode)throw new RangeError("Can't add per-node props to node types");return typeof t!="function"&&(t=Ce.match(t)),i=>{let l=t(i);return l===void 0?null:[this,l]}}}Rt.closedBy=new Rt({deserialize:r=>r.split(" ")});Rt.openedBy=new Rt({deserialize:r=>r.split(" ")});Rt.group=new Rt({deserialize:r=>r.split(" ")});Rt.isolate=new Rt({deserialize:r=>{if(r&&r!="rtl"&&r!="ltr"&&r!="auto")throw new RangeError("Invalid value for isolate: "+r);return r||"auto"}});Rt.contextHash=new Rt({perNode:!0});Rt.lookAhead=new Rt({perNode:!0});Rt.mounted=new Rt({perNode:!0});class Mo{constructor(t,i,l){this.tree=t,this.overlay=i,this.parser=l}static get(t){return t&&t.props&&t.props[Rt.mounted.id]}}const wx=Object.create(null);class Ce{constructor(t,i,l,a=0){this.name=t,this.props=i,this.id=l,this.flags=a}static define(t){let i=t.props&&t.props.length?Object.create(null):wx,l=(t.top?1:0)|(t.skipped?2:0)|(t.error?4:0)|(t.name==null?8:0),a=new Ce(t.name||"",i,t.id,l);if(t.props){for(let o of t.props)if(Array.isArray(o)||(o=o(a)),o){if(o[0].perNode)throw new RangeError("Can't store a per-node prop on a node type");i[o[0].id]=o[1]}}return a}prop(t){return this.props[t.id]}get isTop(){return(this.flags&1)>0}get isSkipped(){return(this.flags&2)>0}get isError(){return(this.flags&4)>0}get isAnonymous(){return(this.flags&8)>0}is(t){if(typeof t=="string"){if(this.name==t)return!0;let i=this.prop(Rt.group);return i?i.indexOf(t)>-1:!1}return this.id==t}static match(t){let i=Object.create(null);for(let l in t)for(let a of l.split(" "))i[a]=t[l];return l=>{for(let a=l.prop(Rt.group),o=-1;o<(a?a.length:0);o++){let h=i[o<0?l.name:a[o]];if(h)return h}}}}Ce.none=new Ce("",Object.create(null),0,8);class Mc{constructor(t){this.types=t;for(let i=0;i<t.length;i++)if(t[i].id!=i)throw new RangeError("Node type ids should correspond to array positions when creating a node set")}extend(...t){let i=[];for(let l of this.types){let a=null;for(let o of t){let h=o(l);h&&(a||(a=Object.assign({},l.props)),a[h[0].id]=h[1])}i.push(a?new Ce(l.name,a,l.id,l.flags):l)}return new Mc(i)}}const ao=new WeakMap,CO=new WeakMap;var ie;(function(r){r[r.ExcludeBuffers=1]="ExcludeBuffers",r[r.IncludeAnonymous=2]="IncludeAnonymous",r[r.IgnoreMounts=4]="IgnoreMounts",r[r.IgnoreOverlays=8]="IgnoreOverlays"})(ie||(ie={}));class It{constructor(t,i,l,a,o){if(this.type=t,this.children=i,this.positions=l,this.length=a,this.props=null,o&&o.length){this.props=Object.create(null);for(let[h,c]of o)this.props[typeof h=="number"?h:h.id]=c}}toString(){let t=Mo.get(this);if(t&&!t.overlay)return t.tree.toString();let i="";for(let l of this.children){let a=l.toString();a&&(i&&(i+=","),i+=a)}return this.type.name?(/\W/.test(this.type.name)&&!this.type.isError?JSON.stringify(this.type.name):this.type.name)+(i.length?"("+i+")":""):i}cursor(t=0){return new dc(this.topNode,t)}cursorAt(t,i=0,l=0){let a=ao.get(this)||this.topNode,o=new dc(a);return o.moveTo(t,i),ao.set(this,o._tree),o}get topNode(){return new Me(this,0,0,null)}resolve(t,i=0){let l=ka(ao.get(this)||this.topNode,t,i,!1);return ao.set(this,l),l}resolveInner(t,i=0){let l=ka(CO.get(this)||this.topNode,t,i,!0);return CO.set(this,l),l}resolveStack(t,i=0){return Ax(this,t,i)}iterate(t){let{enter:i,leave:l,from:a=0,to:o=this.length}=t,h=t.mode||0,c=(h&ie.IncludeAnonymous)>0;for(let d=this.cursor(h|ie.IncludeAnonymous);;){let p=!1;if(d.from<=o&&d.to>=a&&(!c&&d.type.isAnonymous||i(d)!==!1)){if(d.firstChild())continue;p=!0}for(;p&&l&&(c||!d.type.isAnonymous)&&l(d),!d.nextSibling();){if(!d.parent())return;p=!0}}}prop(t){return t.perNode?this.props?this.props[t.id]:void 0:this.type.prop(t)}get propValues(){let t=[];if(this.props)for(let i in this.props)t.push([+i,this.props[i]]);return t}balance(t={}){return this.children.length<=8?this:qc(Ce.none,this.children,this.positions,0,this.children.length,0,this.length,(i,l,a)=>new It(this.type,i,l,a,this.propValues),t.makeTree||((i,l,a)=>new It(Ce.none,i,l,a)))}static build(t){return Rx(t)}}It.empty=new It(Ce.none,[],[],0);class Cc{constructor(t,i){this.buffer=t,this.index=i}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}get pos(){return this.index}next(){this.index-=4}fork(){return new Cc(this.buffer,this.index)}}class Mn{constructor(t,i,l){this.buffer=t,this.length=i,this.set=l}get type(){return Ce.none}toString(){let t=[];for(let i=0;i<this.buffer.length;)t.push(this.childString(i)),i=this.buffer[i+3];return t.join(",")}childString(t){let i=this.buffer[t],l=this.buffer[t+3],a=this.set.types[i],o=a.name;if(/\W/.test(o)&&!a.isError&&(o=JSON.stringify(o)),t+=4,l==t)return o;let h=[];for(;t<l;)h.push(this.childString(t)),t=this.buffer[t+3];return o+"("+h.join(",")+")"}findChild(t,i,l,a,o){let{buffer:h}=this,c=-1;for(let d=t;d!=i&&!(v1(o,a,h[d+1],h[d+2])&&(c=d,l>0));d=h[d+3]);return c}slice(t,i,l){let a=this.buffer,o=new Uint16Array(i-t),h=0;for(let c=t,d=0;c<i;){o[d++]=a[c++],o[d++]=a[c++]-l;let p=o[d++]=a[c++]-l;o[d++]=a[c++]-t,h=Math.max(h,p)}return new Mn(o,h,this.set)}}function v1(r,t,i,l){switch(r){case-2:return i<t;case-1:return l>=t&&i<t;case 0:return i<t&&l>t;case 1:return i<=t&&l>t;case 2:return l>t;case 4:return!0}}function ka(r,t,i,l){for(var a;r.from==r.to||(i<1?r.from>=t:r.from>t)||(i>-1?r.to<=t:r.to<t);){let h=!l&&r instanceof Me&&r.index<0?null:r.parent;if(!h)return r;r=h}let o=l?0:ie.IgnoreOverlays;if(l)for(let h=r,c=h.parent;c;h=c,c=h.parent)h instanceof Me&&h.index<0&&((a=c.enter(t,i,o))===null||a===void 0?void 0:a.from)!=h.from&&(r=c);for(;;){let h=r.enter(t,i,o);if(!h)return r;r=h}}class b1{cursor(t=0){return new dc(this,t)}getChild(t,i=null,l=null){let a=DO(this,t,i,l);return a.length?a[0]:null}getChildren(t,i=null,l=null){return DO(this,t,i,l)}resolve(t,i=0){return ka(this,t,i,!1)}resolveInner(t,i=0){return ka(this,t,i,!0)}matchContext(t){return cc(this.parent,t)}enterUnfinishedNodesBefore(t){let i=this.childBefore(t),l=this;for(;i;){let a=i.lastChild;if(!a||a.to!=i.to)break;a.type.isError&&a.from==a.to?(l=i,i=a.prevSibling):i=a}return l}get node(){return this}get next(){return this.parent}}class Me extends b1{constructor(t,i,l,a){super(),this._tree=t,this.from=i,this.index=l,this._parent=a}get type(){return this._tree.type}get name(){return this._tree.type.name}get to(){return this.from+this._tree.length}nextChild(t,i,l,a,o=0){for(let h=this;;){for(let{children:c,positions:d}=h._tree,p=i>0?c.length:-1;t!=p;t+=i){let m=c[t],O=d[t]+h.from;if(v1(a,l,O,O+m.length)){if(m instanceof Mn){if(o&ie.ExcludeBuffers)continue;let S=m.findChild(0,m.buffer.length,i,l-O,a);if(S>-1)return new bi(new Qx(h,m,t,O),null,S)}else if(o&ie.IncludeAnonymous||!m.type.isAnonymous||Dc(m)){let S;if(!(o&ie.IgnoreMounts)&&(S=Mo.get(m))&&!S.overlay)return new Me(S.tree,O,t,h);let b=new Me(m,O,t,h);return o&ie.IncludeAnonymous||!b.type.isAnonymous?b:b.nextChild(i<0?m.children.length-1:0,i,l,a)}}}if(o&ie.IncludeAnonymous||!h.type.isAnonymous||(h.index>=0?t=h.index+i:t=i<0?-1:h._parent._tree.children.length,h=h._parent,!h))return null}}get firstChild(){return this.nextChild(0,1,0,4)}get lastChild(){return this.nextChild(this._tree.children.length-1,-1,0,4)}childAfter(t){return this.nextChild(0,1,t,2)}childBefore(t){return this.nextChild(this._tree.children.length-1,-1,t,-2)}enter(t,i,l=0){let a;if(!(l&ie.IgnoreOverlays)&&(a=Mo.get(this._tree))&&a.overlay){let o=t-this.from;for(let{from:h,to:c}of a.overlay)if((i>0?h<=o:h<o)&&(i<0?c>=o:c>o))return new Me(a.tree,a.overlay[0].from+this.from,-1,this)}return this.nextChild(0,1,t,i,l)}nextSignificantParent(){let t=this;for(;t.type.isAnonymous&&t._parent;)t=t._parent;return t}get parent(){return this._parent?this._parent.nextSignificantParent():null}get nextSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index+1,1,0,4):null}get prevSibling(){return this._parent&&this.index>=0?this._parent.nextChild(this.index-1,-1,0,4):null}get tree(){return this._tree}toTree(){return this._tree}toString(){return this._tree.toString()}}function DO(r,t,i,l){let a=r.cursor(),o=[];if(!a.firstChild())return o;if(i!=null){for(let h=!1;!h;)if(h=a.type.is(i),!a.nextSibling())return o}for(;;){if(l!=null&&a.type.is(l))return o;if(a.type.is(t)&&o.push(a.node),!a.nextSibling())return l==null?o:[]}}function cc(r,t,i=t.length-1){for(let l=r;i>=0;l=l.parent){if(!l)return!1;if(!l.type.isAnonymous){if(t[i]&&t[i]!=l.name)return!1;i--}}return!0}class Qx{constructor(t,i,l,a){this.parent=t,this.buffer=i,this.index=l,this.start=a}}class bi extends b1{get name(){return this.type.name}get from(){return this.context.start+this.context.buffer.buffer[this.index+1]}get to(){return this.context.start+this.context.buffer.buffer[this.index+2]}constructor(t,i,l){super(),this.context=t,this._parent=i,this.index=l,this.type=t.buffer.set.types[t.buffer.buffer[l]]}child(t,i,l){let{buffer:a}=this.context,o=a.findChild(this.index+4,a.buffer[this.index+3],t,i-this.context.start,l);return o<0?null:new bi(this.context,this,o)}get firstChild(){return this.child(1,0,4)}get lastChild(){return this.child(-1,0,4)}childAfter(t){return this.child(1,t,2)}childBefore(t){return this.child(-1,t,-2)}enter(t,i,l=0){if(l&ie.ExcludeBuffers)return null;let{buffer:a}=this.context,o=a.findChild(this.index+4,a.buffer[this.index+3],i>0?1:-1,t-this.context.start,i);return o<0?null:new bi(this.context,this,o)}get parent(){return this._parent||this.context.parent.nextSignificantParent()}externalSibling(t){return this._parent?null:this.context.parent.nextChild(this.context.index+t,t,0,4)}get nextSibling(){let{buffer:t}=this.context,i=t.buffer[this.index+3];return i<(this._parent?t.buffer[this._parent.index+3]:t.buffer.length)?new bi(this.context,this._parent,i):this.externalSibling(1)}get prevSibling(){let{buffer:t}=this.context,i=this._parent?this._parent.index+4:0;return this.index==i?this.externalSibling(-1):new bi(this.context,this._parent,t.findChild(i,this.index,-1,0,4))}get tree(){return null}toTree(){let t=[],i=[],{buffer:l}=this.context,a=this.index+4,o=l.buffer[this.index+3];if(o>a){let h=l.buffer[this.index+1];t.push(l.slice(a,o,h)),i.push(0)}return new It(this.type,t,i,this.to-this.from)}toString(){return this.context.buffer.childString(this.index)}}function x1(r){if(!r.length)return null;let t=0,i=r[0];for(let o=1;o<r.length;o++){let h=r[o];(h.from>i.from||h.to<i.to)&&(i=h,t=o)}let l=i instanceof Me&&i.index<0?null:i.parent,a=r.slice();return l?a[t]=l:a.splice(t,1),new kx(a,i)}class kx{constructor(t,i){this.heads=t,this.node=i}get next(){return x1(this.heads)}}function Ax(r,t,i){let l=r.resolveInner(t,i),a=null;for(let o=l instanceof Me?l:l.context.parent;o;o=o.parent)if(o.index<0){let h=o.parent;(a||(a=[l])).push(h.resolve(t,i)),o=h}else{let h=Mo.get(o.tree);if(h&&h.overlay&&h.overlay[0].from<=t&&h.overlay[h.overlay.length-1].to>=t){let c=new Me(h.tree,h.overlay[0].from+o.from,-1,o);(a||(a=[l])).push(ka(c,t,i,!1))}}return a?x1(a):l}class dc{get name(){return this.type.name}constructor(t,i=0){if(this.mode=i,this.buffer=null,this.stack=[],this.index=0,this.bufferNode=null,t instanceof Me)this.yieldNode(t);else{this._tree=t.context.parent,this.buffer=t.context;for(let l=t._parent;l;l=l._parent)this.stack.unshift(l.index);this.bufferNode=t,this.yieldBuf(t.index)}}yieldNode(t){return t?(this._tree=t,this.type=t.type,this.from=t.from,this.to=t.to,!0):!1}yieldBuf(t,i){this.index=t;let{start:l,buffer:a}=this.buffer;return this.type=i||a.set.types[a.buffer[t]],this.from=l+a.buffer[t+1],this.to=l+a.buffer[t+2],!0}yield(t){return t?t instanceof Me?(this.buffer=null,this.yieldNode(t)):(this.buffer=t.context,this.yieldBuf(t.index,t.type)):!1}toString(){return this.buffer?this.buffer.buffer.childString(this.index):this._tree.toString()}enterChild(t,i,l){if(!this.buffer)return this.yield(this._tree.nextChild(t<0?this._tree._tree.children.length-1:0,t,i,l,this.mode));let{buffer:a}=this.buffer,o=a.findChild(this.index+4,a.buffer[this.index+3],t,i-this.buffer.start,l);return o<0?!1:(this.stack.push(this.index),this.yieldBuf(o))}firstChild(){return this.enterChild(1,0,4)}lastChild(){return this.enterChild(-1,0,4)}childAfter(t){return this.enterChild(1,t,2)}childBefore(t){return this.enterChild(-1,t,-2)}enter(t,i,l=this.mode){return this.buffer?l&ie.ExcludeBuffers?!1:this.enterChild(1,t,i):this.yield(this._tree.enter(t,i,l))}parent(){if(!this.buffer)return this.yieldNode(this.mode&ie.IncludeAnonymous?this._tree._parent:this._tree.parent);if(this.stack.length)return this.yieldBuf(this.stack.pop());let t=this.mode&ie.IncludeAnonymous?this.buffer.parent:this.buffer.parent.nextSignificantParent();return this.buffer=null,this.yieldNode(t)}sibling(t){if(!this.buffer)return this._tree._parent?this.yield(this._tree.index<0?null:this._tree._parent.nextChild(this._tree.index+t,t,0,4,this.mode)):!1;let{buffer:i}=this.buffer,l=this.stack.length-1;if(t<0){let a=l<0?0:this.stack[l]+4;if(this.index!=a)return this.yieldBuf(i.findChild(a,this.index,-1,0,4))}else{let a=i.buffer[this.index+3];if(a<(l<0?i.buffer.length:i.buffer[this.stack[l]+3]))return this.yieldBuf(a)}return l<0?this.yield(this.buffer.parent.nextChild(this.buffer.index+t,t,0,4,this.mode)):!1}nextSibling(){return this.sibling(1)}prevSibling(){return this.sibling(-1)}atLastNode(t){let i,l,{buffer:a}=this;if(a){if(t>0){if(this.index<a.buffer.buffer.length)return!1}else for(let o=0;o<this.index;o++)if(a.buffer.buffer[o+3]<this.index)return!1;({index:i,parent:l}=a)}else({index:i,_parent:l}=this._tree);for(;l;{index:i,_parent:l}=l)if(i>-1)for(let o=i+t,h=t<0?-1:l._tree.children.length;o!=h;o+=t){let c=l._tree.children[o];if(this.mode&ie.IncludeAnonymous||c instanceof Mn||!c.type.isAnonymous||Dc(c))return!1}return!0}move(t,i){if(i&&this.enterChild(t,0,4))return!0;for(;;){if(this.sibling(t))return!0;if(this.atLastNode(t)||!this.parent())return!1}}next(t=!0){return this.move(1,t)}prev(t=!0){return this.move(-1,t)}moveTo(t,i=0){for(;(this.from==this.to||(i<1?this.from>=t:this.from>t)||(i>-1?this.to<=t:this.to<t))&&this.parent(););for(;this.enterChild(1,t,i););return this}get node(){if(!this.buffer)return this._tree;let t=this.bufferNode,i=null,l=0;if(t&&t.context==this.buffer)t:for(let a=this.index,o=this.stack.length;o>=0;){for(let h=t;h;h=h._parent)if(h.index==a){if(a==this.index)return h;i=h,l=o+1;break t}a=this.stack[--o]}for(let a=l;a<this.stack.length;a++)i=new bi(this.buffer,i,this.stack[a]);return this.bufferNode=new bi(this.buffer,i,this.index)}get tree(){return this.buffer?null:this._tree._tree}iterate(t,i){for(let l=0;;){let a=!1;if(this.type.isAnonymous||t(this)!==!1){if(this.firstChild()){l++;continue}this.type.isAnonymous||(a=!0)}for(;;){if(a&&i&&i(this),a=this.type.isAnonymous,!l)return;if(this.nextSibling())break;this.parent(),l--,a=!0}}}matchContext(t){if(!this.buffer)return cc(this.node.parent,t);let{buffer:i}=this.buffer,{types:l}=i.set;for(let a=t.length-1,o=this.stack.length-1;a>=0;o--){if(o<0)return cc(this._tree,t,a);let h=l[i.buffer[this.stack[o]]];if(!h.isAnonymous){if(t[a]&&t[a]!=h.name)return!1;a--}}return!0}}function Dc(r){return r.children.some(t=>t instanceof Mn||!t.type.isAnonymous||Dc(t))}function Rx(r){var t;let{buffer:i,nodeSet:l,maxBufferLength:a=S1,reused:o=[],minRepeatType:h=l.types.length}=r,c=Array.isArray(i)?new Cc(i,i.length):i,d=l.types,p=0,m=0;function O(X,J,Y,Ot,gt,Et){let{id:ot,start:F,end:xt,size:Qt}=c,vt=m,D=p;for(;Qt<0;)if(c.next(),Qt==-1){let U=o[ot];Y.push(U),Ot.push(F-X);return}else if(Qt==-3){p=ot;return}else if(Qt==-4){m=ot;return}else throw new RangeError(`Unrecognized record size: ${Qt}`);let L=d[ot],it,St,w=F-X;if(xt-F<=a&&(St=C(c.pos-J,gt))){let U=new Uint16Array(St.size-St.skip),V=c.pos-St.size,P=U.length;for(;c.pos>V;)P=q(St.start,U,P);it=new Mn(U,xt-St.start,l),w=St.start-X}else{let U=c.pos-Qt;c.next();let V=[],P=[],tt=ot>=h?ot:-1,ft=0,at=xt;for(;c.pos>U;)tt>=0&&c.id==tt&&c.size>=0?(c.end<=at-a&&(T(V,P,F,ft,c.end,at,tt,vt,D),ft=V.length,at=c.end),c.next()):Et>2500?S(F,U,V,P):O(F,U,V,P,tt,Et+1);if(tt>=0&&ft>0&&ft<V.length&&T(V,P,F,ft,F,at,tt,vt,D),V.reverse(),P.reverse(),tt>-1&&ft>0){let me=b(L,D);it=qc(L,V,P,0,V.length,0,xt-F,me,me)}else it=z(L,V,P,xt-F,vt-xt,D)}Y.push(it),Ot.push(w)}function S(X,J,Y,Ot){let gt=[],Et=0,ot=-1;for(;c.pos>J;){let{id:F,start:xt,end:Qt,size:vt}=c;if(vt>4)c.next();else{if(ot>-1&&xt<ot)break;ot<0&&(ot=Qt-a),gt.push(F,xt,Qt),Et++,c.next()}}if(Et){let F=new Uint16Array(Et*4),xt=gt[gt.length-2];for(let Qt=gt.length-3,vt=0;Qt>=0;Qt-=3)F[vt++]=gt[Qt],F[vt++]=gt[Qt+1]-xt,F[vt++]=gt[Qt+2]-xt,F[vt++]=vt;Y.push(new Mn(F,gt[2]-xt,l)),Ot.push(xt-X)}}function b(X,J){return(Y,Ot,gt)=>{let Et=0,ot=Y.length-1,F,xt;if(ot>=0&&(F=Y[ot])instanceof It){if(!ot&&F.type==X&&F.length==gt)return F;(xt=F.prop(Rt.lookAhead))&&(Et=Ot[ot]+F.length+xt)}return z(X,Y,Ot,gt,Et,J)}}function T(X,J,Y,Ot,gt,Et,ot,F,xt){let Qt=[],vt=[];for(;X.length>Ot;)Qt.push(X.pop()),vt.push(J.pop()+Y-gt);X.push(z(l.types[ot],Qt,vt,Et-gt,F-Et,xt)),J.push(gt-Y)}function z(X,J,Y,Ot,gt,Et,ot){if(Et){let F=[Rt.contextHash,Et];ot=ot?[F].concat(ot):[F]}if(gt>25){let F=[Rt.lookAhead,gt];ot=ot?[F].concat(ot):[F]}return new It(X,J,Y,Ot,ot)}function C(X,J){let Y=c.fork(),Ot=0,gt=0,Et=0,ot=Y.end-a,F={size:0,start:0,skip:0};t:for(let xt=Y.pos-X;Y.pos>xt;){let Qt=Y.size;if(Y.id==J&&Qt>=0){F.size=Ot,F.start=gt,F.skip=Et,Et+=4,Ot+=4,Y.next();continue}let vt=Y.pos-Qt;if(Qt<0||vt<xt||Y.start<ot)break;let D=Y.id>=h?4:0,L=Y.start;for(Y.next();Y.pos>vt;){if(Y.size<0)if(Y.size==-3)D+=4;else break t;else Y.id>=h&&(D+=4);Y.next()}gt=L,Ot+=Qt,Et+=D}return(J<0||Ot==X)&&(F.size=Ot,F.start=gt,F.skip=Et),F.size>4?F:void 0}function q(X,J,Y){let{id:Ot,start:gt,end:Et,size:ot}=c;if(c.next(),ot>=0&&Ot<h){let F=Y;if(ot>4){let xt=c.pos-(ot-4);for(;c.pos>xt;)Y=q(X,J,Y)}J[--Y]=F,J[--Y]=Et-X,J[--Y]=gt-X,J[--Y]=Ot}else ot==-3?p=Ot:ot==-4&&(m=Ot);return Y}let $=[],j=[];for(;c.pos>0;)O(r.start||0,r.bufferStart||0,$,j,-1,0);let W=(t=r.length)!==null&&t!==void 0?t:$.length?j[0]+$[0].length:0;return new It(d[r.topID],$.reverse(),j.reverse(),W)}const qO=new WeakMap;function So(r,t){if(!r.isAnonymous||t instanceof Mn||t.type!=r)return 1;let i=qO.get(t);if(i==null){i=1;for(let l of t.children){if(l.type!=r||!(l instanceof It)){i=1;break}i+=So(r,l)}qO.set(t,i)}return i}function qc(r,t,i,l,a,o,h,c,d){let p=0;for(let T=l;T<a;T++)p+=So(r,t[T]);let m=Math.ceil(p*1.5/8),O=[],S=[];function b(T,z,C,q,$){for(let j=C;j<q;){let W=j,X=z[j],J=So(r,T[j]);for(j++;j<q;j++){let Y=So(r,T[j]);if(J+Y>=m)break;J+=Y}if(j==W+1){if(J>m){let Y=T[W];b(Y.children,Y.positions,0,Y.children.length,z[W]+$);continue}O.push(T[W])}else{let Y=z[j-1]+T[j-1].length-X;O.push(qc(r,T,z,W,j,X,Y,null,d))}S.push(X+$-o)}}return b(t,i,l,a,0),(c||d)(O,S,h)}class Ex{constructor(){this.map=new WeakMap}setBuffer(t,i,l){let a=this.map.get(t);a||this.map.set(t,a=new Map),a.set(i,l)}getBuffer(t,i){let l=this.map.get(t);return l&&l.get(i)}set(t,i){t instanceof bi?this.setBuffer(t.context.buffer,t.index,i):t instanceof Me&&this.map.set(t.tree,i)}get(t){return t instanceof bi?this.getBuffer(t.context.buffer,t.index):t instanceof Me?this.map.get(t.tree):void 0}cursorSet(t,i){t.buffer?this.setBuffer(t.buffer.buffer,t.index,i):this.map.set(t.tree,i)}cursorGet(t){return t.buffer?this.getBuffer(t.buffer.buffer,t.index):this.map.get(t.tree)}}class el{constructor(t,i,l,a,o=!1,h=!1){this.from=t,this.to=i,this.tree=l,this.offset=a,this.open=(o?1:0)|(h?2:0)}get openStart(){return(this.open&1)>0}get openEnd(){return(this.open&2)>0}static addTree(t,i=[],l=!1){let a=[new el(0,t.length,t,0,!1,l)];for(let o of i)o.to>t.length&&a.push(o);return a}static applyChanges(t,i,l=128){if(!i.length)return t;let a=[],o=1,h=t.length?t[0]:null;for(let c=0,d=0,p=0;;c++){let m=c<i.length?i[c]:null,O=m?m.fromA:1e9;if(O-d>=l)for(;h&&h.from<O;){let S=h;if(d>=S.from||O<=S.to||p){let b=Math.max(S.from,d)-p,T=Math.min(S.to,O)-p;S=b>=T?null:new el(b,T,S.tree,S.offset+p,c>0,!!m)}if(S&&a.push(S),h.to>O)break;h=o<t.length?t[o++]:null}if(!m)break;d=m.toA,p=m.toA-m.toB}return a}}class T1{startParse(t,i,l){return typeof t=="string"&&(t=new zx(t)),l=l?l.length?l.map(a=>new xf(a.from,a.to)):[new xf(0,0)]:[new xf(0,t.length)],this.createParse(t,i||[],l)}parse(t,i,l){let a=this.startParse(t,i,l);for(;;){let o=a.advance();if(o)return o}}}class zx{constructor(t){this.string=t}get length(){return this.string.length}chunk(t){return this.string.slice(t)}get lineChunks(){return!1}read(t,i){return this.string.slice(t,i)}}new Rt({perNode:!0});var _O={};class Co{constructor(t,i,l,a,o,h,c,d,p,m=0,O){this.p=t,this.stack=i,this.state=l,this.reducePos=a,this.pos=o,this.score=h,this.buffer=c,this.bufferBase=d,this.curContext=p,this.lookAhead=m,this.parent=O}toString(){return`[${this.stack.filter((t,i)=>i%3==0).concat(this.state)}]@${this.pos}${this.score?"!"+this.score:""}`}static start(t,i,l=0){let a=t.parser.context;return new Co(t,[],i,l,l,0,[],0,a?new NO(a,a.start):null,0,null)}get context(){return this.curContext?this.curContext.context:null}pushState(t,i){this.stack.push(this.state,i,this.bufferBase+this.buffer.length),this.state=t}reduce(t){var i;let l=t>>19,a=t&65535,{parser:o}=this.p,h=this.reducePos<this.pos-25;h&&this.setLookAhead(this.pos);let c=o.dynamicPrecedence(a);if(c&&(this.score+=c),l==0){this.pushState(o.getGoto(this.state,a,!0),this.reducePos),a<o.minRepeatTerm&&this.storeNode(a,this.reducePos,this.reducePos,h?8:4,!0),this.reduceContext(a,this.reducePos);return}let d=this.stack.length-(l-1)*3-(t&262144?6:0),p=d?this.stack[d-2]:this.p.ranges[0].from,m=this.reducePos-p;m>=2e3&&!(!((i=this.p.parser.nodeSet.types[a])===null||i===void 0)&&i.isAnonymous)&&(p==this.p.lastBigReductionStart?(this.p.bigReductionCount++,this.p.lastBigReductionSize=m):this.p.lastBigReductionSize<m&&(this.p.bigReductionCount=1,this.p.lastBigReductionStart=p,this.p.lastBigReductionSize=m));let O=d?this.stack[d-1]:0,S=this.bufferBase+this.buffer.length-O;if(a<o.minRepeatTerm||t&131072){let b=o.stateFlag(this.state,1)?this.pos:this.reducePos;this.storeNode(a,p,b,S+4,!0)}if(t&262144)this.state=this.stack[d];else{let b=this.stack[d-3];this.state=o.getGoto(b,a,!0)}for(;this.stack.length>d;)this.stack.pop();this.reduceContext(a,p)}storeNode(t,i,l,a=4,o=!1){if(t==0&&(!this.stack.length||this.stack[this.stack.length-1]<this.buffer.length+this.bufferBase)){let h=this,c=this.buffer.length;if(c==0&&h.parent&&(c=h.bufferBase-h.parent.bufferBase,h=h.parent),c>0&&h.buffer[c-4]==0&&h.buffer[c-1]>-1){if(i==l)return;if(h.buffer[c-2]>=i){h.buffer[c-2]=l;return}}}if(!o||this.pos==l)this.buffer.push(t,i,l,a);else{let h=this.buffer.length;if(h>0&&this.buffer[h-4]!=0){let c=!1;for(let d=h;d>0&&this.buffer[d-2]>l;d-=4)if(this.buffer[d-1]>=0){c=!0;break}if(c)for(;h>0&&this.buffer[h-2]>l;)this.buffer[h]=this.buffer[h-4],this.buffer[h+1]=this.buffer[h-3],this.buffer[h+2]=this.buffer[h-2],this.buffer[h+3]=this.buffer[h-1],h-=4,a>4&&(a-=4)}this.buffer[h]=t,this.buffer[h+1]=i,this.buffer[h+2]=l,this.buffer[h+3]=a}}shift(t,i,l,a){if(t&131072)this.pushState(t&65535,this.pos);else if((t&262144)==0){let o=t,{parser:h}=this.p;(a>this.pos||i<=h.maxNode)&&(this.pos=a,h.stateFlag(o,1)||(this.reducePos=a)),this.pushState(o,l),this.shiftContext(i,l),i<=h.maxNode&&this.buffer.push(i,l,a,4)}else this.pos=a,this.shiftContext(i,l),i<=this.p.parser.maxNode&&this.buffer.push(i,l,a,4)}apply(t,i,l,a){t&65536?this.reduce(t):this.shift(t,i,l,a)}useNode(t,i){let l=this.p.reused.length-1;(l<0||this.p.reused[l]!=t)&&(this.p.reused.push(t),l++);let a=this.pos;this.reducePos=this.pos=a+t.length,this.pushState(i,a),this.buffer.push(l,a,this.reducePos,-1),this.curContext&&this.updateContext(this.curContext.tracker.reuse(this.curContext.context,t,this,this.p.stream.reset(this.pos-t.length)))}split(){let t=this,i=t.buffer.length;for(;i>0&&t.buffer[i-2]>t.reducePos;)i-=4;let l=t.buffer.slice(i),a=t.bufferBase+i;for(;t&&a==t.bufferBase;)t=t.parent;return new Co(this.p,this.stack.slice(),this.state,this.reducePos,this.pos,this.score,l,a,this.curContext,this.lookAhead,t)}recoverByDelete(t,i){let l=t<=this.p.parser.maxNode;l&&this.storeNode(t,this.pos,i,4),this.storeNode(0,this.pos,i,l?8:4),this.pos=this.reducePos=i,this.score-=190}canShift(t){for(let i=new Mx(this);;){let l=this.p.parser.stateSlot(i.state,4)||this.p.parser.hasAction(i.state,t);if(l==0)return!1;if((l&65536)==0)return!0;i.reduce(l)}}recoverByInsert(t){if(this.stack.length>=300)return[];let i=this.p.parser.nextStates(this.state);if(i.length>8||this.stack.length>=120){let a=[];for(let o=0,h;o<i.length;o+=2)(h=i[o+1])!=this.state&&this.p.parser.hasAction(h,t)&&a.push(i[o],h);if(this.stack.length<120)for(let o=0;a.length<8&&o<i.length;o+=2){let h=i[o+1];a.some((c,d)=>d&1&&c==h)||a.push(i[o],h)}i=a}let l=[];for(let a=0;a<i.length&&l.length<4;a+=2){let o=i[a+1];if(o==this.state)continue;let h=this.split();h.pushState(o,this.pos),h.storeNode(0,h.pos,h.pos,4,!0),h.shiftContext(i[a],this.pos),h.reducePos=this.pos,h.score-=200,l.push(h)}return l}forceReduce(){let{parser:t}=this.p,i=t.stateSlot(this.state,5);if((i&65536)==0)return!1;if(!t.validAction(this.state,i)){let l=i>>19,a=i&65535,o=this.stack.length-l*3;if(o<0||t.getGoto(this.stack[o],a,!1)<0){let h=this.findForcedReduction();if(h==null)return!1;i=h}this.storeNode(0,this.pos,this.pos,4,!0),this.score-=100}return this.reducePos=this.pos,this.reduce(i),!0}findForcedReduction(){let{parser:t}=this.p,i=[],l=(a,o)=>{if(!i.includes(a))return i.push(a),t.allActions(a,h=>{if(!(h&393216))if(h&65536){let c=(h>>19)-o;if(c>1){let d=h&65535,p=this.stack.length-c*3;if(p>=0&&t.getGoto(this.stack[p],d,!1)>=0)return c<<19|65536|d}}else{let c=l(h,o+1);if(c!=null)return c}})};return l(this.state,0)}forceAll(){for(;!this.p.parser.stateFlag(this.state,2);)if(!this.forceReduce()){this.storeNode(0,this.pos,this.pos,4,!0);break}return this}get deadEnd(){if(this.stack.length!=3)return!1;let{parser:t}=this.p;return t.data[t.stateSlot(this.state,1)]==65535&&!t.stateSlot(this.state,4)}restart(){this.storeNode(0,this.pos,this.pos,4,!0),this.state=this.stack[0],this.stack.length=0}sameState(t){if(this.state!=t.state||this.stack.length!=t.stack.length)return!1;for(let i=0;i<this.stack.length;i+=3)if(this.stack[i]!=t.stack[i])return!1;return!0}get parser(){return this.p.parser}dialectEnabled(t){return this.p.parser.dialect.flags[t]}shiftContext(t,i){this.curContext&&this.updateContext(this.curContext.tracker.shift(this.curContext.context,t,this,this.p.stream.reset(i)))}reduceContext(t,i){this.curContext&&this.updateContext(this.curContext.tracker.reduce(this.curContext.context,t,this,this.p.stream.reset(i)))}emitContext(){let t=this.buffer.length-1;(t<0||this.buffer[t]!=-3)&&this.buffer.push(this.curContext.hash,this.pos,this.pos,-3)}emitLookAhead(){let t=this.buffer.length-1;(t<0||this.buffer[t]!=-4)&&this.buffer.push(this.lookAhead,this.pos,this.pos,-4)}updateContext(t){if(t!=this.curContext.context){let i=new NO(this.curContext.tracker,t);i.hash!=this.curContext.hash&&this.emitContext(),this.curContext=i}}setLookAhead(t){t>this.lookAhead&&(this.emitLookAhead(),this.lookAhead=t)}close(){this.curContext&&this.curContext.tracker.strict&&this.emitContext(),this.lookAhead>0&&this.emitLookAhead()}}class NO{constructor(t,i){this.tracker=t,this.context=i,this.hash=t.strict?t.hash(i):0}}class Mx{constructor(t){this.start=t,this.state=t.state,this.stack=t.stack,this.base=this.stack.length}reduce(t){let i=t&65535,l=t>>19;l==0?(this.stack==this.start.stack&&(this.stack=this.stack.slice()),this.stack.push(this.state,0,0),this.base+=3):this.base-=(l-1)*3;let a=this.start.p.parser.getGoto(this.stack[this.base-3],i,!0);this.state=a}}class Do{constructor(t,i,l){this.stack=t,this.pos=i,this.index=l,this.buffer=t.buffer,this.index==0&&this.maybeNext()}static create(t,i=t.bufferBase+t.buffer.length){return new Do(t,i,i-t.bufferBase)}maybeNext(){let t=this.stack.parent;t!=null&&(this.index=this.stack.bufferBase-t.bufferBase,this.stack=t,this.buffer=t.buffer)}get id(){return this.buffer[this.index-4]}get start(){return this.buffer[this.index-3]}get end(){return this.buffer[this.index-2]}get size(){return this.buffer[this.index-1]}next(){this.index-=4,this.pos-=4,this.index==0&&this.maybeNext()}fork(){return new Do(this.stack,this.pos,this.index)}}function ro(r,t=Uint16Array){if(typeof r!="string")return r;let i=null;for(let l=0,a=0;l<r.length;){let o=0;for(;;){let h=r.charCodeAt(l++),c=!1;if(h==126){o=65535;break}h>=92&&h--,h>=34&&h--;let d=h-32;if(d>=46&&(d-=46,c=!0),o+=d,c)break;o*=46}i?i[a++]=o:i=new t(o)}return i}class vo{constructor(){this.start=-1,this.value=-1,this.end=-1,this.extended=-1,this.lookAhead=0,this.mask=0,this.context=0}}const UO=new vo;class Cx{constructor(t,i){this.input=t,this.ranges=i,this.chunk="",this.chunkOff=0,this.chunk2="",this.chunk2Pos=0,this.next=-1,this.token=UO,this.rangeIndex=0,this.pos=this.chunkPos=i[0].from,this.range=i[0],this.end=i[i.length-1].to,this.readNext()}resolveOffset(t,i){let l=this.range,a=this.rangeIndex,o=this.pos+t;for(;o<l.from;){if(!a)return null;let h=this.ranges[--a];o-=l.from-h.to,l=h}for(;i<0?o>l.to:o>=l.to;){if(a==this.ranges.length-1)return null;let h=this.ranges[++a];o+=h.from-l.to,l=h}return o}clipPos(t){if(t>=this.range.from&&t<this.range.to)return t;for(let i of this.ranges)if(i.to>t)return Math.max(t,i.from);return this.end}peek(t){let i=this.chunkOff+t,l,a;if(i>=0&&i<this.chunk.length)l=this.pos+t,a=this.chunk.charCodeAt(i);else{let o=this.resolveOffset(t,1);if(o==null)return-1;if(l=o,l>=this.chunk2Pos&&l<this.chunk2Pos+this.chunk2.length)a=this.chunk2.charCodeAt(l-this.chunk2Pos);else{let h=this.rangeIndex,c=this.range;for(;c.to<=l;)c=this.ranges[++h];this.chunk2=this.input.chunk(this.chunk2Pos=l),l+this.chunk2.length>c.to&&(this.chunk2=this.chunk2.slice(0,c.to-l)),a=this.chunk2.charCodeAt(0)}}return l>=this.token.lookAhead&&(this.token.lookAhead=l+1),a}acceptToken(t,i=0){let l=i?this.resolveOffset(i,-1):this.pos;if(l==null||l<this.token.start)throw new RangeError("Token end out of bounds");this.token.value=t,this.token.end=l}acceptTokenTo(t,i){this.token.value=t,this.token.end=i}getChunk(){if(this.pos>=this.chunk2Pos&&this.pos<this.chunk2Pos+this.chunk2.length){let{chunk:t,chunkPos:i}=this;this.chunk=this.chunk2,this.chunkPos=this.chunk2Pos,this.chunk2=t,this.chunk2Pos=i,this.chunkOff=this.pos-this.chunkPos}else{this.chunk2=this.chunk,this.chunk2Pos=this.chunkPos;let t=this.input.chunk(this.pos),i=this.pos+t.length;this.chunk=i>this.range.to?t.slice(0,this.range.to-this.pos):t,this.chunkPos=this.pos,this.chunkOff=0}}readNext(){return this.chunkOff>=this.chunk.length&&(this.getChunk(),this.chunkOff==this.chunk.length)?this.next=-1:this.next=this.chunk.charCodeAt(this.chunkOff)}advance(t=1){for(this.chunkOff+=t;this.pos+t>=this.range.to;){if(this.rangeIndex==this.ranges.length-1)return this.setDone();t-=this.range.to-this.pos,this.range=this.ranges[++this.rangeIndex],this.pos=this.range.from}return this.pos+=t,this.pos>=this.token.lookAhead&&(this.token.lookAhead=this.pos+1),this.readNext()}setDone(){return this.pos=this.chunkPos=this.end,this.range=this.ranges[this.rangeIndex=this.ranges.length-1],this.chunk="",this.next=-1}reset(t,i){if(i?(this.token=i,i.start=t,i.lookAhead=t+1,i.value=i.extended=-1):this.token=UO,this.pos!=t){if(this.pos=t,t==this.end)return this.setDone(),this;for(;t<this.range.from;)this.range=this.ranges[--this.rangeIndex];for(;t>=this.range.to;)this.range=this.ranges[++this.rangeIndex];t>=this.chunkPos&&t<this.chunkPos+this.chunk.length?this.chunkOff=t-this.chunkPos:(this.chunk="",this.chunkOff=0),this.readNext()}return this}read(t,i){if(t>=this.chunkPos&&i<=this.chunkPos+this.chunk.length)return this.chunk.slice(t-this.chunkPos,i-this.chunkPos);if(t>=this.chunk2Pos&&i<=this.chunk2Pos+this.chunk2.length)return this.chunk2.slice(t-this.chunk2Pos,i-this.chunk2Pos);if(t>=this.range.from&&i<=this.range.to)return this.input.read(t,i);let l="";for(let a of this.ranges){if(a.from>=i)break;a.to>t&&(l+=this.input.read(Math.max(a.from,t),Math.min(a.to,i)))}return l}}class Fl{constructor(t,i){this.data=t,this.id=i}token(t,i){let{parser:l}=i.p;Dx(this.data,t,i,this.id,l.data,l.tokenPrecTable)}}Fl.prototype.contextual=Fl.prototype.fallback=Fl.prototype.extend=!1;Fl.prototype.fallback=Fl.prototype.extend=!1;class jo{constructor(t,i={}){this.token=t,this.contextual=!!i.contextual,this.fallback=!!i.fallback,this.extend=!!i.extend}}function Dx(r,t,i,l,a,o){let h=0,c=1<<l,{dialect:d}=i.p.parser;t:for(;(c&r[h])!=0;){let p=r[h+1];for(let b=h+3;b<p;b+=2)if((r[b+1]&c)>0){let T=r[b];if(d.allows(T)&&(t.token.value==-1||t.token.value==T||qx(T,t.token.value,a,o))){t.acceptToken(T);break}}let m=t.next,O=0,S=r[h+2];if(t.next<0&&S>O&&r[p+S*3-3]==65535){h=r[p+S*3-1];continue t}for(;O<S;){let b=O+S>>1,T=p+b+(b<<1),z=r[T],C=r[T+1]||65536;if(m<z)S=b;else if(m>=C)O=b+1;else{h=r[T+2],t.advance();continue t}}break}}function BO(r,t,i){for(let l=t,a;(a=r[l])!=65535;l++)if(a==i)return l-t;return-1}function qx(r,t,i,l){let a=BO(i,l,t);return a<0||BO(i,l,r)<a}const He=typeof process<"u"&&_O&&/\bparse\b/.test(_O.LOG);let Tf=null;function XO(r,t,i){let l=r.cursor(ie.IncludeAnonymous);for(l.moveTo(t);;)if(!(i<0?l.childBefore(t):l.childAfter(t)))for(;;){if((i<0?l.to<t:l.from>t)&&!l.type.isError)return i<0?Math.max(0,Math.min(l.to-1,t-25)):Math.min(r.length,Math.max(l.from+1,t+25));if(i<0?l.prevSibling():l.nextSibling())break;if(!l.parent())return i<0?0:r.length}}class _x{constructor(t,i){this.fragments=t,this.nodeSet=i,this.i=0,this.fragment=null,this.safeFrom=-1,this.safeTo=-1,this.trees=[],this.start=[],this.index=[],this.nextFragment()}nextFragment(){let t=this.fragment=this.i==this.fragments.length?null:this.fragments[this.i++];if(t){for(this.safeFrom=t.openStart?XO(t.tree,t.from+t.offset,1)-t.offset:t.from,this.safeTo=t.openEnd?XO(t.tree,t.to+t.offset,-1)-t.offset:t.to;this.trees.length;)this.trees.pop(),this.start.pop(),this.index.pop();this.trees.push(t.tree),this.start.push(-t.offset),this.index.push(0),this.nextStart=this.safeFrom}else this.nextStart=1e9}nodeAt(t){if(t<this.nextStart)return null;for(;this.fragment&&this.safeTo<=t;)this.nextFragment();if(!this.fragment)return null;for(;;){let i=this.trees.length-1;if(i<0)return this.nextFragment(),null;let l=this.trees[i],a=this.index[i];if(a==l.children.length){this.trees.pop(),this.start.pop(),this.index.pop();continue}let o=l.children[a],h=this.start[i]+l.positions[a];if(h>t)return this.nextStart=h,null;if(o instanceof It){if(h==t){if(h<this.safeFrom)return null;let c=h+o.length;if(c<=this.safeTo){let d=o.prop(Rt.lookAhead);if(!d||c+d<this.fragment.to)return o}}this.index[i]++,h+o.length>=Math.max(this.safeFrom,t)&&(this.trees.push(o),this.start.push(h),this.index.push(0))}else this.index[i]++,this.nextStart=h+o.length}}}class Nx{constructor(t,i){this.stream=i,this.tokens=[],this.mainToken=null,this.actions=[],this.tokens=t.tokenizers.map(l=>new vo)}getActions(t){let i=0,l=null,{parser:a}=t.p,{tokenizers:o}=a,h=a.stateSlot(t.state,3),c=t.curContext?t.curContext.hash:0,d=0;for(let p=0;p<o.length;p++){if((1<<p&h)==0)continue;let m=o[p],O=this.tokens[p];if(!(l&&!m.fallback)&&((m.contextual||O.start!=t.pos||O.mask!=h||O.context!=c)&&(this.updateCachedToken(O,m,t),O.mask=h,O.context=c),O.lookAhead>O.end+25&&(d=Math.max(O.lookAhead,d)),O.value!=0)){let S=i;if(O.extended>-1&&(i=this.addActions(t,O.extended,O.end,i)),i=this.addActions(t,O.value,O.end,i),!m.extend&&(l=O,i>S))break}}for(;this.actions.length>i;)this.actions.pop();return d&&t.setLookAhead(d),!l&&t.pos==this.stream.end&&(l=new vo,l.value=t.p.parser.eofTerm,l.start=l.end=t.pos,i=this.addActions(t,l.value,l.end,i)),this.mainToken=l,this.actions}getMainToken(t){if(this.mainToken)return this.mainToken;let i=new vo,{pos:l,p:a}=t;return i.start=l,i.end=Math.min(l+1,a.stream.end),i.value=l==a.stream.end?a.parser.eofTerm:0,i}updateCachedToken(t,i,l){let a=this.stream.clipPos(l.pos);if(i.token(this.stream.reset(a,t),l),t.value>-1){let{parser:o}=l.p;for(let h=0;h<o.specialized.length;h++)if(o.specialized[h]==t.value){let c=o.specializers[h](this.stream.read(t.start,t.end),l);if(c>=0&&l.p.parser.dialect.allows(c>>1)){(c&1)==0?t.value=c>>1:t.extended=c>>1;break}}}else t.value=0,t.end=this.stream.clipPos(a+1)}putAction(t,i,l,a){for(let o=0;o<a;o+=3)if(this.actions[o]==t)return a;return this.actions[a++]=t,this.actions[a++]=i,this.actions[a++]=l,a}addActions(t,i,l,a){let{state:o}=t,{parser:h}=t.p,{data:c}=h;for(let d=0;d<2;d++)for(let p=h.stateSlot(o,d?2:1);;p+=3){if(c[p]==65535)if(c[p+1]==1)p=Gi(c,p+2);else{a==0&&c[p+1]==2&&(a=this.putAction(Gi(c,p+2),i,l,a));break}c[p]==i&&(a=this.putAction(Gi(c,p+1),i,l,a))}return a}}class Ux{constructor(t,i,l,a){this.parser=t,this.input=i,this.ranges=a,this.recovering=0,this.nextStackID=9812,this.minStackPos=0,this.reused=[],this.stoppedAt=null,this.lastBigReductionStart=-1,this.lastBigReductionSize=0,this.bigReductionCount=0,this.stream=new Cx(i,a),this.tokens=new Nx(t,this.stream),this.topTerm=t.top[1];let{from:o}=a[0];this.stacks=[Co.start(this,t.top[0],o)],this.fragments=l.length&&this.stream.end-o>t.bufferLength*4?new _x(l,t.nodeSet):null}get parsedPos(){return this.minStackPos}advance(){let t=this.stacks,i=this.minStackPos,l=this.stacks=[],a,o;if(this.bigReductionCount>300&&t.length==1){let[h]=t;for(;h.forceReduce()&&h.stack.length&&h.stack[h.stack.length-2]>=this.lastBigReductionStart;);this.bigReductionCount=this.lastBigReductionSize=0}for(let h=0;h<t.length;h++){let c=t[h];for(;;){if(this.tokens.mainToken=null,c.pos>i)l.push(c);else{if(this.advanceStack(c,l,t))continue;{a||(a=[],o=[]),a.push(c);let d=this.tokens.getMainToken(c);o.push(d.value,d.end)}}break}}if(!l.length){let h=a&&Px(a);if(h)return He&&console.log("Finish with "+this.stackID(h)),this.stackToTree(h);if(this.parser.strict)throw He&&a&&console.log("Stuck with token "+(this.tokens.mainToken?this.parser.getName(this.tokens.mainToken.value):"none")),new SyntaxError("No parse at "+i);this.recovering||(this.recovering=5)}if(this.recovering&&a){let h=this.stoppedAt!=null&&a[0].pos>this.stoppedAt?a[0]:this.runRecovery(a,o,l);if(h)return He&&console.log("Force-finish "+this.stackID(h)),this.stackToTree(h.forceAll())}if(this.recovering){let h=this.recovering==1?1:this.recovering*3;if(l.length>h)for(l.sort((c,d)=>d.score-c.score);l.length>h;)l.pop();l.some(c=>c.reducePos>i)&&this.recovering--}else if(l.length>1){t:for(let h=0;h<l.length-1;h++){let c=l[h];for(let d=h+1;d<l.length;d++){let p=l[d];if(c.sameState(p)||c.buffer.length>500&&p.buffer.length>500)if((c.score-p.score||c.buffer.length-p.buffer.length)>0)l.splice(d--,1);else{l.splice(h--,1);continue t}}}l.length>12&&l.splice(12,l.length-12)}this.minStackPos=l[0].pos;for(let h=1;h<l.length;h++)l[h].pos<this.minStackPos&&(this.minStackPos=l[h].pos);return null}stopAt(t){if(this.stoppedAt!=null&&this.stoppedAt<t)throw new RangeError("Can't move stoppedAt forward");this.stoppedAt=t}advanceStack(t,i,l){let a=t.pos,{parser:o}=this,h=He?this.stackID(t)+" -> ":"";if(this.stoppedAt!=null&&a>this.stoppedAt)return t.forceReduce()?t:null;if(this.fragments){let p=t.curContext&&t.curContext.tracker.strict,m=p?t.curContext.hash:0;for(let O=this.fragments.nodeAt(a);O;){let S=this.parser.nodeSet.types[O.type.id]==O.type?o.getGoto(t.state,O.type.id):-1;if(S>-1&&O.length&&(!p||(O.prop(Rt.contextHash)||0)==m))return t.useNode(O,S),He&&console.log(h+this.stackID(t)+` (via reuse of ${o.getName(O.type.id)})`),!0;if(!(O instanceof It)||O.children.length==0||O.positions[0]>0)break;let b=O.children[0];if(b instanceof It&&O.positions[0]==0)O=b;else break}}let c=o.stateSlot(t.state,4);if(c>0)return t.reduce(c),He&&console.log(h+this.stackID(t)+` (via always-reduce ${o.getName(c&65535)})`),!0;if(t.stack.length>=8400)for(;t.stack.length>6e3&&t.forceReduce(););let d=this.tokens.getActions(t);for(let p=0;p<d.length;){let m=d[p++],O=d[p++],S=d[p++],b=p==d.length||!l,T=b?t:t.split(),z=this.tokens.mainToken;if(T.apply(m,O,z?z.start:T.pos,S),He&&console.log(h+this.stackID(T)+` (via ${(m&65536)==0?"shift":`reduce of ${o.getName(m&65535)}`} for ${o.getName(O)} @ ${a}${T==t?"":", split"})`),b)return!0;T.pos>a?i.push(T):l.push(T)}return!1}advanceFully(t,i){let l=t.pos;for(;;){if(!this.advanceStack(t,null,null))return!1;if(t.pos>l)return PO(t,i),!0}}runRecovery(t,i,l){let a=null,o=!1;for(let h=0;h<t.length;h++){let c=t[h],d=i[h<<1],p=i[(h<<1)+1],m=He?this.stackID(c)+" -> ":"";if(c.deadEnd&&(o||(o=!0,c.restart(),He&&console.log(m+this.stackID(c)+" (restarted)"),this.advanceFully(c,l))))continue;let O=c.split(),S=m;for(let b=0;O.forceReduce()&&b<10&&(He&&console.log(S+this.stackID(O)+" (via force-reduce)"),!this.advanceFully(O,l));b++)He&&(S=this.stackID(O)+" -> ");for(let b of c.recoverByInsert(d))He&&console.log(m+this.stackID(b)+" (via recover-insert)"),this.advanceFully(b,l);this.stream.end>c.pos?(p==c.pos&&(p++,d=0),c.recoverByDelete(d,p),He&&console.log(m+this.stackID(c)+` (via recover-delete ${this.parser.getName(d)})`),PO(c,l)):(!a||a.score<c.score)&&(a=c)}return a}stackToTree(t){return t.close(),It.build({buffer:Do.create(t),nodeSet:this.parser.nodeSet,topID:this.topTerm,maxBufferLength:this.parser.bufferLength,reused:this.reused,start:this.ranges[0].from,length:t.pos-this.ranges[0].from,minRepeatType:this.parser.minRepeatTerm})}stackID(t){let i=(Tf||(Tf=new WeakMap)).get(t);return i||Tf.set(t,i=String.fromCodePoint(this.nextStackID++)),i+t}}function PO(r,t){for(let i=0;i<t.length;i++){let l=t[i];if(l.pos==r.pos&&l.sameState(r)){t[i].score<r.score&&(t[i]=r);return}}t.push(r)}class Bx{constructor(t,i,l){this.source=t,this.flags=i,this.disabled=l}allows(t){return!this.disabled||this.disabled[t]==0}}const wf=r=>r;class Xx{constructor(t){this.start=t.start,this.shift=t.shift||wf,this.reduce=t.reduce||wf,this.reuse=t.reuse||wf,this.hash=t.hash||(()=>0),this.strict=t.strict!==!1}}class qo extends T1{constructor(t){if(super(),this.wrappers=[],t.version!=14)throw new RangeError(`Parser version (${t.version}) doesn't match runtime version (14)`);let i=t.nodeNames.split(" ");this.minRepeatTerm=i.length;for(let c=0;c<t.repeatNodeCount;c++)i.push("");let l=Object.keys(t.topRules).map(c=>t.topRules[c][1]),a=[];for(let c=0;c<i.length;c++)a.push([]);function o(c,d,p){a[c].push([d,d.deserialize(String(p))])}if(t.nodeProps)for(let c of t.nodeProps){let d=c[0];typeof d=="string"&&(d=Rt[d]);for(let p=1;p<c.length;){let m=c[p++];if(m>=0)o(m,d,c[p++]);else{let O=c[p+-m];for(let S=-m;S>0;S--)o(c[p++],d,O);p++}}}this.nodeSet=new Mc(i.map((c,d)=>Ce.define({name:d>=this.minRepeatTerm?void 0:c,id:d,props:a[d],top:l.indexOf(d)>-1,error:d==0,skipped:t.skippedNodes&&t.skippedNodes.indexOf(d)>-1}))),t.propSources&&(this.nodeSet=this.nodeSet.extend(...t.propSources)),this.strict=!1,this.bufferLength=S1;let h=ro(t.tokenData);this.context=t.context,this.specializerSpecs=t.specialized||[],this.specialized=new Uint16Array(this.specializerSpecs.length);for(let c=0;c<this.specializerSpecs.length;c++)this.specialized[c]=this.specializerSpecs[c].term;this.specializers=this.specializerSpecs.map(VO),this.states=ro(t.states,Uint32Array),this.data=ro(t.stateData),this.goto=ro(t.goto),this.maxTerm=t.maxTerm,this.tokenizers=t.tokenizers.map(c=>typeof c=="number"?new Fl(h,c):c),this.topRules=t.topRules,this.dialects=t.dialects||{},this.dynamicPrecedences=t.dynamicPrecedences||null,this.tokenPrecTable=t.tokenPrec,this.termNames=t.termNames||null,this.maxNode=this.nodeSet.types.length-1,this.dialect=this.parseDialect(),this.top=this.topRules[Object.keys(this.topRules)[0]]}createParse(t,i,l){let a=new Ux(this,t,i,l);for(let o of this.wrappers)a=o(a,t,i,l);return a}getGoto(t,i,l=!1){let a=this.goto;if(i>=a[0])return-1;for(let o=a[i+1];;){let h=a[o++],c=h&1,d=a[o++];if(c&&l)return d;for(let p=o+(h>>1);o<p;o++)if(a[o]==t)return d;if(c)return-1}}hasAction(t,i){let l=this.data;for(let a=0;a<2;a++)for(let o=this.stateSlot(t,a?2:1),h;;o+=3){if((h=l[o])==65535)if(l[o+1]==1)h=l[o=Gi(l,o+2)];else{if(l[o+1]==2)return Gi(l,o+2);break}if(h==i||h==0)return Gi(l,o+1)}return 0}stateSlot(t,i){return this.states[t*6+i]}stateFlag(t,i){return(this.stateSlot(t,0)&i)>0}validAction(t,i){return!!this.allActions(t,l=>l==i?!0:null)}allActions(t,i){let l=this.stateSlot(t,4),a=l?i(l):void 0;for(let o=this.stateSlot(t,1);a==null;o+=3){if(this.data[o]==65535)if(this.data[o+1]==1)o=Gi(this.data,o+2);else break;a=i(Gi(this.data,o+1))}return a}nextStates(t){let i=[];for(let l=this.stateSlot(t,1);;l+=3){if(this.data[l]==65535)if(this.data[l+1]==1)l=Gi(this.data,l+2);else break;if((this.data[l+2]&1)==0){let a=this.data[l+1];i.some((o,h)=>h&1&&o==a)||i.push(this.data[l],a)}}return i}configure(t){let i=Object.assign(Object.create(qo.prototype),this);if(t.props&&(i.nodeSet=this.nodeSet.extend(...t.props)),t.top){let l=this.topRules[t.top];if(!l)throw new RangeError(`Invalid top rule name ${t.top}`);i.top=l}return t.tokenizers&&(i.tokenizers=this.tokenizers.map(l=>{let a=t.tokenizers.find(o=>o.from==l);return a?a.to:l})),t.specializers&&(i.specializers=this.specializers.slice(),i.specializerSpecs=this.specializerSpecs.map((l,a)=>{let o=t.specializers.find(c=>c.from==l.external);if(!o)return l;let h=Object.assign(Object.assign({},l),{external:o.to});return i.specializers[a]=VO(h),h})),t.contextTracker&&(i.context=t.contextTracker),t.dialect&&(i.dialect=this.parseDialect(t.dialect)),t.strict!=null&&(i.strict=t.strict),t.wrap&&(i.wrappers=i.wrappers.concat(t.wrap)),t.bufferLength!=null&&(i.bufferLength=t.bufferLength),i}hasWrappers(){return this.wrappers.length>0}getName(t){return this.termNames?this.termNames[t]:String(t<=this.maxNode&&this.nodeSet.types[t].name||t)}get eofTerm(){return this.maxNode+1}get topNode(){return this.nodeSet.types[this.top[1]]}dynamicPrecedence(t){let i=this.dynamicPrecedences;return i==null?0:i[t]||0}parseDialect(t){let i=Object.keys(this.dialects),l=i.map(()=>!1);if(t)for(let o of t.split(" ")){let h=i.indexOf(o);h>=0&&(l[h]=!0)}let a=null;for(let o=0;o<i.length;o++)if(!l[o])for(let h=this.dialects[i[o]],c;(c=this.data[h++])!=65535;)(a||(a=new Uint8Array(this.maxTerm+1)))[c]=1;return new Bx(t,l,a)}static deserialize(t){return new qo(t)}}function Gi(r,t){return r[t]|r[t+1]<<16}function Px(r){let t=null;for(let i of r){let l=i.p.stoppedAt;(i.pos==i.p.stream.end||l!=null&&i.pos>l)&&i.p.parser.stateFlag(i.state,2)&&(!t||t.score<i.score)&&(t=i)}return t}function VO(r){if(r.external){let t=r.extend?1:0;return(i,l)=>r.external(i,l)<<1|t}return r.get}let Vx=0;class ti{constructor(t,i,l,a){this.name=t,this.set=i,this.base=l,this.modified=a,this.id=Vx++}toString(){let{name:t}=this;for(let i of this.modified)i.name&&(t=`${i.name}(${t})`);return t}static define(t,i){let l=typeof t=="string"?t:"?";if(t instanceof ti&&(i=t),i!=null&&i.base)throw new Error("Can not derive from a modified tag");let a=new ti(l,[],null,[]);if(a.set.push(a),i)for(let o of i.set)a.set.push(o);return a}static defineModifier(t){let i=new _o(t);return l=>l.modified.indexOf(i)>-1?l:_o.get(l.base||l,l.modified.concat(i).sort((a,o)=>a.id-o.id))}}let Hx=0;class _o{constructor(t){this.name=t,this.instances=[],this.id=Hx++}static get(t,i){if(!i.length)return t;let l=i[0].instances.find(c=>c.base==t&&jx(i,c.modified));if(l)return l;let a=[],o=new ti(t.name,a,t,i);for(let c of i)c.instances.push(o);let h=Lx(i);for(let c of t.set)if(!c.modified.length)for(let d of h)a.push(_o.get(c,d));return o}}function jx(r,t){return r.length==t.length&&r.every((i,l)=>i==t[l])}function Lx(r){let t=[[]];for(let i=0;i<r.length;i++)for(let l=0,a=t.length;l<a;l++)t.push(t[l].concat(r[i]));return t.sort((i,l)=>l.length-i.length)}function w1(r){let t=Object.create(null);for(let i in r){let l=r[i];Array.isArray(l)||(l=[l]);for(let a of i.split(" "))if(a){let o=[],h=2,c=a;for(let O=0;;){if(c=="..."&&O>0&&O+3==a.length){h=1;break}let S=/^"(?:[^"\\]|\\.)*?"|[^\/!]+/.exec(c);if(!S)throw new RangeError("Invalid path: "+a);if(o.push(S[0]=="*"?"":S[0][0]=='"'?JSON.parse(S[0]):S[0]),O+=S[0].length,O==a.length)break;let b=a[O++];if(O==a.length&&b=="!"){h=0;break}if(b!="/")throw new RangeError("Invalid path: "+a);c=a.slice(O)}let d=o.length-1,p=o[d];if(!p)throw new RangeError("Invalid path: "+a);let m=new gc(l,h,d>0?o.slice(0,d):null);t[p]=m.sort(t[p])}}return Gx.add(t)}const Gx=new Rt;class gc{constructor(t,i,l,a){this.tags=t,this.mode=i,this.context=l,this.next=a}get opaque(){return this.mode==0}get inherit(){return this.mode==1}sort(t){return!t||t.depth<this.depth?(this.next=t,this):(t.next=this.sort(t.next),t)}get depth(){return this.context?this.context.length:0}}gc.empty=new gc([],2,null);function $x(r,t){let i=Object.create(null);for(let o of r)if(!Array.isArray(o.tag))i[o.tag.id]=o.class;else for(let h of o.tag)i[h.id]=o.class;let{scope:l,all:a=null}={};return{style:o=>{let h=a;for(let c of o)for(let d of c.set){let p=i[d.id];if(p){h=h?h+" "+p:p;break}}return h},scope:l}}const H=ti.define,oo=H(),wn=H(),HO=H(wn),jO=H(wn),Qn=H(),uo=H(Qn),Qf=H(Qn),Oi=H(),Jn=H(Oi),pi=H(),mi=H(),pc=H(),ha=H(pc),ho=H(),G={comment:oo,lineComment:H(oo),blockComment:H(oo),docComment:H(oo),name:wn,variableName:H(wn),typeName:HO,tagName:H(HO),propertyName:jO,attributeName:H(jO),className:H(wn),labelName:H(wn),namespace:H(wn),macroName:H(wn),literal:Qn,string:uo,docString:H(uo),character:H(uo),attributeValue:H(uo),number:Qf,integer:H(Qf),float:H(Qf),bool:H(Qn),regexp:H(Qn),escape:H(Qn),color:H(Qn),url:H(Qn),keyword:pi,self:H(pi),null:H(pi),atom:H(pi),unit:H(pi),modifier:H(pi),operatorKeyword:H(pi),controlKeyword:H(pi),definitionKeyword:H(pi),moduleKeyword:H(pi),operator:mi,derefOperator:H(mi),arithmeticOperator:H(mi),logicOperator:H(mi),bitwiseOperator:H(mi),compareOperator:H(mi),updateOperator:H(mi),definitionOperator:H(mi),typeOperator:H(mi),controlOperator:H(mi),punctuation:pc,separator:H(pc),bracket:ha,angleBracket:H(ha),squareBracket:H(ha),paren:H(ha),brace:H(ha),content:Oi,heading:Jn,heading1:H(Jn),heading2:H(Jn),heading3:H(Jn),heading4:H(Jn),heading5:H(Jn),heading6:H(Jn),contentSeparator:H(Oi),list:H(Oi),quote:H(Oi),emphasis:H(Oi),strong:H(Oi),link:H(Oi),monospace:H(Oi),strikethrough:H(Oi),inserted:H(),deleted:H(),changed:H(),invalid:H(),meta:ho,documentMeta:H(ho),annotation:H(ho),processingInstruction:H(ho),definition:ti.defineModifier("definition"),constant:ti.defineModifier("constant"),function:ti.defineModifier("function"),standard:ti.defineModifier("standard"),local:ti.defineModifier("local"),special:ti.defineModifier("special")};for(let r in G){let t=G[r];t instanceof ti&&(t.name=r)}$x([{tag:G.link,class:"tok-link"},{tag:G.heading,class:"tok-heading"},{tag:G.emphasis,class:"tok-emphasis"},{tag:G.strong,class:"tok-strong"},{tag:G.keyword,class:"tok-keyword"},{tag:G.atom,class:"tok-atom"},{tag:G.bool,class:"tok-bool"},{tag:G.url,class:"tok-url"},{tag:G.labelName,class:"tok-labelName"},{tag:G.inserted,class:"tok-inserted"},{tag:G.deleted,class:"tok-deleted"},{tag:G.literal,class:"tok-literal"},{tag:G.string,class:"tok-string"},{tag:G.number,class:"tok-number"},{tag:[G.regexp,G.escape,G.special(G.string)],class:"tok-string2"},{tag:G.variableName,class:"tok-variableName"},{tag:G.local(G.variableName),class:"tok-variableName tok-local"},{tag:G.definition(G.variableName),class:"tok-variableName tok-definition"},{tag:G.special(G.variableName),class:"tok-variableName2"},{tag:G.definition(G.propertyName),class:"tok-propertyName tok-definition"},{tag:G.typeName,class:"tok-typeName"},{tag:G.namespace,class:"tok-namespace"},{tag:G.className,class:"tok-className"},{tag:G.macroName,class:"tok-macroName"},{tag:G.propertyName,class:"tok-propertyName"},{tag:G.operator,class:"tok-operator"},{tag:G.comment,class:"tok-comment"},{tag:G.meta,class:"tok-meta"},{tag:G.invalid,class:"tok-invalid"},{tag:G.punctuation,class:"tok-punctuation"}]);const Wx=1,Q1=194,k1=195,Yx=196,LO=197,Zx=198,Kx=199,Jx=200,Fx=2,A1=3,GO=201,Ix=24,tT=25,eT=49,iT=50,nT=55,lT=56,sT=57,aT=59,rT=60,oT=61,uT=62,hT=63,fT=65,cT=238,dT=71,gT=241,pT=242,mT=243,OT=244,yT=245,ST=246,vT=247,bT=248,R1=72,xT=249,TT=250,wT=251,QT=252,kT=253,AT=254,RT=255,ET=256,zT=73,MT=77,CT=263,DT=112,qT=130,_T=151,NT=152,UT=155,al=10,Aa=13,_c=32,Lo=9,Nc=35,BT=40,XT=46,mc=123,$O=125,E1=39,z1=34,WO=92,PT=111,VT=120,HT=78,jT=117,LT=85,GT=new Set([tT,eT,iT,CT,fT,qT,lT,sT,cT,uT,hT,R1,zT,MT,rT,oT,_T,NT,UT,DT]);function kf(r){return r==al||r==Aa}function Af(r){return r>=48&&r<=57||r>=65&&r<=70||r>=97&&r<=102}const $T=new jo((r,t)=>{let i;if(r.next<0)r.acceptToken(Kx);else if(t.context.flags&bo)kf(r.next)&&r.acceptToken(Zx,1);else if(((i=r.peek(-1))<0||kf(i))&&t.canShift(LO)){let l=0;for(;r.next==_c||r.next==Lo;)r.advance(),l++;(r.next==al||r.next==Aa||r.next==Nc)&&r.acceptToken(LO,-l)}else kf(r.next)&&r.acceptToken(Yx,1)},{contextual:!0}),WT=new jo((r,t)=>{let i=t.context;if(i.flags)return;let l=r.peek(-1);if(l==al||l==Aa){let a=0,o=0;for(;;){if(r.next==_c)a++;else if(r.next==Lo)a+=8-a%8;else break;r.advance(),o++}a!=i.indent&&r.next!=al&&r.next!=Aa&&r.next!=Nc&&(a<i.indent?r.acceptToken(k1,-o):r.acceptToken(Q1))}}),bo=1,M1=2,Vi=4,Hi=8,ji=16,Li=32;function xo(r,t,i){this.parent=r,this.indent=t,this.flags=i,this.hash=(r?r.hash+r.hash<<8:0)+t+(t<<4)+i+(i<<6)}const YT=new xo(null,0,0);function ZT(r){let t=0;for(let i=0;i<r.length;i++)t+=r.charCodeAt(i)==Lo?8-t%8:1;return t}const YO=new Map([[gT,0],[pT,Vi],[mT,Hi],[OT,Hi|Vi],[yT,ji],[ST,ji|Vi],[vT,ji|Hi],[bT,ji|Hi|Vi],[xT,Li],[TT,Li|Vi],[wT,Li|Hi],[QT,Li|Hi|Vi],[kT,Li|ji],[AT,Li|ji|Vi],[RT,Li|ji|Hi],[ET,Li|ji|Hi|Vi]].map(([r,t])=>[r,t|M1])),KT=new Xx({start:YT,reduce(r,t,i,l){return r.flags&bo&&GT.has(t)||(t==dT||t==R1)&&r.flags&M1?r.parent:r},shift(r,t,i,l){return t==Q1?new xo(r,ZT(l.read(l.pos,i.pos)),0):t==k1?r.parent:t==Ix||t==nT||t==aT||t==A1?new xo(r,0,bo):YO.has(t)?new xo(r,0,YO.get(t)|r.flags&bo):r},hash(r){return r.hash}}),JT=new jo(r=>{for(let t=0;t<5;t++){if(r.next!="print".charCodeAt(t))return;r.advance()}if(!/\w/.test(String.fromCharCode(r.next)))for(let t=0;;t++){let i=r.peek(t);if(!(i==_c||i==Lo)){i!=BT&&i!=XT&&i!=al&&i!=Aa&&i!=Nc&&r.acceptToken(Wx);return}}}),FT=new jo((r,t)=>{let{flags:i}=t.context,l=i&Vi?z1:E1,a=(i&Hi)>0,o=!(i&ji),h=(i&Li)>0,c=r.pos;for(;!(r.next<0);)if(h&&r.next==mc)if(r.peek(1)==mc)r.advance(2);else{if(r.pos==c){r.acceptToken(A1,1);return}break}else if(o&&r.next==WO){if(r.pos==c){r.advance();let d=r.next;d>=0&&(r.advance(),IT(r,d)),r.acceptToken(Fx);return}break}else if(r.next==WO&&!o&&r.peek(1)>-1)r.advance(2);else if(r.next==l&&(!a||r.peek(1)==l&&r.peek(2)==l)){if(r.pos==c){r.acceptToken(GO,a?3:1);return}break}else if(r.next==al){if(a)r.advance();else if(r.pos==c){r.acceptToken(GO);return}break}else r.advance();r.pos>c&&r.acceptToken(Jx)});function IT(r,t){if(t==PT)for(let i=0;i<2&&r.next>=48&&r.next<=55;i++)r.advance();else if(t==VT)for(let i=0;i<2&&Af(r.next);i++)r.advance();else if(t==jT)for(let i=0;i<4&&Af(r.next);i++)r.advance();else if(t==LT)for(let i=0;i<8&&Af(r.next);i++)r.advance();else if(t==HT&&r.next==mc){for(r.advance();r.next>=0&&r.next!=$O&&r.next!=E1&&r.next!=z1&&r.next!=al;)r.advance();r.next==$O&&r.advance()}}const t2=w1({'async "*" "**" FormatConversion FormatSpec':G.modifier,"for while if elif else try except finally return raise break continue with pass assert await yield match case":G.controlKeyword,"in not and or is del":G.operatorKeyword,"from def class global nonlocal lambda":G.definitionKeyword,import:G.moduleKeyword,"with as print":G.keyword,Boolean:G.bool,None:G.null,VariableName:G.variableName,"CallExpression/VariableName":G.function(G.variableName),"FunctionDefinition/VariableName":G.function(G.definition(G.variableName)),"ClassDefinition/VariableName":G.definition(G.className),PropertyName:G.propertyName,"CallExpression/MemberExpression/PropertyName":G.function(G.propertyName),Comment:G.lineComment,Number:G.number,String:G.string,FormatString:G.special(G.string),Escape:G.escape,UpdateOp:G.updateOperator,"ArithOp!":G.arithmeticOperator,BitOp:G.bitwiseOperator,CompareOp:G.compareOperator,AssignOp:G.definitionOperator,Ellipsis:G.punctuation,At:G.meta,"( )":G.paren,"[ ]":G.squareBracket,"{ }":G.brace,".":G.derefOperator,", ;":G.separator}),e2={__proto__:null,await:44,or:54,and:56,in:60,not:62,is:64,if:70,else:72,lambda:76,yield:94,from:96,async:102,for:104,None:162,True:164,False:164,del:178,pass:182,break:186,continue:190,return:194,raise:202,import:206,as:208,global:212,nonlocal:214,assert:218,type:223,elif:236,while:240,try:246,except:248,finally:250,with:254,def:258,class:268,match:279,case:285},i2=qo.deserialize({version:14,states:"##jO`QeOOP$}OSOOO&WQtO'#HUOOQS'#Co'#CoOOQS'#Cp'#CpO'vQdO'#CnO*UQtO'#HTOOQS'#HU'#HUOOQS'#DU'#DUOOQS'#HT'#HTO*rQdO'#D_O+VQdO'#DfO+gQdO'#DjO+zOWO'#DuO,VOWO'#DvO.[QtO'#GuOOQS'#Gu'#GuO'vQdO'#GtO0ZQtO'#GtOOQS'#Eb'#EbO0rQdO'#EcOOQS'#Gs'#GsO0|QdO'#GrOOQV'#Gr'#GrO1XQdO'#FYOOQS'#G^'#G^O1^QdO'#FXOOQV'#IS'#ISOOQV'#Gq'#GqOOQV'#Fq'#FqQ`QeOOO'vQdO'#CqO1lQdO'#C}O1sQdO'#DRO2RQdO'#HYO2cQtO'#EVO'vQdO'#EWOOQS'#EY'#EYOOQS'#E['#E[OOQS'#E^'#E^O2wQdO'#E`O3_QdO'#EdO3rQdO'#EfO3zQtO'#EfO1XQdO'#EiO0rQdO'#ElO1XQdO'#EnO0rQdO'#EtO0rQdO'#EwO4VQdO'#EyO4^QdO'#FOO4iQdO'#EzO0rQdO'#FOO1XQdO'#FQO1XQdO'#FVO4nQdO'#F[P4uOdO'#GpPOOO)CBd)CBdOOQS'#Ce'#CeOOQS'#Cf'#CfOOQS'#Cg'#CgOOQS'#Ch'#ChOOQS'#Ci'#CiOOQS'#Cj'#CjOOQS'#Cl'#ClO'vQdO,59OO'vQdO,59OO'vQdO,59OO'vQdO,59OO'vQdO,59OO'vQdO,59OO5TQdO'#DoOOQS,5:Y,5:YO5hQdO'#HdOOQS,5:],5:]O5uQ!fO,5:]O5zQtO,59YO1lQdO,59bO1lQdO,59bO1lQdO,59bO8jQdO,59bO8oQdO,59bO8vQdO,59jO8}QdO'#HTO:TQdO'#HSOOQS'#HS'#HSOOQS'#D['#D[O:lQdO,59aO'vQdO,59aO:zQdO,59aOOQS,59y,59yO;PQdO,5:RO'vQdO,5:ROOQS,5:Q,5:QO;_QdO,5:QO;dQdO,5:XO'vQdO,5:XO'vQdO,5:VOOQS,5:U,5:UO;uQdO,5:UO;zQdO,5:WOOOW'#Fy'#FyO<POWO,5:aOOQS,5:a,5:aO<[QdO'#HwOOOW'#Dw'#DwOOOW'#Fz'#FzO<lOWO,5:bOOQS,5:b,5:bOOQS'#F}'#F}O<zQtO,5:iO?lQtO,5=`O@VQ#xO,5=`O@vQtO,5=`OOQS,5:},5:}OA_QeO'#GWOBqQdO,5;^OOQV,5=^,5=^OB|QtO'#IPOCkQdO,5;tOOQS-E:[-E:[OOQV,5;s,5;sO4dQdO'#FQOOQV-E9o-E9oOCsQtO,59]OEzQtO,59iOFeQdO'#HVOFpQdO'#HVO1XQdO'#HVOF{QdO'#DTOGTQdO,59mOGYQdO'#HZO'vQdO'#HZO0rQdO,5=tOOQS,5=t,5=tO0rQdO'#EROOQS'#ES'#ESOGwQdO'#GPOHXQdO,58|OHXQdO,58|O*xQdO,5:oOHgQtO'#H]OOQS,5:r,5:rOOQS,5:z,5:zOHzQdO,5;OOI]QdO'#IOO1XQdO'#H}OOQS,5;Q,5;QOOQS'#GT'#GTOIqQtO,5;QOJPQdO,5;QOJUQdO'#IQOOQS,5;T,5;TOJdQdO'#H|OOQS,5;W,5;WOJuQdO,5;YO4iQdO,5;`O4iQdO,5;cOJ}QtO'#ITO'vQdO'#ITOKXQdO,5;eO4VQdO,5;eO0rQdO,5;jO1XQdO,5;lOK^QeO'#EuOLjQgO,5;fO!!kQdO'#IUO4iQdO,5;jO!!vQdO,5;lO!#OQdO,5;qO!#ZQtO,5;vO'vQdO,5;vPOOO,5=[,5=[P!#bOSO,5=[P!#jOdO,5=[O!&bQtO1G.jO!&iQtO1G.jO!)YQtO1G.jO!)dQtO1G.jO!+}QtO1G.jO!,bQtO1G.jO!,uQdO'#HcO!-TQtO'#GuO0rQdO'#HcO!-_QdO'#HbOOQS,5:Z,5:ZO!-gQdO,5:ZO!-lQdO'#HeO!-wQdO'#HeO!.[QdO,5>OOOQS'#Ds'#DsOOQS1G/w1G/wOOQS1G.|1G.|O!/[QtO1G.|O!/cQtO1G.|O1lQdO1G.|O!0OQdO1G/UOOQS'#DZ'#DZO0rQdO,59tOOQS1G.{1G.{O!0VQdO1G/eO!0gQdO1G/eO!0oQdO1G/fO'vQdO'#H[O!0tQdO'#H[O!0yQtO1G.{O!1ZQdO,59iO!2aQdO,5=zO!2qQdO,5=zO!2yQdO1G/mO!3OQtO1G/mOOQS1G/l1G/lO!3`QdO,5=uO!4VQdO,5=uO0rQdO1G/qO!4tQdO1G/sO!4yQtO1G/sO!5ZQtO1G/qOOQS1G/p1G/pOOQS1G/r1G/rOOOW-E9w-E9wOOQS1G/{1G/{O!5kQdO'#HxO0rQdO'#HxO!5|QdO,5>cOOOW-E9x-E9xOOQS1G/|1G/|OOQS-E9{-E9{O!6[Q#xO1G2zO!6{QtO1G2zO'vQdO,5<jOOQS,5<j,5<jOOQS-E9|-E9|OOQS,5<r,5<rOOQS-E:U-E:UOOQV1G0x1G0xO1XQdO'#GRO!7dQtO,5>kOOQS1G1`1G1`O!8RQdO1G1`OOQS'#DV'#DVO0rQdO,5=qOOQS,5=q,5=qO!8WQdO'#FrO!8cQdO,59oO!8kQdO1G/XO!8uQtO,5=uOOQS1G3`1G3`OOQS,5:m,5:mO!9fQdO'#GtOOQS,5<k,5<kOOQS-E9}-E9}O!9wQdO1G.hOOQS1G0Z1G0ZO!:VQdO,5=wO!:gQdO,5=wO0rQdO1G0jO0rQdO1G0jO!:xQdO,5>jO!;ZQdO,5>jO1XQdO,5>jO!;lQdO,5>iOOQS-E:R-E:RO!;qQdO1G0lO!;|QdO1G0lO!<RQdO,5>lO!<aQdO,5>lO!<oQdO,5>hO!=VQdO,5>hO!=hQdO'#EpO0rQdO1G0tO!=sQdO1G0tO!=xQgO1G0zO!AvQgO1G0}O!EqQdO,5>oO!E{QdO,5>oO!FTQtO,5>oO0rQdO1G1PO!F_QdO1G1PO4iQdO1G1UO!!vQdO1G1WOOQV,5;a,5;aO!FdQfO,5;aO!FiQgO1G1QO!JjQdO'#GZO4iQdO1G1QO4iQdO1G1QO!JzQdO,5>pO!KXQdO,5>pO1XQdO,5>pOOQV1G1U1G1UO!KaQdO'#FSO!KrQ!fO1G1WO!KzQdO1G1WOOQV1G1]1G1]O4iQdO1G1]O!LPQdO1G1]O!LXQdO'#F^OOQV1G1b1G1bO!#ZQtO1G1bPOOO1G2v1G2vP!L^OSO1G2vOOQS,5=},5=}OOQS'#Dp'#DpO0rQdO,5=}O!LfQdO,5=|O!LyQdO,5=|OOQS1G/u1G/uO!MRQdO,5>PO!McQdO,5>PO!MkQdO,5>PO!NOQdO,5>PO!N`QdO,5>POOQS1G3j1G3jOOQS7+$h7+$hO!8kQdO7+$pO#!RQdO1G.|O#!YQdO1G.|OOQS1G/`1G/`OOQS,5<`,5<`O'vQdO,5<`OOQS7+%P7+%PO#!aQdO7+%POOQS-E9r-E9rOOQS7+%Q7+%QO#!qQdO,5=vO'vQdO,5=vOOQS7+$g7+$gO#!vQdO7+%PO##OQdO7+%QO##TQdO1G3fOOQS7+%X7+%XO##eQdO1G3fO##mQdO7+%XOOQS,5<_,5<_O'vQdO,5<_O##rQdO1G3aOOQS-E9q-E9qO#$iQdO7+%]OOQS7+%_7+%_O#$wQdO1G3aO#%fQdO7+%_O#%kQdO1G3gO#%{QdO1G3gO#&TQdO7+%]O#&YQdO,5>dO#&sQdO,5>dO#&sQdO,5>dOOQS'#Dx'#DxO#'UO&jO'#DzO#'aO`O'#HyOOOW1G3}1G3}O#'fQdO1G3}O#'nQdO1G3}O#'yQ#xO7+(fO#(jQtO1G2UP#)TQdO'#GOOOQS,5<m,5<mOOQS-E:P-E:POOQS7+&z7+&zOOQS1G3]1G3]OOQS,5<^,5<^OOQS-E9p-E9pOOQS7+$s7+$sO#)bQdO,5=`O#){QdO,5=`O#*^QtO,5<aO#*qQdO1G3cOOQS-E9s-E9sOOQS7+&U7+&UO#+RQdO7+&UO#+aQdO,5<nO#+uQdO1G4UOOQS-E:Q-E:QO#,WQdO1G4UOOQS1G4T1G4TOOQS7+&W7+&WO#,iQdO7+&WOOQS,5<p,5<pO#,tQdO1G4WOOQS-E:S-E:SOOQS,5<l,5<lO#-SQdO1G4SOOQS-E:O-E:OO1XQdO'#EqO#-jQdO'#EqO#-uQdO'#IRO#-}QdO,5;[OOQS7+&`7+&`O0rQdO7+&`O#.SQgO7+&fO!JmQdO'#GXO4iQdO7+&fO4iQdO7+&iO#2QQtO,5<tO'vQdO,5<tO#2[QdO1G4ZOOQS-E:W-E:WO#2fQdO1G4ZO4iQdO7+&kO0rQdO7+&kOOQV7+&p7+&pO!KrQ!fO7+&rO!KzQdO7+&rO`QeO1G0{OOQV-E:X-E:XO4iQdO7+&lO4iQdO7+&lOOQV,5<u,5<uO#2nQdO,5<uO!JmQdO,5<uOOQV7+&l7+&lO#2yQgO7+&lO#6tQdO,5<vO#7PQdO1G4[OOQS-E:Y-E:YO#7^QdO1G4[O#7fQdO'#IWO#7tQdO'#IWO1XQdO'#IWOOQS'#IW'#IWO#8PQdO'#IVOOQS,5;n,5;nO#8XQdO,5;nO0rQdO'#FUOOQV7+&r7+&rO4iQdO7+&rOOQV7+&w7+&wO4iQdO7+&wO#8^QfO,5;xOOQV7+&|7+&|POOO7+(b7+(bO#8cQdO1G3iOOQS,5<c,5<cO#8qQdO1G3hOOQS-E9u-E9uO#9UQdO,5<dO#9aQdO,5<dO#9tQdO1G3kOOQS-E9v-E9vO#:UQdO1G3kO#:^QdO1G3kO#:nQdO1G3kO#:UQdO1G3kOOQS<<H[<<H[O#:yQtO1G1zOOQS<<Hk<<HkP#;WQdO'#FtO8vQdO1G3bO#;eQdO1G3bO#;jQdO<<HkOOQS<<Hl<<HlO#;zQdO7+)QOOQS<<Hs<<HsO#<[QtO1G1yP#<{QdO'#FsO#=YQdO7+)RO#=jQdO7+)RO#=rQdO<<HwO#=wQdO7+({OOQS<<Hy<<HyO#>nQdO,5<bO'vQdO,5<bOOQS-E9t-E9tOOQS<<Hw<<HwOOQS,5<g,5<gO0rQdO,5<gO#>sQdO1G4OOOQS-E9y-E9yO#?^QdO1G4OO<[QdO'#H{OOOO'#D{'#D{OOOO'#F|'#F|O#?oO&jO,5:fOOOW,5>e,5>eOOOW7+)i7+)iO#?zQdO7+)iO#@SQdO1G2zO#@mQdO1G2zP'vQdO'#FuO0rQdO<<IpO1XQdO1G2YP1XQdO'#GSO#AOQdO7+)pO#AaQdO7+)pOOQS<<Ir<<IrP1XQdO'#GUP0rQdO'#GQOOQS,5;],5;]O#ArQdO,5>mO#BQQdO,5>mOOQS1G0v1G0vOOQS<<Iz<<IzOOQV-E:V-E:VO4iQdO<<JQOOQV,5<s,5<sO4iQdO,5<sOOQV<<JQ<<JQOOQV<<JT<<JTO#BYQtO1G2`P#BdQdO'#GYO#BkQdO7+)uO#BuQgO<<JVO4iQdO<<JVOOQV<<J^<<J^O4iQdO<<J^O!KrQ!fO<<J^O#FpQgO7+&gOOQV<<JW<<JWO#FzQgO<<JWOOQV1G2a1G2aO1XQdO1G2aO#JuQdO1G2aO4iQdO<<JWO1XQdO1G2bP0rQdO'#G[O#KQQdO7+)vO#K_QdO7+)vOOQS'#FT'#FTO0rQdO,5>rO#KgQdO,5>rO#KrQdO,5>rO#K}QdO,5>qO#L`QdO,5>qOOQS1G1Y1G1YOOQS,5;p,5;pOOQV<<Jc<<JcO#LhQdO1G1dOOQS7+)T7+)TP#LmQdO'#FwO#L}QdO1G2OO#MbQdO1G2OO#MrQdO1G2OP#M}QdO'#FxO#N[QdO7+)VO#NlQdO7+)VO#NlQdO7+)VO#NtQdO7+)VO$ UQdO7+(|O8vQdO7+(|OOQSAN>VAN>VO$ oQdO<<LmOOQSAN>cAN>cO0rQdO1G1|O$!PQtO1G1|P$!ZQdO'#FvOOQS1G2R1G2RP$!hQdO'#F{O$!uQdO7+)jO$#`QdO,5>gOOOO-E9z-E9zOOOW<<MT<<MTO$#nQdO7+(fOOQSAN?[AN?[OOQS7+'t7+'tO$$XQdO<<M[OOQS,5<q,5<qO$$jQdO1G4XOOQS-E:T-E:TOOQVAN?lAN?lOOQV1G2_1G2_O4iQdOAN?qO$$xQgOAN?qOOQVAN?xAN?xO4iQdOAN?xOOQV<<JR<<JRO4iQdOAN?rO4iQdO7+'{OOQV7+'{7+'{O1XQdO7+'{OOQVAN?rAN?rOOQS7+'|7+'|O$(sQdO<<MbOOQS1G4^1G4^O0rQdO1G4^OOQS,5<w,5<wO$)QQdO1G4]OOQS-E:Z-E:ZOOQU'#G_'#G_O$)cQfO7+'OO$)nQdO'#F_O$*uQdO7+'jO$+VQdO7+'jOOQS7+'j7+'jO$+bQdO<<LqO$+rQdO<<LqO$+rQdO<<LqO$+zQdO'#H^OOQS<<Lh<<LhO$,UQdO<<LhOOQS7+'h7+'hOOQS'#D|'#D|OOOO1G4R1G4RO$,oQdO1G4RO$,wQdO1G4RP!=hQdO'#GVOOQVG25]G25]O4iQdOG25]OOQVG25dG25dOOQVG25^G25^OOQV<<Kg<<KgO4iQdO<<KgOOQS7+)x7+)xP$-SQdO'#G]OOQU-E:]-E:]OOQV<<Jj<<JjO$-vQtO'#FaOOQS'#Fc'#FcO$.WQdO'#FbO$.xQdO'#FbOOQS'#Fb'#FbO$.}QdO'#IYO$)nQdO'#FiO$)nQdO'#FiO$/fQdO'#FjO$)nQdO'#FkO$/mQdO'#IZOOQS'#IZ'#IZO$0[QdO,5;yOOQS<<KU<<KUO$0dQdO<<KUO$0tQdOANB]O$1UQdOANB]O$1^QdO'#H_OOQS'#H_'#H_O1sQdO'#DcO$1wQdO,5=xOOQSANBSANBSOOOO7+)m7+)mO$2`QdO7+)mOOQVLD*wLD*wOOQVANARANARO5uQ!fO'#GaO$2hQtO,5<SO$)nQdO'#FmOOQS,5<W,5<WOOQS'#Fd'#FdO$3YQdO,5;|O$3_QdO,5;|OOQS'#Fg'#FgO$)nQdO'#G`O$4PQdO,5<QO$4kQdO,5>tO$4{QdO,5>tO1XQdO,5<PO$5^QdO,5<TO$5cQdO,5<TO$)nQdO'#I[O$5hQdO'#I[O$5mQdO,5<UOOQS,5<V,5<VO0rQdO'#FpOOQU1G1e1G1eO4iQdO1G1eOOQSAN@pAN@pO$5rQdOG27wO$6SQdO,59}OOQS1G3d1G3dOOOO<<MX<<MXOOQS,5<{,5<{OOQS-E:_-E:_O$6XQtO'#FaO$6`QdO'#I]O$6nQdO'#I]O$6vQdO,5<XOOQS1G1h1G1hO$6{QdO1G1hO$7QQdO,5<zOOQS-E:^-E:^O$7lQdO,5=OO$8TQdO1G4`OOQS-E:b-E:bOOQS1G1k1G1kOOQS1G1o1G1oO$8eQdO,5>vO$)nQdO,5>vOOQS1G1p1G1pOOQS,5<[,5<[OOQU7+'P7+'PO$+zQdO1G/iO$)nQdO,5<YO$8sQdO,5>wO$8zQdO,5>wOOQS1G1s1G1sOOQS7+'S7+'SP$)nQdO'#GdO$9SQdO1G4bO$9^QdO1G4bO$9fQdO1G4bOOQS7+%T7+%TO$9tQdO1G1tO$:SQtO'#FaO$:ZQdO,5<}OOQS,5<},5<}O$:iQdO1G4cOOQS-E:a-E:aO$)nQdO,5<|O$:pQdO,5<|O$:uQdO7+)|OOQS-E:`-E:`O$;PQdO7+)|O$)nQdO,5<ZP$)nQdO'#GcO$;XQdO1G2hO$)nQdO1G2hP$;gQdO'#GbO$;nQdO<<MhO$;xQdO1G1uO$<WQdO7+(SO8vQdO'#C}O8vQdO,59bO8vQdO,59bO8vQdO,59bO$<fQtO,5=`O8vQdO1G.|O0rQdO1G/XO0rQdO7+$pP$<yQdO'#GOO'vQdO'#GtO$=WQdO,59bO$=]QdO,59bO$=dQdO,59mO$=iQdO1G/UO1sQdO'#DRO8vQdO,59j",stateData:"$>S~O%cOS%^OSSOS%]PQ~OPdOVaOfoOhYOopOs!POvqO!PrO!Q{O!T!SO!U!RO!XZO!][O!h`O!r`O!s`O!t`O!{tO!}uO#PvO#RwO#TxO#XyO#ZzO#^|O#_|O#a}O#c!OO#l!QO#o!TO#s!UO#u!VO#z!WO#}hO$P!XO%oRO%pRO%tSO%uWO&Z]O&[]O&]]O&^]O&_]O&`]O&a]O&b]O&c^O&d^O&e^O&f^O&g^O&h^O&i^O&j^O~O%]!YO~OV!aO_!aOa!bOh!iO!X!kO!f!mO%j![O%k!]O%l!^O%m!_O%n!_O%o!`O%p!`O%q!aO%r!aO%s!aO~Ok%xXl%xXm%xXn%xXo%xXp%xXs%xXz%xX{%xX!x%xX#g%xX%[%xX%_%xX%z%xXg%xX!T%xX!U%xX%{%xX!W%xX![%xX!Q%xX#[%xXt%xX!m%xX~P%SOfoOhYO!XZO!][O!h`O!r`O!s`O!t`O%oRO%pRO%tSO%uWO&Z]O&[]O&]]O&^]O&_]O&`]O&a]O&b]O&c^O&d^O&e^O&f^O&g^O&h^O&i^O&j^O~Oz%wX{%wX#g%wX%[%wX%_%wX%z%wX~Ok!pOl!qOm!oOn!oOo!rOp!sOs!tO!x%wX~P)pOV!zOg!|Oo0cOv0qO!PrO~P'vOV#OOo0cOv0qO!W#PO~P'vOV#SOa#TOo0cOv0qO![#UO~P'vOQ#XO%`#XO%a#ZO~OQ#^OR#[O%`#^O%a#`O~OV%iX_%iXa%iXh%iXk%iXl%iXm%iXn%iXo%iXp%iXs%iXz%iX!X%iX!f%iX%j%iX%k%iX%l%iX%m%iX%n%iX%o%iX%p%iX%q%iX%r%iX%s%iXg%iX!T%iX!U%iX~O&Z]O&[]O&]]O&^]O&_]O&`]O&a]O&b]O&c^O&d^O&e^O&f^O&g^O&h^O&i^O&j^O{%iX!x%iX#g%iX%[%iX%_%iX%z%iX%{%iX!W%iX![%iX!Q%iX#[%iXt%iX!m%iX~P,eOz#dO{%hX!x%hX#g%hX%[%hX%_%hX%z%hX~Oo0cOv0qO~P'vO#g#gO%[#iO%_#iO~O%uWO~O!T#nO#u!VO#z!WO#}hO~OopO~P'vOV#sOa#tO%uWO{wP~OV#xOo0cOv0qO!Q#yO~P'vO{#{O!x$QO%z#|O#g!yX%[!yX%_!yX~OV#xOo0cOv0qO#g#SX%[#SX%_#SX~P'vOo0cOv0qO#g#WX%[#WX%_#WX~P'vOh$WO%uWO~O!f$YO!r$YO%uWO~OV$eO~P'vO!U$gO#s$hO#u$iO~O{$jO~OV$qO~P'vOS$sO%[$rO%_$rO%c$tO~OV$}Oa$}Og%POo0cOv0qO~P'vOo0cOv0qO{%SO~P'vO&Y%UO~Oa!bOh!iO!X!kO!f!mOVba_bakbalbambanbaobapbasbazba{ba!xba#gba%[ba%_ba%jba%kba%lba%mba%nba%oba%pba%qba%rba%sba%zbagba!Tba!Uba%{ba!Wba![ba!Qba#[batba!mba~On%ZO~Oo%ZO~P'vOo0cO~P'vOk0eOl0fOm0dOn0dOo0mOp0nOs0rOg%wX!T%wX!U%wX%{%wX!W%wX![%wX!Q%wX#[%wX!m%wX~P)pO%{%]Og%vXz%vX!T%vX!U%vX!W%vX{%vX~Og%_Oz%`O!T%dO!U%cO~Og%_O~Oz%gO!T%dO!U%cO!W&SX~O!W%kO~Oz%lO{%nO!T%dO!U%cO![%}X~O![%rO~O![%sO~OQ#XO%`#XO%a%uO~OV%wOo0cOv0qO!PrO~P'vOQ#^OR#[O%`#^O%a%zO~OV!qa_!qaa!qah!qak!qal!qam!qan!qao!qap!qas!qaz!qa{!qa!X!qa!f!qa!x!qa#g!qa%[!qa%_!qa%j!qa%k!qa%l!qa%m!qa%n!qa%o!qa%p!qa%q!qa%r!qa%s!qa%z!qag!qa!T!qa!U!qa%{!qa!W!qa![!qa!Q!qa#[!qat!qa!m!qa~P#yOz%|O{%ha!x%ha#g%ha%[%ha%_%ha%z%ha~P%SOV&OOopOvqO{%ha!x%ha#g%ha%[%ha%_%ha%z%ha~P'vOz%|O{%ha!x%ha#g%ha%[%ha%_%ha%z%ha~OPdOVaOopOvqO!PrO!Q{O!{tO!}uO#PvO#RwO#TxO#XyO#ZzO#^|O#_|O#a}O#c!OO#g$zX%[$zX%_$zX~P'vO#g#gO%[&TO%_&TO~O!f&UOh&sX%[&sXz&sX#[&sX#g&sX%_&sX#Z&sXg&sX~Oh!iO%[&WO~Okealeameaneaoeapeaseazea{ea!xea#gea%[ea%_ea%zeagea!Tea!Uea%{ea!Wea![ea!Qea#[eatea!mea~P%SOsqazqa{qa#gqa%[qa%_qa%zqa~Ok!pOl!qOm!oOn!oOo!rOp!sO!xqa~PEcO%z&YOz%yX{%yX~O%uWOz%yX{%yX~Oz&]O{wX~O{&_O~Oz%lO#g%}X%[%}X%_%}Xg%}X{%}X![%}X!m%}X%z%}X~OV0lOo0cOv0qO!PrO~P'vO%z#|O#gUa%[Ua%_Ua~Oz&hO#g&PX%[&PX%_&PXn&PX~P%SOz&kO!Q&jO#g#Wa%[#Wa%_#Wa~Oz&lO#[&nO#g&rX%[&rX%_&rXg&rX~O!f$YO!r$YO#Z&qO%uWO~O#Z&qO~Oz&sO#g&tX%[&tX%_&tX~Oz&uO#g&pX%[&pX%_&pX{&pX~O!X&wO%z&xO~Oz&|On&wX~P%SOn'PO~OPdOVaOopOvqO!PrO!Q{O!{tO!}uO#PvO#RwO#TxO#XyO#ZzO#^|O#_|O#a}O#c!OO%['UO~P'vOt'YO#p'WO#q'XOP#naV#naf#nah#nao#nas#nav#na!P#na!Q#na!T#na!U#na!X#na!]#na!h#na!r#na!s#na!t#na!{#na!}#na#P#na#R#na#T#na#X#na#Z#na#^#na#_#na#a#na#c#na#l#na#o#na#s#na#u#na#z#na#}#na$P#na%X#na%o#na%p#na%t#na%u#na&Z#na&[#na&]#na&^#na&_#na&`#na&a#na&b#na&c#na&d#na&e#na&f#na&g#na&h#na&i#na&j#na%Z#na%_#na~Oz'ZO#[']O{&xX~Oh'_O!X&wO~Oh!iO{$jO!X&wO~O{'eO~P%SO%['hO%_'hO~OS'iO%['hO%_'hO~OV!aO_!aOa!bOh!iO!X!kO!f!mO%l!^O%m!_O%n!_O%o!`O%p!`O%q!aO%r!aO%s!aOkWilWimWinWioWipWisWizWi{Wi!xWi#gWi%[Wi%_Wi%jWi%zWigWi!TWi!UWi%{Wi!WWi![Wi!QWi#[WitWi!mWi~O%k!]O~P!#uO%kWi~P!#uOV!aO_!aOa!bOh!iO!X!kO!f!mO%o!`O%p!`O%q!aO%r!aO%s!aOkWilWimWinWioWipWisWizWi{Wi!xWi#gWi%[Wi%_Wi%jWi%kWi%lWi%zWigWi!TWi!UWi%{Wi!WWi![Wi!QWi#[WitWi!mWi~O%m!_O%n!_O~P!&pO%mWi%nWi~P!&pOa!bOh!iO!X!kO!f!mOkWilWimWinWioWipWisWizWi{Wi!xWi#gWi%[Wi%_Wi%jWi%kWi%lWi%mWi%nWi%oWi%pWi%zWigWi!TWi!UWi%{Wi!WWi![Wi!QWi#[WitWi!mWi~OV!aO_!aO%q!aO%r!aO%s!aO~P!)nOVWi_Wi%qWi%rWi%sWi~P!)nO!T%dO!U%cOg&VXz&VX~O%z'kO%{'kO~P,eOz'mOg&UX~Og'oO~Oz'pO{'rO!W&XX~Oo0cOv0qOz'pO{'sO!W&XX~P'vO!W'uO~Om!oOn!oOo!rOp!sOkjisjizji{ji!xji#gji%[ji%_ji%zji~Ol!qO~P!.aOlji~P!.aOk0eOl0fOm0dOn0dOo0mOp0nO~Ot'wO~P!/jOV'|Og'}Oo0cOv0qO~P'vOg'}Oz(OO~Og(QO~O!U(SO~Og(TOz(OO!T%dO!U%cO~P%SOk0eOl0fOm0dOn0dOo0mOp0nOgqa!Tqa!Uqa%{qa!Wqa![qa!Qqa#[qatqa!mqa~PEcOV'|Oo0cOv0qO!W&Sa~P'vOz(WO!W&Sa~O!W(XO~Oz(WO!T%dO!U%cO!W&Sa~P%SOV(]Oo0cOv0qO![%}a#g%}a%[%}a%_%}ag%}a{%}a!m%}a%z%}a~P'vOz(^O![%}a#g%}a%[%}a%_%}ag%}a{%}a!m%}a%z%}a~O![(aO~Oz(^O!T%dO!U%cO![%}a~P%SOz(dO!T%dO!U%cO![&Ta~P%SOz(gO{&lX![&lX!m&lX%z&lX~O{(kO![(mO!m(nO%z(jO~OV&OOopOvqO{%hi!x%hi#g%hi%[%hi%_%hi%z%hi~P'vOz(pO{%hi!x%hi#g%hi%[%hi%_%hi%z%hi~O!f&UOh&sa%[&saz&sa#[&sa#g&sa%_&sa#Z&sag&sa~O%[(uO~OV#sOa#tO%uWO~Oz&]O{wa~OopOvqO~P'vOz(^O#g%}a%[%}a%_%}ag%}a{%}a![%}a!m%}a%z%}a~P%SOz(zO#g%hX%[%hX%_%hX%z%hX~O%z#|O#gUi%[Ui%_Ui~O#g&Pa%[&Pa%_&Pan&Pa~P'vOz(}O#g&Pa%[&Pa%_&Pan&Pa~O%uWO#g&ra%[&ra%_&rag&ra~Oz)SO#g&ra%[&ra%_&rag&ra~Og)VO~OV)WOh$WO%uWO~O#Z)XO~O%uWO#g&ta%[&ta%_&ta~Oz)ZO#g&ta%[&ta%_&ta~Oo0cOv0qO#g&pa%[&pa%_&pa{&pa~P'vOz)^O#g&pa%[&pa%_&pa{&pa~OV)`Oa)`O%uWO~O%z)eO~Ot)hO#j)gOP#hiV#hif#hih#hio#his#hiv#hi!P#hi!Q#hi!T#hi!U#hi!X#hi!]#hi!h#hi!r#hi!s#hi!t#hi!{#hi!}#hi#P#hi#R#hi#T#hi#X#hi#Z#hi#^#hi#_#hi#a#hi#c#hi#l#hi#o#hi#s#hi#u#hi#z#hi#}#hi$P#hi%X#hi%o#hi%p#hi%t#hi%u#hi&Z#hi&[#hi&]#hi&^#hi&_#hi&`#hi&a#hi&b#hi&c#hi&d#hi&e#hi&f#hi&g#hi&h#hi&i#hi&j#hi%Z#hi%_#hi~Ot)iOP#kiV#kif#kih#kio#kis#kiv#ki!P#ki!Q#ki!T#ki!U#ki!X#ki!]#ki!h#ki!r#ki!s#ki!t#ki!{#ki!}#ki#P#ki#R#ki#T#ki#X#ki#Z#ki#^#ki#_#ki#a#ki#c#ki#l#ki#o#ki#s#ki#u#ki#z#ki#}#ki$P#ki%X#ki%o#ki%p#ki%t#ki%u#ki&Z#ki&[#ki&]#ki&^#ki&_#ki&`#ki&a#ki&b#ki&c#ki&d#ki&e#ki&f#ki&g#ki&h#ki&i#ki&j#ki%Z#ki%_#ki~OV)kOn&wa~P'vOz)lOn&wa~Oz)lOn&wa~P%SOn)pO~O%Y)tO~Ot)wO#p'WO#q)vOP#niV#nif#nih#nio#nis#niv#ni!P#ni!Q#ni!T#ni!U#ni!X#ni!]#ni!h#ni!r#ni!s#ni!t#ni!{#ni!}#ni#P#ni#R#ni#T#ni#X#ni#Z#ni#^#ni#_#ni#a#ni#c#ni#l#ni#o#ni#s#ni#u#ni#z#ni#}#ni$P#ni%X#ni%o#ni%p#ni%t#ni%u#ni&Z#ni&[#ni&]#ni&^#ni&_#ni&`#ni&a#ni&b#ni&c#ni&d#ni&e#ni&f#ni&g#ni&h#ni&i#ni&j#ni%Z#ni%_#ni~OV)zOo0cOv0qO{$jO~P'vOo0cOv0qO{&xa~P'vOz*OO{&xa~OV*SOa*TOg*WO%q*UO%uWO~O{$jO&{*YO~Oh'_O~Oh!iO{$jO~O%[*_O~O%[*aO%_*aO~OV$}Oa$}Oo0cOv0qOg&Ua~P'vOz*dOg&Ua~Oo0cOv0qO{*gO!W&Xa~P'vOz*hO!W&Xa~Oo0cOv0qOz*hO{*kO!W&Xa~P'vOo0cOv0qOz*hO!W&Xa~P'vOz*hO{*kO!W&Xa~Om0dOn0dOo0mOp0nOgjikjisjizji!Tji!Uji%{ji!Wji{ji![ji#gji%[ji%_ji!Qji#[jitji!mji%zji~Ol0fO~P!NkOlji~P!NkOV'|Og*pOo0cOv0qO~P'vOn*rO~Og*pOz*tO~Og*uO~OV'|Oo0cOv0qO!W&Si~P'vOz*vO!W&Si~O!W*wO~OV(]Oo0cOv0qO![%}i#g%}i%[%}i%_%}ig%}i{%}i!m%}i%z%}i~P'vOz*zO!T%dO!U%cO![&Ti~Oz*}O![%}i#g%}i%[%}i%_%}ig%}i{%}i!m%}i%z%}i~O![+OO~Oa+QOo0cOv0qO![&Ti~P'vOz*zO![&Ti~O![+SO~OV+UOo0cOv0qO{&la![&la!m&la%z&la~P'vOz+VO{&la![&la!m&la%z&la~O!]+YO&n+[O![!nX~O![+^O~O{(kO![+_O~O{(kO![+_O!m+`O~OV&OOopOvqO{%hq!x%hq#g%hq%[%hq%_%hq%z%hq~P'vOz$ri{$ri!x$ri#g$ri%[$ri%_$ri%z$ri~P%SOV&OOopOvqO~P'vOV&OOo0cOv0qO#g%ha%[%ha%_%ha%z%ha~P'vOz+aO#g%ha%[%ha%_%ha%z%ha~Oz$ia#g$ia%[$ia%_$ian$ia~P%SO#g&Pi%[&Pi%_&Pin&Pi~P'vOz+dO#g#Wq%[#Wq%_#Wq~O#[+eOz$va#g$va%[$va%_$vag$va~O%uWO#g&ri%[&ri%_&rig&ri~Oz+gO#g&ri%[&ri%_&rig&ri~OV+iOh$WO%uWO~O%uWO#g&ti%[&ti%_&ti~Oo0cOv0qO#g&pi%[&pi%_&pi{&pi~P'vO{#{Oz#eX!W#eX~Oz+mO!W&uX~O!W+oO~Ot+rO#j)gOP#hqV#hqf#hqh#hqo#hqs#hqv#hq!P#hq!Q#hq!T#hq!U#hq!X#hq!]#hq!h#hq!r#hq!s#hq!t#hq!{#hq!}#hq#P#hq#R#hq#T#hq#X#hq#Z#hq#^#hq#_#hq#a#hq#c#hq#l#hq#o#hq#s#hq#u#hq#z#hq#}#hq$P#hq%X#hq%o#hq%p#hq%t#hq%u#hq&Z#hq&[#hq&]#hq&^#hq&_#hq&`#hq&a#hq&b#hq&c#hq&d#hq&e#hq&f#hq&g#hq&h#hq&i#hq&j#hq%Z#hq%_#hq~On$|az$|a~P%SOV)kOn&wi~P'vOz+yOn&wi~Oz,TO{$jO#[,TO~O#q,VOP#nqV#nqf#nqh#nqo#nqs#nqv#nq!P#nq!Q#nq!T#nq!U#nq!X#nq!]#nq!h#nq!r#nq!s#nq!t#nq!{#nq!}#nq#P#nq#R#nq#T#nq#X#nq#Z#nq#^#nq#_#nq#a#nq#c#nq#l#nq#o#nq#s#nq#u#nq#z#nq#}#nq$P#nq%X#nq%o#nq%p#nq%t#nq%u#nq&Z#nq&[#nq&]#nq&^#nq&_#nq&`#nq&a#nq&b#nq&c#nq&d#nq&e#nq&f#nq&g#nq&h#nq&i#nq&j#nq%Z#nq%_#nq~O#[,WOz%Oa{%Oa~Oo0cOv0qO{&xi~P'vOz,YO{&xi~O{#{O%z,[Og&zXz&zX~O%uWOg&zXz&zX~Oz,`Og&yX~Og,bO~O%Y,eO~O!T%dO!U%cOg&Viz&Vi~OV$}Oa$}Oo0cOv0qOg&Ui~P'vO{,hOz$la!W$la~Oo0cOv0qO{,iOz$la!W$la~P'vOo0cOv0qO{*gO!W&Xi~P'vOz,lO!W&Xi~Oo0cOv0qOz,lO!W&Xi~P'vOz,lO{,oO!W&Xi~Og$hiz$hi!W$hi~P%SOV'|Oo0cOv0qO~P'vOn,qO~OV'|Og,rOo0cOv0qO~P'vOV'|Oo0cOv0qO!W&Sq~P'vOz$gi![$gi#g$gi%[$gi%_$gig$gi{$gi!m$gi%z$gi~P%SOV(]Oo0cOv0qO~P'vOa+QOo0cOv0qO![&Tq~P'vOz,sO![&Tq~O![,tO~OV(]Oo0cOv0qO![%}q#g%}q%[%}q%_%}qg%}q{%}q!m%}q%z%}q~P'vO{,uO~OV+UOo0cOv0qO{&li![&li!m&li%z&li~P'vOz,zO{&li![&li!m&li%z&li~O!]+YO&n+[O![!na~O{(kO![,}O~OV&OOo0cOv0qO#g%hi%[%hi%_%hi%z%hi~P'vOz-OO#g%hi%[%hi%_%hi%z%hi~O%uWO#g&rq%[&rq%_&rqg&rq~Oz-RO#g&rq%[&rq%_&rqg&rq~OV)`Oa)`O%uWO!W&ua~Oz-TO!W&ua~On$|iz$|i~P%SOV)kO~P'vOV)kOn&wq~P'vOt-XOP#myV#myf#myh#myo#mys#myv#my!P#my!Q#my!T#my!U#my!X#my!]#my!h#my!r#my!s#my!t#my!{#my!}#my#P#my#R#my#T#my#X#my#Z#my#^#my#_#my#a#my#c#my#l#my#o#my#s#my#u#my#z#my#}#my$P#my%X#my%o#my%p#my%t#my%u#my&Z#my&[#my&]#my&^#my&_#my&`#my&a#my&b#my&c#my&d#my&e#my&f#my&g#my&h#my&i#my&j#my%Z#my%_#my~O%Z-]O%_-]O~P`O#q-^OP#nyV#nyf#nyh#nyo#nys#nyv#ny!P#ny!Q#ny!T#ny!U#ny!X#ny!]#ny!h#ny!r#ny!s#ny!t#ny!{#ny!}#ny#P#ny#R#ny#T#ny#X#ny#Z#ny#^#ny#_#ny#a#ny#c#ny#l#ny#o#ny#s#ny#u#ny#z#ny#}#ny$P#ny%X#ny%o#ny%p#ny%t#ny%u#ny&Z#ny&[#ny&]#ny&^#ny&_#ny&`#ny&a#ny&b#ny&c#ny&d#ny&e#ny&f#ny&g#ny&h#ny&i#ny&j#ny%Z#ny%_#ny~Oz-aO{$jO#[-aO~Oo0cOv0qO{&xq~P'vOz-dO{&xq~O%z,[Og&zaz&za~O{#{Og&zaz&za~OV*SOa*TO%q*UO%uWOg&ya~Oz-hOg&ya~O$S-lO~OV$}Oa$}Oo0cOv0qO~P'vOo0cOv0qO{-mOz$li!W$li~P'vOo0cOv0qOz$li!W$li~P'vO{-mOz$li!W$li~Oo0cOv0qO{*gO~P'vOo0cOv0qO{*gO!W&Xq~P'vOz-pO!W&Xq~Oo0cOv0qOz-pO!W&Xq~P'vOs-sO!T%dO!U%cOg&Oq!W&Oq![&Oqz&Oq~P!/jOa+QOo0cOv0qO![&Ty~P'vOz$ji![$ji~P%SOa+QOo0cOv0qO~P'vOV+UOo0cOv0qO~P'vOV+UOo0cOv0qO{&lq![&lq!m&lq%z&lq~P'vO{(kO![-xO!m-yO%z-wO~OV&OOo0cOv0qO#g%hq%[%hq%_%hq%z%hq~P'vO%uWO#g&ry%[&ry%_&ryg&ry~OV)`Oa)`O%uWO!W&ui~Ot-}OP#m!RV#m!Rf#m!Rh#m!Ro#m!Rs#m!Rv#m!R!P#m!R!Q#m!R!T#m!R!U#m!R!X#m!R!]#m!R!h#m!R!r#m!R!s#m!R!t#m!R!{#m!R!}#m!R#P#m!R#R#m!R#T#m!R#X#m!R#Z#m!R#^#m!R#_#m!R#a#m!R#c#m!R#l#m!R#o#m!R#s#m!R#u#m!R#z#m!R#}#m!R$P#m!R%X#m!R%o#m!R%p#m!R%t#m!R%u#m!R&Z#m!R&[#m!R&]#m!R&^#m!R&_#m!R&`#m!R&a#m!R&b#m!R&c#m!R&d#m!R&e#m!R&f#m!R&g#m!R&h#m!R&i#m!R&j#m!R%Z#m!R%_#m!R~Oo0cOv0qO{&xy~P'vOV*SOa*TO%q*UO%uWOg&yi~O$S-lO%Z.VO%_.VO~OV.aOh._O!X.^O!].`O!h.YO!s.[O!t.[O%p.XO%uWO&Z]O&[]O&]]O&^]O&_]O&`]O&a]O&b]O~Oo0cOv0qOz$lq!W$lq~P'vO{.fOz$lq!W$lq~Oo0cOv0qO{*gO!W&Xy~P'vOz.gO!W&Xy~Oo0cOv.kO~P'vOs-sO!T%dO!U%cOg&Oy!W&Oy![&Oyz&Oy~P!/jO{(kO![.nO~O{(kO![.nO!m.oO~OV*SOa*TO%q*UO%uWO~Oh.tO!f.rOz$TX#[$TX%j$TXg$TX~Os$TX{$TX!W$TX![$TX~P$-bO%o.vO%p.vOs$UXz$UX{$UX#[$UX%j$UX!W$UXg$UX![$UX~O!h.xO~Oz.|O#[/OO%j.yOs&|X{&|X!W&|Xg&|X~Oa/RO~P$)zOh.tOs&}Xz&}X{&}X#[&}X%j&}X!W&}Xg&}X![&}X~Os/VO{$jO~Oo0cOv0qOz$ly!W$ly~P'vOo0cOv0qO{*gO!W&X!R~P'vOz/ZO!W&X!R~Og&RXs&RX!T&RX!U&RX!W&RX![&RXz&RX~P!/jOs-sO!T%dO!U%cOg&Qa!W&Qa![&Qaz&Qa~O{(kO![/^O~O!f.rOh$[as$[az$[a{$[a#[$[a%j$[a!W$[ag$[a![$[a~O!h/eO~O%o.vO%p.vOs$Uaz$Ua{$Ua#[$Ua%j$Ua!W$Uag$Ua![$Ua~O%j.yOs$Yaz$Ya{$Ya#[$Ya!W$Yag$Ya![$Ya~Os&|a{&|a!W&|ag&|a~P$)nOz/jOs&|a{&|a!W&|ag&|a~O!W/mO~Og/mO~O{/oO~O![/pO~Oo0cOv0qO{*gO!W&X!Z~P'vO{/sO~O%z/tO~P$-bOz/uO#[/OO%j.yOg'PX~Oz/uOg'PX~Og/wO~O!h/xO~O#[/OOs%Saz%Sa{%Sa%j%Sa!W%Sag%Sa![%Sa~O#[/OO%j.yOs%Waz%Wa{%Wa!W%Wag%Wa~Os&|i{&|i!W&|ig&|i~P$)nOz/zO#[/OO%j.yO!['Oa~Og'Pa~P$)nOz0SOg'Pa~Oa0UO!['Oi~P$)zOz0WO!['Oi~Oz0WO#[/OO%j.yO!['Oi~O#[/OO%j.yOg$biz$bi~O%z0ZO~P$-bO#[/OO%j.yOg%Vaz%Va~Og'Pi~P$)nO{0^O~Oa0UO!['Oq~P$)zOz0`O!['Oq~O#[/OO%j.yOz%Ui![%Ui~Oa0UO~P$)zOa0UO!['Oy~P$)zO#[/OO%j.yOg$ciz$ci~O#[/OO%j.yOz%Uq![%Uq~Oz+aO#g%ha%[%ha%_%ha%z%ha~P%SOV&OOo0cOv0qO~P'vOn0hO~Oo0hO~P'vO{0iO~Ot0jO~P!/jO&]&Z&j&h&i&g&f&d&e&c&b&`&a&_&^&[%u~",goto:"!=j'QPPPPPP'RP'Z*s+[+t,_,y-fP.SP'Z.r.r'ZPPP'Z2[PPPPPP2[5PPP5PP7b7k=sPP=v>h>kPP'Z'ZPP>zPP'Z'ZPP'Z'Z'Z'Z'Z?O?w'ZP?zP@QDXGuGyPG|HWH['ZPPPH_Hk'RP'R'RP'RP'RP'RP'RP'R'R'RP'RPP'RPP'RP'RPHqH}IVPI^IdPI^PI^I^PPPI^PKrPK{LVL]KrPI^LfPI^PLmLsPLwM]MzNeLwLwNkNxLwLwLwLw! ^! d! g! l! o! y!!P!!]!!o!!u!#P!#V!#s!#y!$P!$Z!$a!$g!$y!%T!%Z!%a!%k!%q!%w!%}!&T!&Z!&e!&k!&u!&{!'U!'[!'k!'s!'}!(UPPPPPPPPPPP!([!(_!(e!(n!(x!)TPPPPPPPPPPPP!-u!/Z!3^!6oPP!6w!7W!7a!8Y!8P!8c!8i!8l!8o!8r!8z!9jPPPPPPPPPPPPPPPPP!9m!9q!9wP!:]!:a!:m!:v!;S!;j!;m!;p!;v!;|!<S!<VP!<_!<h!=d!=g]eOn#g$j)t,P'}`OTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!i!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'm'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0r{!cQ#c#p$R$d$p%e%j%p%q&`'O'g(q(|)j*o*x+w,v0g}!dQ#c#p$R$d$p$u%e%j%p%q&`'O'g(q(|)j*o*x+w,v0g!P!eQ#c#p$R$d$p$u$v%e%j%p%q&`'O'g(q(|)j*o*x+w,v0g!R!fQ#c#p$R$d$p$u$v$w%e%j%p%q&`'O'g(q(|)j*o*x+w,v0g!T!gQ#c#p$R$d$p$u$v$w$x%e%j%p%q&`'O'g(q(|)j*o*x+w,v0g!V!hQ#c#p$R$d$p$u$v$w$x$y%e%j%p%q&`'O'g(q(|)j*o*x+w,v0g!Z!hQ!n#c#p$R$d$p$u$v$w$x$y$z%e%j%p%q&`'O'g(q(|)j*o*x+w,v0g'}TOTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!i!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'm'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0r&eVOYZ[dnprxy}!P!Q!U!i!k!o!p!q!s!t#[#d#g#y#{#}$Q$h$j$}%S%Z%^%`%g%l%n%w%|&Z&_&j&k&u&x'P'W'Z'l'm'p'r's'w(O(W(^(d(g(p(r(z)^)e)g)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+U+V+Y+a+d+k,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0n0r%oXOYZ[dnrxy}!P!Q!U!i!k#[#d#g#y#{#}$Q$h$j$}%S%^%`%g%l%n%w%|&Z&_&j&k&u&x'P'W'Z'l'm'p'r's'w(O(W(^(d(g(p(r(z)^)e)g)p)t)z*O*Y*d*g*h*k*q*t*v*y*z*}+U+V+Y+a+d+k,P,X,Y,],g,h,i,k,l,o,s,u,w,y,z-O-d-f-m-p.f.g/V/Z0i0j0kQ#vqQ/[.kR0o0q't`OTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0rh#jhz{$W$Z&l&q)S)X+f+g-RW#rq&].k0qQ$]|Q$a!OQ$n!VQ$o!WW$|!i'm*d,gS&[#s#tQ'S$iQ(s&UQ)U&nU)Y&s)Z+jW)a&w+m-T-{Q*Q']W*R'_,`-h.TQ+l)`S,_*S*TQ-Q+eQ-_,TQ-c,WQ.R-al.W-l.^._.a.z.|/R/j/o/t/y0U0Z0^Q/S.`Q/a.tQ/l/OU0P/u0S0[X0V/z0W0_0`R&Z#r!_!wYZ!P!Q!k%S%`%g'p'r's(O(W)g*g*h*k*q*t*v,h,i,k,l,o-m-p.f.g/ZR%^!vQ!{YQ%x#[Q&d#}Q&g$QR,{+YT.j-s/s!Y!jQ!n#c#p$R$d$p$u$v$w$x$y$z%e%j%p%q&`'O'g(q(|)j*o*x+w,v0gQ&X#kQ'c$oR*^'dR'l$|Q%V!mR/_.r'|_OTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!i!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'm'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0rS#a_#b!P.[-l.^._.`.a.t.z.|/R/j/o/t/u/y/z0S0U0W0Z0[0^0_0`'|_OTYZ[adnoprtxy}!P!Q!R!U!X!c!d!e!f!g!h!i!k!o!p!q!s!t!z#O#S#T#[#d#g#x#y#{#}$Q$e$g$h$j$q$}%S%Z%^%`%c%g%l%n%w%|&O&Z&_&h&j&k&u&x&|'P'W'Z'l'm'p'r's'w'|(O(S(W(](^(d(g(p(r(z(})^)e)g)k)l)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+Q+U+V+Y+a+c+d+k+x+y,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0l0n0rT#a_#bT#^^#_R(o%xa(l%x(n(o+`,{-y-z.oT+[(k+]R-z,{Q$PsQ+l)aQ,^*RR-e,_X#}s$O$P&fQ&y$aQ'a$nQ'd$oR)s'SQ)b&wV-S+m-T-{ZgOn$j)t,PXkOn)t,PQ$k!TQ&z$bQ&{$cQ'^$mQ'b$oQ)q'RQ)x'WQ){'XQ)|'YQ*Z'`S*]'c'dQ+s)gQ+u)hQ+v)iQ+z)oS+|)r*[Q,Q)vQ,R)wS,S)y)zQ,d*^Q-V+rQ-W+tQ-Y+{S-Z+},OQ-`,UQ-b,VQ-|-XQ.O-[Q.P-^Q.Q-_Q.p-}Q.q.RQ/W.dR/r/XWkOn)t,PR#mjQ'`$nS)r'S'aR,O)sQ,]*RR-f,^Q*['`Q+})rR-[,OZiOjn)t,PQ'f$pR*`'gT-j,e-ku.c-l.^._.a.t.z.|/R/j/o/t/u/y0S0U0Z0[0^t.c-l.^._.a.t.z.|/R/j/o/t/u/y0S0U0Z0[0^Q/S.`X0V/z0W0_0`!P.Z-l.^._.`.a.t.z.|/R/j/o/t/u/y/z0S0U0W0Z0[0^0_0`Q.w.YR/f.xg.z.].{/b/i/n/|0O0Q0]0a0bu.b-l.^._.a.t.z.|/R/j/o/t/u/y0S0U0Z0[0^X.u.W.b/a0PR/c.tV0R/u0S0[R/X.dQnOS#on,PR,P)tQ&^#uR(x&^S%m#R#wS(_%m(bT(b%p&`Q%a!yQ%h!}W(P%a%h(U(YQ(U%eR(Y%jQ&i$RR)O&iQ(e%qQ*{(`T+R(e*{Q'n%OR*e'nS'q%R%SY*i'q*j,m-q.hU*j'r's'tU,m*k*l*mS-q,n,oR.h-rQ#Y]R%t#YQ#_^R%y#_Q(h%vS+W(h+XR+X(iQ+](kR,|+]Q#b_R%{#bQ#ebQ%}#cW&Q#e%}({+bQ({&cR+b0gQ$OsS&e$O&fR&f$PQ&v$_R)_&vQ&V#jR(t&VQ&m$VS)T&m+hR+h)UQ$Z{R&p$ZQ&t$]R)[&tQ+n)bR-U+nQ#hfR&S#hQ)f&zR+q)fQ&}$dS)m&})nR)n'OQ'V$kR)u'VQ'[$lS*P'[,ZR,Z*QQ,a*VR-i,aWjOn)t,PR#ljQ-k,eR.U-kd.{.]/b/i/n/|0O0Q0]0a0bR/h.{U.s.W/a0PR/`.sQ/{/nS0X/{0YR0Y/|S/v/b/cR0T/vQ.}.]R/k.}R!ZPXmOn)t,PWlOn)t,PR'T$jYfOn$j)t,PR&R#g[sOn#g$j)t,PR&d#}&dQOYZ[dnprxy}!P!Q!U!i!k!o!p!q!s!t#[#d#g#y#{#}$Q$h$j$}%S%Z%^%`%g%l%n%w%|&Z&_&j&k&u&x'P'W'Z'l'm'p'r's'w(O(W(^(d(g(p(r(z)^)e)g)p)t)z*O*Y*d*g*h*k*q*r*t*v*y*z*}+U+V+Y+a+d+k,P,X,Y,],g,h,i,k,l,o,q,s,u,w,y,z-O-d-f-m-p-s.f.g/V/Z/s0c0d0e0f0h0i0j0k0n0rQ!nTQ#caQ#poU$Rt%c(SS$d!R$gQ$p!XQ$u!cQ$v!dQ$w!eQ$x!fQ$y!gQ$z!hQ%e!zQ%j#OQ%p#SQ%q#TQ&`#xQ'O$eQ'g$qQ(q&OU(|&h(}+cW)j&|)l+x+yQ*o'|Q*x(]Q+w)kQ,v+QR0g0lQ!yYQ!}ZQ$b!PQ$c!QQ%R!kQ't%S^'{%`%g(O(W*q*t*v^*f'p*h,k,l-p.g/ZQ*l'rQ*m'sQ+t)gQ,j*gQ,n*kQ-n,hQ-o,iQ-r,oQ.e-mR/Y.f[bOn#g$j)t,P!^!vYZ!P!Q!k%S%`%g'p'r's(O(W)g*g*h*k*q*t*v,h,i,k,l,o-m-p.f.g/ZQ#R[Q#fdS#wrxQ$UyW$_}$Q'P)pS$l!U$hW${!i'm*d,gS%v#[+Y`&P#d%|(p(r(z+a-O0kQ&a#yQ&b#{Q&c#}Q'j$}Q'z%^W([%l(^*y*}Q(`%nQ(i%wQ(v&ZS(y&_0iQ)P&jQ)Q&kU)]&u)^+kQ)d&xQ)y'WY)}'Z*O,X,Y-dQ*b'lS*n'w0jW+P(d*z,s,wW+T(g+V,y,zQ+p)eQ,U)zQ,c*YQ,x+UQ-P+dQ-e,]Q-v,uQ.S-fR/q/VhUOn#d#g$j%|&_'w(p(r)t,P%U!uYZ[drxy}!P!Q!U!i!k#[#y#{#}$Q$h$}%S%^%`%g%l%n%w&Z&j&k&u&x'P'W'Z'l'm'p'r's(O(W(^(d(g(z)^)e)g)p)z*O*Y*d*g*h*k*q*t*v*y*z*}+U+V+Y+a+d+k,X,Y,],g,h,i,k,l,o,s,u,w,y,z-O-d-f-m-p.f.g/V/Z0i0j0kQ#qpW%W!o!s0d0nQ%X!pQ%Y!qQ%[!tQ%f0cS'v%Z0hQ'x0eQ'y0fQ,p*rQ-u,qS.i-s/sR0p0rU#uq.k0qR(w&][cOn#g$j)t,PZ!xY#[#}$Q+YQ#W[Q#zrR$TxQ%b!yQ%i!}Q%o#RQ'j${Q(V%eQ(Z%jQ(c%pQ(f%qQ*|(`Q,f*bQ-t,pQ.m-uR/].lQ$StQ(R%cR*s(SQ.l-sR/}/sR#QZR#V[R%Q!iQ%O!iV*c'm*d,g!Z!lQ!n#c#p$R$d$p$u$v$w$x$y$z%e%j%p%q&`'O'g(q(|)j*o*x+w,v0gR%T!kT#]^#_Q%x#[R,{+YQ(m%xS+_(n(oQ,}+`Q-x,{S.n-y-zR/^.oT+Z(k+]Q$`}Q&g$QQ)o'PR+{)pQ$XzQ)W&qR+i)XQ$XzQ&o$WQ)W&qR+i)XQ#khW$Vz$W&q)XQ$[{Q&r$ZZ)R&l)S+f+g-RR$^|R)c&wXlOn)t,PQ$f!RR'Q$gQ$m!UR'R$hR*X'_Q*V'_V-g,`-h.TQ.d-lQ/P.^R/Q._U.]-l.^._Q/U.aQ/b.tQ/g.zU/i.|/j/yQ/n/RQ/|/oQ0O/tU0Q/u0S0[Q0]0UQ0a0ZR0b0^R/T.`R/d.t",nodeNames:"⚠ print Escape { Comment Script AssignStatement * BinaryExpression BitOp BitOp BitOp BitOp ArithOp ArithOp @ ArithOp ** UnaryExpression ArithOp BitOp AwaitExpression await ) ( ParenthesizedExpression BinaryExpression or and CompareOp in not is UnaryExpression ConditionalExpression if else LambdaExpression lambda ParamList VariableName AssignOp , : NamedExpression AssignOp YieldExpression yield from TupleExpression ComprehensionExpression async for LambdaExpression ] [ ArrayExpression ArrayComprehensionExpression } { DictionaryExpression DictionaryComprehensionExpression SetExpression SetComprehensionExpression CallExpression ArgList AssignOp MemberExpression . PropertyName Number String FormatString FormatReplacement FormatSelfDoc FormatConversion FormatSpec FormatReplacement FormatSelfDoc ContinuedString Ellipsis None Boolean TypeDef AssignOp UpdateStatement UpdateOp ExpressionStatement DeleteStatement del PassStatement pass BreakStatement break ContinueStatement continue ReturnStatement return YieldStatement PrintStatement RaiseStatement raise ImportStatement import as ScopeStatement global nonlocal AssertStatement assert TypeDefinition type TypeParamList TypeParam StatementGroup ; IfStatement Body elif WhileStatement while ForStatement TryStatement try except finally WithStatement with FunctionDefinition def ParamList AssignOp TypeDef ClassDefinition class DecoratedStatement Decorator At MatchStatement match MatchBody MatchClause case CapturePattern LiteralPattern ArithOp ArithOp AsPattern OrPattern LogicOp AttributePattern SequencePattern MappingPattern StarPattern ClassPattern PatternArgList KeywordPattern KeywordPattern Guard",maxTerm:277,context:KT,nodeProps:[["isolate",-5,4,71,72,73,77,""],["group",-15,6,85,87,88,90,92,94,96,98,99,100,102,105,108,110,"Statement Statement",-22,8,18,21,25,40,49,50,56,57,60,61,62,63,64,67,70,71,72,79,80,81,82,"Expression",-10,114,116,119,121,122,126,128,133,135,138,"Statement",-9,143,144,147,148,150,151,152,153,154,"Pattern"],["openedBy",23,"(",54,"[",58,"{"],["closedBy",24,")",55,"]",59,"}"]],propSources:[t2],skippedNodes:[0,4],repeatNodeCount:34,tokenData:"!2|~R!`OX%TXY%oY[%T[]%o]p%Tpq%oqr'ars)Yst*xtu%Tuv,dvw-hwx.Uxy/tyz0[z{0r{|2S|}2p}!O3W!O!P4_!P!Q:Z!Q!R;k!R![>_![!]Do!]!^Es!^!_FZ!_!`Gk!`!aHX!a!b%T!b!cIf!c!dJU!d!eK^!e!hJU!h!i!#f!i!tJU!t!u!,|!u!wJU!w!x!.t!x!}JU!}#O!0S#O#P&o#P#Q!0j#Q#R!1Q#R#SJU#S#T%T#T#UJU#U#VK^#V#YJU#Y#Z!#f#Z#fJU#f#g!,|#g#iJU#i#j!.t#j#oJU#o#p!1n#p#q!1s#q#r!2a#r#s!2f#s$g%T$g;'SJU;'S;=`KW<%lOJU`%YT&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%T`%lP;=`<%l%To%v]&n`%c_OX%TXY%oY[%T[]%o]p%Tpq%oq#O%T#O#P&o#P#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To&tX&n`OY%TYZ%oZ]%T]^%o^#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc'f[&n`O!_%T!_!`([!`#T%T#T#U(r#U#f%T#f#g(r#g#h(r#h#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc(cTmR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc(yT!mR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk)aV&n`&[ZOr%Trs)vs#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk){V&n`Or%Trs*bs#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk*iT&n`&^ZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To+PZS_&n`OY*xYZ%TZ]*x]^%T^#o*x#o#p+r#p#q*x#q#r+r#r;'S*x;'S;=`,^<%lO*x_+wTS_OY+rZ]+r^;'S+r;'S;=`,W<%lO+r_,ZP;=`<%l+ro,aP;=`<%l*xj,kV%rQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tj-XT!xY&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tj-oV%lQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk.]V&n`&ZZOw%Twx.rx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk.wV&n`Ow%Twx/^x#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk/eT&n`&]ZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk/{ThZ&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc0cTgR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk0yXVZ&n`Oz%Tz{1f{!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk1mVaR&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk2ZV%oZ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc2wTzR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To3_W%pZ&n`O!_%T!_!`-Q!`!a3w!a#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Td4OT&{S&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk4fX!fQ&n`O!O%T!O!P5R!P!Q%T!Q![6T![#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk5WV&n`O!O%T!O!P5m!P#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk5tT!rZ&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti6[a!hX&n`O!Q%T!Q![6T![!g%T!g!h7a!h!l%T!l!m9s!m#R%T#R#S6T#S#X%T#X#Y7a#Y#^%T#^#_9s#_#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti7fZ&n`O{%T{|8X|}%T}!O8X!O!Q%T!Q![8s![#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti8^V&n`O!Q%T!Q![8s![#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti8z]!hX&n`O!Q%T!Q![8s![!l%T!l!m9s!m#R%T#R#S8s#S#^%T#^#_9s#_#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti9zT!hX&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk:bX%qR&n`O!P%T!P!Q:}!Q!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tj;UV%sQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti;ro!hX&n`O!O%T!O!P=s!P!Q%T!Q![>_![!d%T!d!e?q!e!g%T!g!h7a!h!l%T!l!m9s!m!q%T!q!rA]!r!z%T!z!{Bq!{#R%T#R#S>_#S#U%T#U#V?q#V#X%T#X#Y7a#Y#^%T#^#_9s#_#c%T#c#dA]#d#l%T#l#mBq#m#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti=xV&n`O!Q%T!Q![6T![#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti>fc!hX&n`O!O%T!O!P=s!P!Q%T!Q![>_![!g%T!g!h7a!h!l%T!l!m9s!m#R%T#R#S>_#S#X%T#X#Y7a#Y#^%T#^#_9s#_#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti?vY&n`O!Q%T!Q!R@f!R!S@f!S#R%T#R#S@f#S#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Ti@mY!hX&n`O!Q%T!Q!R@f!R!S@f!S#R%T#R#S@f#S#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TiAbX&n`O!Q%T!Q!YA}!Y#R%T#R#SA}#S#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TiBUX!hX&n`O!Q%T!Q!YA}!Y#R%T#R#SA}#S#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TiBv]&n`O!Q%T!Q![Co![!c%T!c!iCo!i#R%T#R#SCo#S#T%T#T#ZCo#Z#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TiCv]!hX&n`O!Q%T!Q![Co![!c%T!c!iCo!i#R%T#R#SCo#S#T%T#T#ZCo#Z#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%ToDvV{_&n`O!_%T!_!`E]!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TcEdT%{R&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkEzT#gZ&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkFbXmR&n`O!^%T!^!_F}!_!`([!`!a([!a#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TjGUV%mQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkGrV%zZ&n`O!_%T!_!`([!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkH`WmR&n`O!_%T!_!`([!`!aHx!a#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TjIPV%nQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkIoV_Q#}P&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%ToJ_]&n`&YS%uZO!Q%T!Q![JU![!c%T!c!}JU!}#R%T#R#SJU#S#T%T#T#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUoKZP;=`<%lJUoKge&n`&YS%uZOr%Trs)Ysw%Twx.Ux!Q%T!Q![JU![!c%T!c!tJU!t!uLx!u!}JU!}#R%T#R#SJU#S#T%T#T#fJU#f#gLx#g#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUoMRa&n`&YS%uZOr%TrsNWsw%Twx! vx!Q%T!Q![JU![!c%T!c!}JU!}#R%T#R#SJU#S#T%T#T#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUkN_V&n`&`ZOr%TrsNts#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%TkNyV&n`Or%Trs! `s#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk! gT&n`&bZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk! }V&n`&_ZOw%Twx!!dx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!!iV&n`Ow%Twx!#Ox#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!#VT&n`&aZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To!#oe&n`&YS%uZOr%Trs!%Qsw%Twx!&px!Q%T!Q![JU![!c%T!c!tJU!t!u!(`!u!}JU!}#R%T#R#SJU#S#T%T#T#fJU#f#g!(`#g#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUk!%XV&n`&dZOr%Trs!%ns#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!%sV&n`Or%Trs!&Ys#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!&aT&n`&fZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!&wV&n`&cZOw%Twx!'^x#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!'cV&n`Ow%Twx!'xx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!(PT&n`&eZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To!(ia&n`&YS%uZOr%Trs!)nsw%Twx!+^x!Q%T!Q![JU![!c%T!c!}JU!}#R%T#R#SJU#S#T%T#T#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUk!)uV&n`&hZOr%Trs!*[s#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!*aV&n`Or%Trs!*vs#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!*}T&n`&jZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!+eV&n`&gZOw%Twx!+zx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!,PV&n`Ow%Twx!,fx#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tk!,mT&n`&iZO#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%To!-Vi&n`&YS%uZOr%TrsNWsw%Twx! vx!Q%T!Q![JU![!c%T!c!dJU!d!eLx!e!hJU!h!i!(`!i!}JU!}#R%T#R#SJU#S#T%T#T#UJU#U#VLx#V#YJU#Y#Z!(`#Z#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUo!.}a&n`&YS%uZOr%Trs)Ysw%Twx.Ux!Q%T!Q![JU![!c%T!c!}JU!}#R%T#R#SJU#S#T%T#T#oJU#p#q%T#r$g%T$g;'SJU;'S;=`KW<%lOJUk!0ZT!XZ&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tc!0qT!WR&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%Tj!1XV%kQ&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%T~!1sO!]~k!1zV%jR&n`O!_%T!_!`-Q!`#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%T~!2fO![~i!2mT%tX&n`O#o%T#p#q%T#r;'S%T;'S;=`%i<%lO%T",tokenizers:[JT,WT,$T,FT,0,1,2,3,4],topRules:{Script:[0,5]},specialized:[{term:221,get:r=>e2[r]||-1}],tokenPrec:7668});var Rf;const pa=new Rt;function n2(r){return ut.define({combine:r?t=>t.concat(r):void 0})}const l2=new Rt;class xi{constructor(t,i,l=[],a=""){this.data=t,this.name=a,At.prototype.hasOwnProperty("tree")||Object.defineProperty(At.prototype,"tree",{get(){return Ra(this)}}),this.parser=i,this.extension=[rs.of(this),At.languageData.of((o,h,c)=>{let d=ZO(o,h,c),p=d.type.prop(pa);if(!p)return[];let m=o.facet(p),O=d.type.prop(l2);if(O){let S=d.resolve(h-d.from,c);for(let b of O)if(b.test(S,o)){let T=o.facet(b.facet);return b.type=="replace"?T:T.concat(m)}}return m})].concat(l)}isActiveAt(t,i,l=-1){return ZO(t,i,l).type.prop(pa)==this.data}findRegions(t){let i=t.facet(rs);if((i==null?void 0:i.data)==this.data)return[{from:0,to:t.doc.length}];if(!i||!i.allowsNesting)return[];let l=[],a=(o,h)=>{if(o.prop(pa)==this.data){l.push({from:h,to:h+o.length});return}let c=o.prop(Rt.mounted);if(c){if(c.tree.prop(pa)==this.data){if(c.overlay)for(let d of c.overlay)l.push({from:d.from+h,to:d.to+h});else l.push({from:h,to:h+o.length});return}else if(c.overlay){let d=l.length;if(a(c.tree,c.overlay[0].from+h),l.length>d)return}}for(let d=0;d<o.children.length;d++){let p=o.children[d];p instanceof It&&a(p,o.positions[d]+h)}};return a(Ra(t),0),l}get allowsNesting(){return!0}}xi.setState=$t.define();function ZO(r,t,i){let l=r.facet(rs),a=Ra(r).topNode;if(!l||l.allowsNesting)for(let o=a;o;o=o.enter(t,i,ie.ExcludeBuffers))o.type.isTop&&(a=o);return a}class No extends xi{constructor(t,i,l){super(t,i,[],l),this.parser=i}static define(t){let i=n2(t.languageData);return new No(i,t.parser.configure({props:[pa.add(l=>l.isTop?i:void 0)]}),t.name)}configure(t,i){return new No(this.data,this.parser.configure(t),i||this.name)}get allowsNesting(){return this.parser.hasWrappers()}}function Ra(r){let t=r.field(xi.state,!1);return t?t.tree:It.empty}class s2{constructor(t){this.doc=t,this.cursorPos=0,this.string="",this.cursor=t.iter()}get length(){return this.doc.length}syncTo(t){return this.string=this.cursor.next(t-this.cursorPos).value,this.cursorPos=t+this.string.length,this.cursorPos-this.string.length}chunk(t){return this.syncTo(t),this.string}get lineChunks(){return!0}read(t,i){let l=this.cursorPos-this.string.length;return t<l||i>=this.cursorPos?this.doc.sliceString(t,i):this.string.slice(t-l,i-l)}}let fa=null;class Uo{constructor(t,i,l=[],a,o,h,c,d){this.parser=t,this.state=i,this.fragments=l,this.tree=a,this.treeLen=o,this.viewport=h,this.skipped=c,this.scheduleOn=d,this.parse=null,this.tempSkipped=[]}static create(t,i,l){return new Uo(t,i,[],It.empty,0,l,[],null)}startParse(){return this.parser.startParse(new s2(this.state.doc),this.fragments)}work(t,i){return i!=null&&i>=this.state.doc.length&&(i=void 0),this.tree!=It.empty&&this.isDone(i??this.state.doc.length)?(this.takeTree(),!0):this.withContext(()=>{var l;if(typeof t=="number"){let a=Date.now()+t;t=()=>Date.now()>a}for(this.parse||(this.parse=this.startParse()),i!=null&&(this.parse.stoppedAt==null||this.parse.stoppedAt>i)&&i<this.state.doc.length&&this.parse.stopAt(i);;){let a=this.parse.advance();if(a)if(this.fragments=this.withoutTempSkipped(el.addTree(a,this.fragments,this.parse.stoppedAt!=null)),this.treeLen=(l=this.parse.stoppedAt)!==null&&l!==void 0?l:this.state.doc.length,this.tree=a,this.parse=null,this.treeLen<(i??this.state.doc.length))this.parse=this.startParse();else return!0;if(t())return!1}})}takeTree(){let t,i;this.parse&&(t=this.parse.parsedPos)>=this.treeLen&&((this.parse.stoppedAt==null||this.parse.stoppedAt>t)&&this.parse.stopAt(t),this.withContext(()=>{for(;!(i=this.parse.advance()););}),this.treeLen=t,this.tree=i,this.fragments=this.withoutTempSkipped(el.addTree(this.tree,this.fragments,!0)),this.parse=null)}withContext(t){let i=fa;fa=this;try{return t()}finally{fa=i}}withoutTempSkipped(t){for(let i;i=this.tempSkipped.pop();)t=KO(t,i.from,i.to);return t}changes(t,i){let{fragments:l,tree:a,treeLen:o,viewport:h,skipped:c}=this;if(this.takeTree(),!t.empty){let d=[];if(t.iterChangedRanges((p,m,O,S)=>d.push({fromA:p,toA:m,fromB:O,toB:S})),l=el.applyChanges(l,d),a=It.empty,o=0,h={from:t.mapPos(h.from,-1),to:t.mapPos(h.to,1)},this.skipped.length){c=[];for(let p of this.skipped){let m=t.mapPos(p.from,1),O=t.mapPos(p.to,-1);m<O&&c.push({from:m,to:O})}}}return new Uo(this.parser,i,l,a,o,h,c,this.scheduleOn)}updateViewport(t){if(this.viewport.from==t.from&&this.viewport.to==t.to)return!1;this.viewport=t;let i=this.skipped.length;for(let l=0;l<this.skipped.length;l++){let{from:a,to:o}=this.skipped[l];a<t.to&&o>t.from&&(this.fragments=KO(this.fragments,a,o),this.skipped.splice(l--,1))}return this.skipped.length>=i?!1:(this.reset(),!0)}reset(){this.parse&&(this.takeTree(),this.parse=null)}skipUntilInView(t,i){this.skipped.push({from:t,to:i})}static getSkippingParser(t){return new class extends T1{createParse(i,l,a){let o=a[0].from,h=a[a.length-1].to;return{parsedPos:o,advance(){let d=fa;if(d){for(let p of a)d.tempSkipped.push(p);t&&(d.scheduleOn=d.scheduleOn?Promise.all([d.scheduleOn,t]):t)}return this.parsedPos=h,new It(Ce.none,[],[],h-o)},stoppedAt:null,stopAt(){}}}}}isDone(t){t=Math.min(t,this.state.doc.length);let i=this.fragments;return this.treeLen>=t&&i.length&&i[0].from==0&&i[0].to>=t}static get(){return fa}}function KO(r,t,i){return el.applyChanges(r,[{fromA:t,toA:i,fromB:t,toB:i}])}class as{constructor(t){this.context=t,this.tree=t.tree}apply(t){if(!t.docChanged&&this.tree==this.context.tree)return this;let i=this.context.changes(t.changes,t.state),l=this.context.treeLen==t.startState.doc.length?void 0:Math.max(t.changes.mapPos(this.context.treeLen),i.viewport.to);return i.work(20,l)||i.takeTree(),new as(i)}static init(t){let i=Math.min(3e3,t.doc.length),l=Uo.create(t.facet(rs).parser,t,{from:0,to:i});return l.work(20,i)||l.takeTree(),new as(l)}}xi.state=Cn.define({create:as.init,update(r,t){for(let i of t.effects)if(i.is(xi.setState))return i.value;return t.startState.facet(rs)!=t.state.facet(rs)?as.init(t.state):r.apply(t)}});let C1=r=>{let t=setTimeout(()=>r(),500);return()=>clearTimeout(t)};typeof requestIdleCallback<"u"&&(C1=r=>{let t=-1,i=setTimeout(()=>{t=requestIdleCallback(r,{timeout:400})},100);return()=>t<0?clearTimeout(i):cancelIdleCallback(t)});const Ef=typeof navigator<"u"&&(!((Rf=navigator.scheduling)===null||Rf===void 0)&&Rf.isInputPending)?()=>navigator.scheduling.isInputPending():null,a2=ns.fromClass(class{constructor(t){this.view=t,this.working=null,this.workScheduled=0,this.chunkEnd=-1,this.chunkBudget=-1,this.work=this.work.bind(this),this.scheduleWork()}update(t){let i=this.view.state.field(xi.state).context;(i.updateViewport(t.view.viewport)||this.view.viewport.to>i.treeLen)&&this.scheduleWork(),(t.docChanged||t.selectionSet)&&(this.view.hasFocus&&(this.chunkBudget+=50),this.scheduleWork()),this.checkAsyncSchedule(i)}scheduleWork(){if(this.working)return;let{state:t}=this.view,i=t.field(xi.state);(i.tree!=i.context.tree||!i.context.isDone(t.doc.length))&&(this.working=C1(this.work))}work(t){this.working=null;let i=Date.now();if(this.chunkEnd<i&&(this.chunkEnd<0||this.view.hasFocus)&&(this.chunkEnd=i+3e4,this.chunkBudget=3e3),this.chunkBudget<=0)return;let{state:l,viewport:{to:a}}=this.view,o=l.field(xi.state);if(o.tree==o.context.tree&&o.context.isDone(a+1e5))return;let h=Date.now()+Math.min(this.chunkBudget,100,t&&!Ef?Math.max(25,t.timeRemaining()-5):1e9),c=o.context.treeLen<a&&l.doc.length>a+1e3,d=o.context.work(()=>Ef&&Ef()||Date.now()>h,a+(c?0:1e5));this.chunkBudget-=Date.now()-i,(d||this.chunkBudget<=0)&&(o.context.takeTree(),this.view.dispatch({effects:xi.setState.of(new as(o.context))})),this.chunkBudget>0&&!(d&&!c)&&this.scheduleWork(),this.checkAsyncSchedule(o.context)}checkAsyncSchedule(t){t.scheduleOn&&(this.workScheduled++,t.scheduleOn.then(()=>this.scheduleWork()).catch(i=>vi(this.view.state,i)).then(()=>this.workScheduled--),t.scheduleOn=null)}destroy(){this.working&&this.working()}isWorking(){return!!(this.working||this.workScheduled>0)}},{eventHandlers:{focus(){this.scheduleWork()}}}),rs=ut.define({combine(r){return r.length?r[0]:null},enables:r=>[xi.state,a2,yt.contentAttributes.compute([r],t=>{let i=t.facet(r);return i&&i.name?{"data-language":i.name}:{}})]});class r2{constructor(t,i=[]){this.language=t,this.support=i,this.extension=[t,i]}}const o2=ut.define({combine:r=>{if(!r.length)return"  ";let t=r[0];if(!t||/\S/.test(t)||Array.from(t).some(i=>i!=t[0]))throw new Error("Invalid indent unit: "+JSON.stringify(r[0]));return t}}),u2=new Rt;function h2(r){let t=r.node,i=t.childAfter(t.from),l=t.lastChild;if(!i)return null;let a=r.options.simulateBreak,o=r.state.doc.lineAt(i.from),h=a==null||a<=o.from?o.to:Math.min(o.to,a);for(let c=i.to;;){let d=t.childAfter(c);if(!d||d==l)return null;if(!d.type.isSkipped){if(d.from>=h)return null;let p=/^ */.exec(o.text.slice(i.to-o.from))[0].length;return{from:i.from,to:i.to+p}}c=d.to}}function zf({closing:r,align:t=!0,units:i=1}){return l=>f2(l,t,i,r)}function f2(r,t,i,l,a){let o=r.textAfter,h=o.match(/^\s*/)[0].length,c=l&&o.slice(h,h+l.length)==l||a==r.pos+h,d=t?h2(r):null;return d?c?r.column(d.from):r.column(d.to):r.baseIndent+(c?0:r.unit*i)}const c2=new Rt;function d2(r){let t=r.firstChild,i=r.lastChild;return t&&t.to<i.from?{from:t.to,to:i.type.isError?r.to:i.from}:null}const g2=Object.create(null),JO=[Ce.none],FO=[],IO=Object.create(null),p2=Object.create(null);for(let[r,t]of[["variable","variableName"],["variable-2","variableName.special"],["string-2","string.special"],["def","variableName.definition"],["tag","tagName"],["attribute","attributeName"],["type","typeName"],["builtin","variableName.standard"],["qualifier","modifier"],["error","invalid"],["header","heading"],["property","propertyName"]])p2[r]=m2(g2,t);function Mf(r,t){FO.indexOf(r)>-1||(FO.push(r),console.warn(t))}function m2(r,t){let i=[];for(let c of t.split(" ")){let d=[];for(let p of c.split(".")){let m=r[p]||G[p];m?typeof m=="function"?d.length?d=d.map(m):Mf(p,`Modifier ${p} used at start of tag`):d.length?Mf(p,`Tag ${p} used as modifier`):d=Array.isArray(m)?m:[m]:Mf(p,`Unknown highlighting tag ${p}`)}for(let p of d)i.push(p)}if(!i.length)return 0;let l=t.replace(/ /g,"_"),a=l+" "+i.map(c=>c.id),o=IO[a];if(o)return o.id;let h=IO[a]=Ce.define({id:JO.length,name:l,props:[w1({[l]:i})]});return JO.push(h),h.id}pe.RTL,pe.LTR;function t0(r){let t=Object.keys(r).join(""),i=/\w/.test(t);return i&&(t=t.replace(/\w/g,"")),`[${i?"\\w":""}${t.replace(/[^\w\s]/g,"\\$&")}]`}function O2(r){let t=Object.create(null),i=Object.create(null);for(let{label:a}of r){t[a[0]]=!0;for(let o=1;o<a.length;o++)i[a[o]]=!0}let l=t0(t)+t0(i)+"*$";return[new RegExp("^"+l),new RegExp(l)]}function y2(r){let t=r.map(a=>typeof a=="string"?{label:a}:a),[i,l]=t.every(a=>/^\w+$/.test(a.label))?[/\w*$/,/\w+$/]:O2(t);return a=>{let o=a.matchBefore(l);return o||a.explicit?{from:o?o.from:a.pos,options:t,validFor:i}:null}}function S2(r,t){return i=>{for(let l=Ra(i.state).resolveInner(i.pos,-1);l;l=l.parent){if(r.indexOf(l.name)>-1)return null;if(l.type.isTop)break}return t(i)}}const v2=rl.define(),b2=yt.baseTheme({".cm-tooltip.cm-tooltip-autocomplete":{"& > ul":{fontFamily:"monospace",whiteSpace:"nowrap",overflow:"hidden auto",maxWidth_fallback:"700px",maxWidth:"min(700px, 95vw)",minWidth:"250px",maxHeight:"10em",height:"100%",listStyle:"none",margin:0,padding:0,"& > li, & > completion-section":{padding:"1px 3px",lineHeight:1.2},"& > li":{overflowX:"hidden",textOverflow:"ellipsis",cursor:"pointer"},"& > completion-section":{display:"list-item",borderBottom:"1px solid silver",paddingLeft:"0.5em",opacity:.7}}},"&light .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#17c",color:"white"},"&light .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#777"},"&dark .cm-tooltip-autocomplete ul li[aria-selected]":{background:"#347",color:"white"},"&dark .cm-tooltip-autocomplete-disabled ul li[aria-selected]":{background:"#444"},".cm-completionListIncompleteTop:before, .cm-completionListIncompleteBottom:after":{content:'"···"',opacity:.5,display:"block",textAlign:"center"},".cm-tooltip.cm-completionInfo":{position:"absolute",padding:"3px 9px",width:"max-content",maxWidth:"400px",boxSizing:"border-box",whiteSpace:"pre-line"},".cm-completionInfo.cm-completionInfo-left":{right:"100%"},".cm-completionInfo.cm-completionInfo-right":{left:"100%"},".cm-completionInfo.cm-completionInfo-left-narrow":{right:"30px"},".cm-completionInfo.cm-completionInfo-right-narrow":{left:"30px"},"&light .cm-snippetField":{backgroundColor:"#00000022"},"&dark .cm-snippetField":{backgroundColor:"#ffffff22"},".cm-snippetFieldPosition":{verticalAlign:"text-top",width:0,height:"1.15em",display:"inline-block",margin:"0 -0.7px -.7em",borderLeft:"1.4px dotted #888"},".cm-completionMatchedText":{textDecoration:"underline"},".cm-completionDetail":{marginLeft:"0.5em",fontStyle:"italic"},".cm-completionIcon":{fontSize:"90%",width:".8em",display:"inline-block",textAlign:"center",paddingRight:".6em",opacity:"0.6",boxSizing:"content-box"},".cm-completionIcon-function, .cm-completionIcon-method":{"&:after":{content:"'ƒ'"}},".cm-completionIcon-class":{"&:after":{content:"'○'"}},".cm-completionIcon-interface":{"&:after":{content:"'◌'"}},".cm-completionIcon-variable":{"&:after":{content:"'𝑥'"}},".cm-completionIcon-constant":{"&:after":{content:"'𝐶'"}},".cm-completionIcon-type":{"&:after":{content:"'𝑡'"}},".cm-completionIcon-enum":{"&:after":{content:"'∪'"}},".cm-completionIcon-property":{"&:after":{content:"'□'"}},".cm-completionIcon-keyword":{"&:after":{content:"'🔑︎'"}},".cm-completionIcon-namespace":{"&:after":{content:"'▢'"}},".cm-completionIcon-text":{"&:after":{content:"'abc'",fontSize:"50%",verticalAlign:"middle"}}});class x2{constructor(t,i,l,a){this.field=t,this.line=i,this.from=l,this.to=a}}class Uc{constructor(t,i,l){this.field=t,this.from=i,this.to=l}map(t){let i=t.mapPos(this.from,-1,Te.TrackDel),l=t.mapPos(this.to,1,Te.TrackDel);return i==null||l==null?null:new Uc(this.field,i,l)}}class Bc{constructor(t,i){this.lines=t,this.fieldPositions=i}instantiate(t,i){let l=[],a=[i],o=t.doc.lineAt(i),h=/^\s*/.exec(o.text)[0];for(let d of this.lines){if(l.length){let p=h,m=/^\t*/.exec(d)[0].length;for(let O=0;O<m;O++)p+=t.facet(o2);a.push(i+p.length-m),d=p+d.slice(m)}l.push(d),i+=d.length+1}let c=this.fieldPositions.map(d=>new Uc(d.field,a[d.line]+d.from,a[d.line]+d.to));return{text:l,ranges:c}}static parse(t){let i=[],l=[],a=[],o;for(let h of t.split(/\r\n?|\n/)){for(;o=/[#$]\{(?:(\d+)(?::([^}]*))?|((?:\\[{}]|[^}])*))\}/.exec(h);){let c=o[1]?+o[1]:null,d=o[2]||o[3]||"",p=-1,m=d.replace(/\\[{}]/g,O=>O[1]);for(let O=0;O<i.length;O++)(c!=null?i[O].seq==c:m&&i[O].name==m)&&(p=O);if(p<0){let O=0;for(;O<i.length&&(c==null||i[O].seq!=null&&i[O].seq<c);)O++;i.splice(O,0,{seq:c,name:m}),p=O;for(let S of a)S.field>=p&&S.field++}a.push(new x2(p,l.length,o.index,o.index+m.length)),h=h.slice(0,o.index)+d+h.slice(o.index+o[0].length)}h=h.replace(/\\([{}])/g,(c,d,p)=>{for(let m of a)m.line==l.length&&m.from>p&&(m.from--,m.to--);return d}),l.push(h)}return new Bc(l,a)}}let T2=ne.widget({widget:new class extends Vo{toDOM(){let r=document.createElement("span");return r.className="cm-snippetFieldPosition",r}ignoreEvent(){return!1}}}),w2=ne.mark({class:"cm-snippetField"});class os{constructor(t,i){this.ranges=t,this.active=i,this.deco=ne.set(t.map(l=>(l.from==l.to?T2:w2).range(l.from,l.to)))}map(t){let i=[];for(let l of this.ranges){let a=l.map(t);if(!a)return null;i.push(a)}return new os(i,this.active)}selectionInsideField(t){return t.ranges.every(i=>this.ranges.some(l=>l.field==this.active&&l.from<=i.from&&l.to>=i.to))}}const Ca=$t.define({map(r,t){return r&&r.map(t)}}),Q2=$t.define(),Ea=Cn.define({create(){return null},update(r,t){for(let i of t.effects){if(i.is(Ca))return i.value;if(i.is(Q2)&&r)return new os(r.ranges,i.value)}return r&&t.docChanged&&(r=r.map(t.changes)),r&&t.selection&&!r.selectionInsideField(t.selection)&&(r=null),r},provide:r=>yt.decorations.from(r,t=>t?t.deco:ne.none)});function Xc(r,t){return I.create(r.filter(i=>i.field==t).map(i=>I.range(i.from,i.to)))}function k2(r){let t=Bc.parse(r);return(i,l,a,o)=>{let{text:h,ranges:c}=t.instantiate(i.state,a),{main:d}=i.state.selection,p={changes:{from:a,to:o==d.from?d.to:o,insert:wt.of(h)},scrollIntoView:!0,annotations:l?[v2.of(l),ge.userEvent.of("input.complete")]:void 0};if(c.length&&(p.selection=Xc(c,0)),c.some(m=>m.field>0)){let m=new os(c,0),O=p.effects=[Ca.of(m)];i.state.field(Ea,!1)===void 0&&O.push($t.appendConfig.of([Ea,M2,C2,b2]))}i.dispatch(i.state.update(p))}}function D1(r){return({state:t,dispatch:i})=>{let l=t.field(Ea,!1);if(!l||r<0&&l.active==0)return!1;let a=l.active+r,o=r>0&&!l.ranges.some(h=>h.field==a+r);return i(t.update({selection:Xc(l.ranges,a),effects:Ca.of(o?null:new os(l.ranges,a)),scrollIntoView:!0})),!0}}const A2=({state:r,dispatch:t})=>r.field(Ea,!1)?(t(r.update({effects:Ca.of(null)})),!0):!1,R2=D1(1),E2=D1(-1),z2=[{key:"Tab",run:R2,shift:E2},{key:"Escape",run:A2}],e0=ut.define({combine(r){return r.length?r[0]:z2}}),M2=vc.highest(y1.compute([e0],r=>r.facet(e0)));function Pi(r,t){return Object.assign(Object.assign({},t),{apply:k2(r)})}const C2=yt.domEventHandlers({mousedown(r,t){let i=t.state.field(Ea,!1),l;if(!i||(l=t.posAtCoords({x:r.clientX,y:r.clientY}))==null)return!1;let a=i.ranges.find(o=>o.from<=l&&o.to>=l);return!a||a.field==i.active?!1:(t.dispatch({selection:Xc(i.ranges,a.field),effects:Ca.of(i.ranges.some(o=>o.field>a.field)?new os(i.ranges,a.field):null),scrollIntoView:!0}),!0)}}),q1=new class extends il{};q1.startSide=1;q1.endSide=-1;const i0=new Ex,_1=new Set(["Script","Body","FunctionDefinition","ClassDefinition","LambdaExpression","ForStatement","MatchClause"]);function fo(r){return(t,i,l)=>{if(l)return!1;let a=t.node.getChild("VariableName");return a&&i(a,r),!0}}const D2={FunctionDefinition:fo("function"),ClassDefinition:fo("class"),ForStatement(r,t,i){if(i){for(let l=r.node.firstChild;l;l=l.nextSibling)if(l.name=="VariableName")t(l,"variable");else if(l.name=="in")break}},ImportStatement(r,t){var i,l;let{node:a}=r,o=((i=a.firstChild)===null||i===void 0?void 0:i.name)=="from";for(let h=a.getChild("import");h;h=h.nextSibling)h.name=="VariableName"&&((l=h.nextSibling)===null||l===void 0?void 0:l.name)!="as"&&t(h,o?"variable":"namespace")},AssignStatement(r,t){for(let i=r.node.firstChild;i;i=i.nextSibling)if(i.name=="VariableName")t(i,"variable");else if(i.name==":"||i.name=="AssignOp")break},ParamList(r,t){for(let i=null,l=r.node.firstChild;l;l=l.nextSibling)l.name=="VariableName"&&(!i||!/\*|AssignOp/.test(i.name))&&t(l,"variable"),i=l},CapturePattern:fo("variable"),AsPattern:fo("variable"),__proto__:null};function N1(r,t){let i=i0.get(t);if(i)return i;let l=[],a=!0;function o(h,c){let d=r.sliceString(h.from,h.to);l.push({label:d,type:c})}return t.cursor(ie.IncludeAnonymous).iterate(h=>{if(h.name){let c=D2[h.name];if(c&&c(h,o,a)||!a&&_1.has(h.name))return!1;a=!1}else if(h.to-h.from>8192){for(let c of N1(r,h.node))l.push(c);return!1}}),i0.set(t,l),l}const n0=/^[\w\xa1-\uffff][\w\d\xa1-\uffff]*$/,U1=["String","FormatString","Comment","PropertyName"];function q2(r){let t=Ra(r.state).resolveInner(r.pos,-1);if(U1.indexOf(t.name)>-1)return null;let i=t.name=="VariableName"||t.to-t.from<20&&n0.test(r.state.sliceDoc(t.from,t.to));if(!i&&!r.explicit)return null;let l=[];for(let a=t;a;a=a.parent)_1.has(a.name)&&(l=l.concat(N1(r.state.doc,a)));return{options:l,from:i?t.from:r.pos,validFor:n0}}const _2=["__annotations__","__builtins__","__debug__","__doc__","__import__","__name__","__loader__","__package__","__spec__","False","None","True"].map(r=>({label:r,type:"constant"})).concat(["ArithmeticError","AssertionError","AttributeError","BaseException","BlockingIOError","BrokenPipeError","BufferError","BytesWarning","ChildProcessError","ConnectionAbortedError","ConnectionError","ConnectionRefusedError","ConnectionResetError","DeprecationWarning","EOFError","Ellipsis","EncodingWarning","EnvironmentError","Exception","FileExistsError","FileNotFoundError","FloatingPointError","FutureWarning","GeneratorExit","IOError","ImportError","ImportWarning","IndentationError","IndexError","InterruptedError","IsADirectoryError","KeyError","KeyboardInterrupt","LookupError","MemoryError","ModuleNotFoundError","NameError","NotADirectoryError","NotImplemented","NotImplementedError","OSError","OverflowError","PendingDeprecationWarning","PermissionError","ProcessLookupError","RecursionError","ReferenceError","ResourceWarning","RuntimeError","RuntimeWarning","StopAsyncIteration","StopIteration","SyntaxError","SyntaxWarning","SystemError","SystemExit","TabError","TimeoutError","TypeError","UnboundLocalError","UnicodeDecodeError","UnicodeEncodeError","UnicodeError","UnicodeTranslateError","UnicodeWarning","UserWarning","ValueError","Warning","ZeroDivisionError"].map(r=>({label:r,type:"type"}))).concat(["bool","bytearray","bytes","classmethod","complex","float","frozenset","int","list","map","memoryview","object","range","set","staticmethod","str","super","tuple","type"].map(r=>({label:r,type:"class"}))).concat(["abs","aiter","all","anext","any","ascii","bin","breakpoint","callable","chr","compile","delattr","dict","dir","divmod","enumerate","eval","exec","exit","filter","format","getattr","globals","hasattr","hash","help","hex","id","input","isinstance","issubclass","iter","len","license","locals","max","min","next","oct","open","ord","pow","print","property","quit","repr","reversed","round","setattr","slice","sorted","sum","vars","zip"].map(r=>({label:r,type:"function"}))),N2=[Pi("def ${name}(${params}):\n	${}",{label:"def",detail:"function",type:"keyword"}),Pi("for ${name} in ${collection}:\n	${}",{label:"for",detail:"loop",type:"keyword"}),Pi("while ${}:\n	${}",{label:"while",detail:"loop",type:"keyword"}),Pi("try:\n	${}\nexcept ${error}:\n	${}",{label:"try",detail:"/ except block",type:"keyword"}),Pi(`if \${}:
	
`,{label:"if",detail:"block",type:"keyword"}),Pi("if ${}:\n	${}\nelse:\n	${}",{label:"if",detail:"/ else block",type:"keyword"}),Pi("class ${name}:\n	def __init__(self, ${params}):\n			${}",{label:"class",detail:"definition",type:"keyword"}),Pi("import ${module}",{label:"import",detail:"statement",type:"keyword"}),Pi("from ${module} import ${names}",{label:"from",detail:"import",type:"keyword"})],U2=S2(U1,y2(_2.concat(N2)));function Cf(r){let{node:t,pos:i}=r,l=r.lineIndent(i,-1),a=null;for(;;){let o=t.childBefore(i);if(o)if(o.name=="Comment")i=o.from;else if(o.name=="Body"||o.name=="MatchBody")r.baseIndentFor(o)+r.unit<=l&&(a=o),t=o;else if(o.name=="MatchClause")t=o;else if(o.type.is("Statement"))t=o;else break;else break}return a}function Df(r,t){let i=r.baseIndentFor(t),l=r.lineAt(r.pos,-1),a=l.from+l.text.length;return/^\s*($|#)/.test(l.text)&&r.node.to<a+100&&!/\S/.test(r.state.sliceDoc(a,r.node.to))&&r.lineIndent(r.pos,-1)<=i||/^\s*(else:|elif |except |finally:|case\s+[^=:]+:)/.test(r.textAfter)&&r.lineIndent(r.pos,-1)>i?null:i+r.unit}const qf=No.define({name:"python",parser:i2.configure({props:[u2.add({Body:r=>{var t;let i=/^\s*(#|$)/.test(r.textAfter)&&Cf(r)||r.node;return(t=Df(r,i))!==null&&t!==void 0?t:r.continue()},MatchBody:r=>{var t;let i=Cf(r);return(t=Df(r,i||r.node))!==null&&t!==void 0?t:r.continue()},IfStatement:r=>/^\s*(else:|elif )/.test(r.textAfter)?r.baseIndent:r.continue(),"ForStatement WhileStatement":r=>/^\s*else:/.test(r.textAfter)?r.baseIndent:r.continue(),TryStatement:r=>/^\s*(except[ :]|finally:|else:)/.test(r.textAfter)?r.baseIndent:r.continue(),MatchStatement:r=>/^\s*case /.test(r.textAfter)?r.baseIndent+r.unit:r.continue(),"TupleExpression ComprehensionExpression ParamList ArgList ParenthesizedExpression":zf({closing:")"}),"DictionaryExpression DictionaryComprehensionExpression SetExpression SetComprehensionExpression":zf({closing:"}"}),"ArrayExpression ArrayComprehensionExpression":zf({closing:"]"}),MemberExpression:r=>r.baseIndent+r.unit,"String FormatString":()=>null,Script:r=>{var t;let i=Cf(r);return(t=i&&Df(r,i))!==null&&t!==void 0?t:r.continue()}}),c2.add({"ArrayExpression DictionaryExpression SetExpression TupleExpression":d2,Body:(r,t)=>({from:r.from+1,to:r.to-(r.to==t.doc.length?0:1)}),"String FormatString":(r,t)=>({from:t.doc.lineAt(r.from).to,to:r.to})})]}),languageData:{closeBrackets:{brackets:["(","[","{","'",'"',"'''",'"""'],stringPrefixes:["f","fr","rf","r","u","b","br","rb","F","FR","RF","R","U","B","BR","RB"]},commentTokens:{line:"#"},indentOnInput:/^\s*([\}\]\)]|else:|elif |except |finally:|case\s+[^:]*:?)$/}});function B2(){return new r2(qf,[qf.data.of({autocomplete:q2}),qf.data.of({autocomplete:U2})])}const X2=({value:r,onChange:t,language:i="python",readOnly:l=!1,className:a=""})=>{const o=Le.useRef(null),h=Le.useRef(null);return Le.useEffect(()=>{if(!o.current)return;const c=[B2(),yt.theme({"&":{fontSize:"14px",fontFamily:'Monaco, Menlo, "Ubuntu Mono", monospace'},".cm-content":{padding:"16px",height:"100%"},".cm-focused":{outline:"none"},".cm-editor":{borderRadius:"8px"},".cm-scroller":{fontFamily:'Monaco, Menlo, "Ubuntu Mono", monospace'}}),yt.updateListener.of(m=>{if(m.docChanged&&t){const O=m.state.doc.toString();t(O)}}),At.readOnly.of(l)],d=At.create({doc:r,extensions:c}),p=new yt({state:d,parent:o.current});return h.current=p,()=>{p.destroy()}},[l]),Le.useEffect(()=>{if(h.current&&r!==h.current.state.doc.toString()){const c=h.current.state.update({changes:{from:0,to:h.current.state.doc.length,insert:r}});h.current.dispatch(c)}},[r]),st.jsx("div",{className:`border border-gray-300 rounded-lg overflow-hidden ${a}`,children:st.jsx("div",{ref:o})})},P2=({output:r,error:t,isRunning:i,onClear:l})=>{const a=r.length>0||t;return st.jsxs("div",{className:"bg-gray-50 border border-gray-300 rounded-lg p-4 font-mono text-sm h-full overflow-y-auto",children:[a&&st.jsx("div",{className:"flex justify-end mb-2",children:st.jsx("button",{onClick:l,className:"text-xs text-gray-500 hover:text-gray-700 px-2 py-1 rounded hover:bg-gray-100 transition-colors",disabled:i,children:"Clear"})}),i&&st.jsxs("div",{className:"flex items-center space-x-2 text-blue-600 mb-2",children:[st.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"}),st.jsx("span",{className:"text-sm",children:"Running code..."})]}),t&&st.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-3 mb-3",children:st.jsxs("div",{className:"flex items-start",children:[st.jsx("div",{className:"flex-shrink-0",children:st.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:st.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),st.jsxs("div",{className:"ml-3",children:[st.jsx("h4",{className:"text-sm font-medium text-red-800",children:"Error"}),st.jsx("pre",{className:"mt-1 text-sm text-red-700 whitespace-pre-wrap font-mono",children:t})]})]})}),r.length>0&&st.jsx("div",{className:"space-y-1",children:r.map((o,h)=>st.jsxs("div",{className:"text-sm text-gray-800 font-mono",children:[o||" "," "]},h))}),!a&&!i&&st.jsxs("div",{className:"text-center text-gray-500 py-4",children:[st.jsx("p",{className:"text-sm",children:"No output yet"}),st.jsx("p",{className:"text-xs text-gray-400",children:"Run your Python code to see results"})]})]})},V2=({onRun:r,onClear:t,isRunning:i,disabled:l=!1})=>st.jsxs("div",{className:"flex justify-center items-center space-x-4 py-2",children:[l&&!i&&st.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-500",children:[st.jsx("div",{className:"animate-spin rounded-full h-3 w-3 border-b-2 border-gray-400"}),st.jsx("span",{children:"Loading Python..."})]}),st.jsx("button",{onClick:r,disabled:l||i,className:`
          flex items-center space-x-2 px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 shadow-sm hover:shadow-md
          ${l||i?"bg-gray-400 cursor-not-allowed text-white":"bg-blue-600 hover:bg-blue-700 active:bg-blue-800 text-white"}
          disabled:opacity-50
        `,children:i?st.jsxs(st.Fragment,{children:[st.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}),st.jsx("span",{children:"Running..."})]}):st.jsxs(st.Fragment,{children:[st.jsx("svg",{className:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:st.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h1m4 0h1m-6-8h1m4 0h1M9 6h6"})}),st.jsx("span",{children:"Run Code"})]})}),st.jsx("button",{onClick:t,disabled:l||i,className:`
          px-4 py-2 text-sm font-medium border border-gray-300 rounded-lg
          ${l||i?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-white text-gray-700 hover:bg-gray-50 hover:border-gray-400"}
          disabled:opacity-50 transition-all duration-200 shadow-sm hover:shadow-md
        `,children:"Clear Output"})]}),H2=({children:r})=>st.jsx("div",{className:"h-screen bg-gray-100 p-4",children:st.jsx("div",{className:"h-full w-full",children:r})}),j2=()=>{const[r,t]=Le.useState({output:[],isRunning:!1}),[i,l]=Le.useState(!1),a=Le.useRef(null),o=Le.useRef(null);Le.useEffect(()=>{try{a.current=new Worker(new URL("/assets/pythonWorker-DE_q01gW.js",import.meta.url)),a.current.onmessage=d=>{const p=d.data;p.message!==void 0?t(m=>({...m,output:[...m.output,p.message]})):p.error?(t(m=>({...m,error:String(p.error),isRunning:!1})),o.current&&(clearTimeout(o.current),o.current=null)):p.ready?l(!0):p.done&&(t(m=>({...m,isRunning:!1})),o.current&&(clearTimeout(o.current),o.current=null))},a.current.onerror=d=>{console.error("Worker error:",d),t(p=>({...p,error:"Worker error occurred",isRunning:!1})),l(!1)},a.current.postMessage({type:"WORKER_READY"})}catch(d){console.error("Failed to create worker:",d),t(p=>({...p,error:"Failed to initialize Python environment",isRunning:!1}))}return()=>{a.current&&a.current.terminate(),o.current&&clearTimeout(o.current)}},[]);const h=Le.useCallback(d=>{if(!a.current||!i){t(p=>({...p,error:"Python environment not ready. Please wait...",isRunning:!1}));return}if(!d.trim()){t(p=>({...p,error:"No code to execute",isRunning:!1}));return}t({output:[],error:void 0,isRunning:!0,executionTime:Date.now()}),o.current=setTimeout(()=>{t(p=>({...p,error:"Code execution timed out (30 seconds)",isRunning:!1})),a.current&&(a.current.terminate(),l(!1),setTimeout(()=>{try{a.current=new Worker(new URL("/assets/pythonWorker-DE_q01gW.js",import.meta.url)),a.current.postMessage({type:"WORKER_READY"})}catch(p){console.error("Failed to recreate worker:",p)}},1e3))},3e4),a.current.postMessage({type:"EXEC_CODE",code:d})},[i]),c=Le.useCallback(()=>{t({output:[],error:void 0,isRunning:!1})},[]);return{executionResult:r,executeCode:h,clearOutput:c,isWorkerReady:i}},L2=`# Welcome to Python Playground!
# Write your Python code here and click "Run Code" to execute it.

print("Hello, World!")

# Try some basic Python operations:
x = 10
y = 20
result = x + y
print(f"The sum of {x} and {y} is {result}")

# You can use most Python built-in functions:
numbers = [1, 2, 3, 4, 5]
squared = [n**2 for n in numbers]
print(f"Squared numbers: {squared}")`;function G2(){const[r,t]=Le.useState(L2),{executionResult:i,executeCode:l,clearOutput:a,isWorkerReady:o}=j2(),h=()=>{l(r)},c=()=>{a()};return st.jsx(H2,{children:st.jsxs("div",{className:"h-full flex flex-col space-y-3",children:[st.jsx("div",{className:"flex-1 bg-white rounded-lg shadow-sm",children:st.jsx("div",{className:"p-3 h-full",children:st.jsx(X2,{value:r,onChange:t,language:"python",className:"h-full"})})}),st.jsx("div",{className:"flex-shrink-0",children:st.jsx(V2,{onRun:h,onClear:c,isRunning:i.isRunning,disabled:!o})}),st.jsx("div",{className:"flex-1 bg-white rounded-lg shadow-sm",children:st.jsx("div",{className:"p-3 h-full",children:st.jsx(P2,{output:i.output,error:i.error,isRunning:i.isRunning,onClear:c})})})]})})}pv.createRoot(document.getElementById("root")).render(st.jsx(Le.StrictMode,{children:st.jsx(G2,{})}));
