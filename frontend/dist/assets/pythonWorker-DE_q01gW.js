self.importScripts("https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js");async function l(){self.pyodide=await self.loadPyodide({indexURL:"https://cdn.jsdelivr.net/pyodide/v0.23.4/full/"}),await self.pyodide.loadPackage("pyodide-http")}const o=l();let t="";self.writeStdOut=e=>{if(!e){t&&self.postMessage({message:t}),t="";return}t+=e;let s;for(;(s=t.indexOf(`
`))!==-1;)self.postMessage({message:t.slice(0,s)}),t=t.slice(s+1)};self.runCode=async e=>{try{const s=self.pyodide.globals.get("dict")();s.set("writeStdOut",self.pyodide.globals.get("writeStdOut"));const d=/(^|\n)\s*async\s+def\s+/m.test(e),i=`
import sys, io
from js import writeStdOut

class StdoutFile:
  def write(self, s): writeStdOut(s)
  def flush(self): pass

sys.stdout = sys.stderr = StdoutFile()
`;if(d){const a=`
${i}
import gc, warnings, asyncio

warnings.filterwarnings(
    "ignore",
    category=RuntimeWarning,
    message=r"coroutine '.*' was never awaited",
)
gc.collect()

${e}
`;await self.pyodide.runPythonAsync(a,{globals:s})}else{const a=`
${i}
${e}
`;self.pyodide.runPython(a,{globals:s})}}catch(s){self.postMessage({error:`${n(s)}`})}finally{self.writeStdOut("")}};self.addEventListener("message",async e=>{if(await o,e.data.type==="WORKER_READY"){self.postMessage({ready:!0});return}if(e.data.type==="EXEC_CODE"){await self.runCode(e.data.code),self.postMessage({done:!0});return}typeof e.data=="string"&&(await self.runCode(e.data),self.postMessage({done:!0}))},!1);const n=e=>(e!=null&&e.message&&(e.message=e.message.replace(/File "\/lib(.|\n)*(File "<exec>)/gm,"$2")),e);
