{"version": 3, "sources": ["../../@codemirror/theme-one-dark/dist/index.js"], "sourcesContent": ["import { EditorView } from '@codemirror/view';\nimport { HighlightStyle, syntaxHighlighting } from '@codemirror/language';\nimport { tags } from '@lezer/highlight';\n\n// Using https://github.com/one-dark/vscode-one-dark-theme/ as reference for the colors\nconst chalky = \"#e5c07b\", coral = \"#e06c75\", cyan = \"#56b6c2\", invalid = \"#ffffff\", ivory = \"#abb2bf\", stone = \"#7d8799\", // Brightened compared to original to increase contrast\nmalibu = \"#61afef\", sage = \"#98c379\", whiskey = \"#d19a66\", violet = \"#c678dd\", darkBackground = \"#21252b\", highlightBackground = \"#2c313a\", background = \"#282c34\", tooltipBackground = \"#353a42\", selection = \"#3E4451\", cursor = \"#528bff\";\n/**\nThe colors used in the theme, as CSS color strings.\n*/\nconst color = {\n    chalky,\n    coral,\n    cyan,\n    invalid,\n    ivory,\n    stone,\n    malibu,\n    sage,\n    whiskey,\n    violet,\n    darkBackground,\n    highlightBackground,\n    background,\n    tooltipBackground,\n    selection,\n    cursor\n};\n/**\nThe editor theme styles for One Dark.\n*/\nconst oneDarkTheme = /*@__PURE__*/EditorView.theme({\n    \"&\": {\n        color: ivory,\n        backgroundColor: background\n    },\n    \".cm-content\": {\n        caretColor: cursor\n    },\n    \".cm-cursor, .cm-dropCursor\": { borderLeftColor: cursor },\n    \"&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground, .cm-selectionBackground, .cm-content ::selection\": { backgroundColor: selection },\n    \".cm-panels\": { backgroundColor: darkBackground, color: ivory },\n    \".cm-panels.cm-panels-top\": { borderBottom: \"2px solid black\" },\n    \".cm-panels.cm-panels-bottom\": { borderTop: \"2px solid black\" },\n    \".cm-searchMatch\": {\n        backgroundColor: \"#72a1ff59\",\n        outline: \"1px solid #457dff\"\n    },\n    \".cm-searchMatch.cm-searchMatch-selected\": {\n        backgroundColor: \"#6199ff2f\"\n    },\n    \".cm-activeLine\": { backgroundColor: \"#6699ff0b\" },\n    \".cm-selectionMatch\": { backgroundColor: \"#aafe661a\" },\n    \"&.cm-focused .cm-matchingBracket, &.cm-focused .cm-nonmatchingBracket\": {\n        backgroundColor: \"#bad0f847\"\n    },\n    \".cm-gutters\": {\n        backgroundColor: background,\n        color: stone,\n        border: \"none\"\n    },\n    \".cm-activeLineGutter\": {\n        backgroundColor: highlightBackground\n    },\n    \".cm-foldPlaceholder\": {\n        backgroundColor: \"transparent\",\n        border: \"none\",\n        color: \"#ddd\"\n    },\n    \".cm-tooltip\": {\n        border: \"none\",\n        backgroundColor: tooltipBackground\n    },\n    \".cm-tooltip .cm-tooltip-arrow:before\": {\n        borderTopColor: \"transparent\",\n        borderBottomColor: \"transparent\"\n    },\n    \".cm-tooltip .cm-tooltip-arrow:after\": {\n        borderTopColor: tooltipBackground,\n        borderBottomColor: tooltipBackground\n    },\n    \".cm-tooltip-autocomplete\": {\n        \"& > ul > li[aria-selected]\": {\n            backgroundColor: highlightBackground,\n            color: ivory\n        }\n    }\n}, { dark: true });\n/**\nThe highlighting style for code in the One Dark theme.\n*/\nconst oneDarkHighlightStyle = /*@__PURE__*/HighlightStyle.define([\n    { tag: tags.keyword,\n        color: violet },\n    { tag: [tags.name, tags.deleted, tags.character, tags.propertyName, tags.macroName],\n        color: coral },\n    { tag: [/*@__PURE__*/tags.function(tags.variableName), tags.labelName],\n        color: malibu },\n    { tag: [tags.color, /*@__PURE__*/tags.constant(tags.name), /*@__PURE__*/tags.standard(tags.name)],\n        color: whiskey },\n    { tag: [/*@__PURE__*/tags.definition(tags.name), tags.separator],\n        color: ivory },\n    { tag: [tags.typeName, tags.className, tags.number, tags.changed, tags.annotation, tags.modifier, tags.self, tags.namespace],\n        color: chalky },\n    { tag: [tags.operator, tags.operatorKeyword, tags.url, tags.escape, tags.regexp, tags.link, /*@__PURE__*/tags.special(tags.string)],\n        color: cyan },\n    { tag: [tags.meta, tags.comment],\n        color: stone },\n    { tag: tags.strong,\n        fontWeight: \"bold\" },\n    { tag: tags.emphasis,\n        fontStyle: \"italic\" },\n    { tag: tags.strikethrough,\n        textDecoration: \"line-through\" },\n    { tag: tags.link,\n        color: stone,\n        textDecoration: \"underline\" },\n    { tag: tags.heading,\n        fontWeight: \"bold\",\n        color: coral },\n    { tag: [tags.atom, tags.bool, /*@__PURE__*/tags.special(tags.variableName)],\n        color: whiskey },\n    { tag: [tags.processingInstruction, tags.string, tags.inserted],\n        color: sage },\n    { tag: tags.invalid,\n        color: invalid },\n]);\n/**\nExtension to enable the One Dark theme (both the editor theme and\nthe highlight style).\n*/\nconst oneDark = [oneDarkTheme, /*@__PURE__*/syntaxHighlighting(oneDarkHighlightStyle)];\n\nexport { color, oneDark, oneDarkHighlightStyle, oneDarkTheme };\n"], "mappings": ";;;;;;;;;;;;AAKA,IAAM,SAAS;AAAf,IAA0B,QAAQ;AAAlC,IAA6C,OAAO;AAApD,IAA+D,UAAU;AAAzE,IAAoF,QAAQ;AAA5F,IAAuG,QAAQ;AAA/G,IACA,SAAS;AADT,IACoB,OAAO;AAD3B,IACsC,UAAU;AADhD,IAC2D,SAAS;AADpE,IAC+E,iBAAiB;AADhG,IAC2G,sBAAsB;AADjI,IAC4I,aAAa;AADzJ,IACoK,oBAAoB;AADxL,IACmM,YAAY;AAD/M,IAC0N,SAAS;AAInO,IAAM,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAIA,IAAM,eAA4B,WAAW,MAAM;AAAA,EAC/C,KAAK;AAAA,IACD,OAAO;AAAA,IACP,iBAAiB;AAAA,EACrB;AAAA,EACA,eAAe;AAAA,IACX,YAAY;AAAA,EAChB;AAAA,EACA,8BAA8B,EAAE,iBAAiB,OAAO;AAAA,EACxD,8HAA8H,EAAE,iBAAiB,UAAU;AAAA,EAC3J,cAAc,EAAE,iBAAiB,gBAAgB,OAAO,MAAM;AAAA,EAC9D,4BAA4B,EAAE,cAAc,kBAAkB;AAAA,EAC9D,+BAA+B,EAAE,WAAW,kBAAkB;AAAA,EAC9D,mBAAmB;AAAA,IACf,iBAAiB;AAAA,IACjB,SAAS;AAAA,EACb;AAAA,EACA,2CAA2C;AAAA,IACvC,iBAAiB;AAAA,EACrB;AAAA,EACA,kBAAkB,EAAE,iBAAiB,YAAY;AAAA,EACjD,sBAAsB,EAAE,iBAAiB,YAAY;AAAA,EACrD,yEAAyE;AAAA,IACrE,iBAAiB;AAAA,EACrB;AAAA,EACA,eAAe;AAAA,IACX,iBAAiB;AAAA,IACjB,OAAO;AAAA,IACP,QAAQ;AAAA,EACZ;AAAA,EACA,wBAAwB;AAAA,IACpB,iBAAiB;AAAA,EACrB;AAAA,EACA,uBAAuB;AAAA,IACnB,iBAAiB;AAAA,IACjB,QAAQ;AAAA,IACR,OAAO;AAAA,EACX;AAAA,EACA,eAAe;AAAA,IACX,QAAQ;AAAA,IACR,iBAAiB;AAAA,EACrB;AAAA,EACA,wCAAwC;AAAA,IACpC,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,EACvB;AAAA,EACA,uCAAuC;AAAA,IACnC,gBAAgB;AAAA,IAChB,mBAAmB;AAAA,EACvB;AAAA,EACA,4BAA4B;AAAA,IACxB,8BAA8B;AAAA,MAC1B,iBAAiB;AAAA,MACjB,OAAO;AAAA,IACX;AAAA,EACJ;AACJ,GAAG,EAAE,MAAM,KAAK,CAAC;AAIjB,IAAM,wBAAqC,eAAe,OAAO;AAAA,EAC7D;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,KAAK,WAAW,KAAK,cAAc,KAAK,SAAS;AAAA,IAC9E,OAAO;AAAA,EAAM;AAAA,EACjB;AAAA,IAAE,KAAK,CAAc,KAAK,SAAS,KAAK,YAAY,GAAG,KAAK,SAAS;AAAA,IACjE,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAC,KAAK,OAAoB,KAAK,SAAS,KAAK,IAAI,GAAgB,KAAK,SAAS,KAAK,IAAI,CAAC;AAAA,IAC5F,OAAO;AAAA,EAAQ;AAAA,EACnB;AAAA,IAAE,KAAK,CAAc,KAAK,WAAW,KAAK,IAAI,GAAG,KAAK,SAAS;AAAA,IAC3D,OAAO;AAAA,EAAM;AAAA,EACjB;AAAA,IAAE,KAAK,CAAC,KAAK,UAAU,KAAK,WAAW,KAAK,QAAQ,KAAK,SAAS,KAAK,YAAY,KAAK,UAAU,KAAK,MAAM,KAAK,SAAS;AAAA,IACvH,OAAO;AAAA,EAAO;AAAA,EAClB;AAAA,IAAE,KAAK,CAAC,KAAK,UAAU,KAAK,iBAAiB,KAAK,KAAK,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAAmB,KAAK,QAAQ,KAAK,MAAM,CAAC;AAAA,IAC9H,OAAO;AAAA,EAAK;AAAA,EAChB;AAAA,IAAE,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;AAAA,IAC3B,OAAO;AAAA,EAAM;AAAA,EACjB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,YAAY;AAAA,EAAO;AAAA,EACvB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,WAAW;AAAA,EAAS;AAAA,EACxB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,gBAAgB;AAAA,EAAe;AAAA,EACnC;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,OAAO;AAAA,IACP,gBAAgB;AAAA,EAAY;AAAA,EAChC;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,YAAY;AAAA,IACZ,OAAO;AAAA,EAAM;AAAA,EACjB;AAAA,IAAE,KAAK,CAAC,KAAK,MAAM,KAAK,MAAmB,KAAK,QAAQ,KAAK,YAAY,CAAC;AAAA,IACtE,OAAO;AAAA,EAAQ;AAAA,EACnB;AAAA,IAAE,KAAK,CAAC,KAAK,uBAAuB,KAAK,QAAQ,KAAK,QAAQ;AAAA,IAC1D,OAAO;AAAA,EAAK;AAAA,EAChB;AAAA,IAAE,KAAK,KAAK;AAAA,IACR,OAAO;AAAA,EAAQ;AACvB,CAAC;AAKD,IAAM,UAAU,CAAC,cAA2B,mBAAmB,qBAAqB,CAAC;", "names": []}