# Frontend Plan: Python Code IDE Playground

## Overview

Build a boot.dev-like code IDE playground for running Python code in the browser using Pyodide and CodeMirror, with a React + TypeScript + Vite frontend.

## Architecture

### Project Structure

```
web-ide-light/
├── frontend/                 # React + TypeScript + Vite frontend
│   ├── src/
│   │   ├── components/
│   │   │   ├── CodeEditor/   # CodeMirror-based editor
│   │   │   ├── OutputPanel/  # Code execution output
│   │   │   ├── Toolbar/      # Run button, language selector
│   │   │   └── Layout/       # Main layout components
│   │   ├── workers/
│   │   │   └── pythonWorker.ts  # Pyodide web worker
│   │   ├── hooks/
│   │   │   └── useCodeExecution.ts  # Code execution logic
│   │   ├── types/
│   │   │   └── index.ts      # TypeScript type definitions
│   │   └── utils/
│   │       └── workerHelper.ts  # Worker management utilities
│   └── public/
│       └── workers/          # Worker files (for proper loading)
├── api/                      # Future backend API for other languages
└── plans/
    └── frontend-plan.md      # This file
```

## Technology Stack

### Core Technologies

- **React 18** with TypeScript
- **Vite** for build tooling and dev server
- **CodeMirror 6** for the code editor
- **Pyodide** for Python execution in browser
- **Web Workers** for non-blocking code execution

### Key Dependencies

- `@codemirror/state` - CodeMirror state management
- `@codemirror/view` - CodeMirror editor view
- `@codemirror/lang-python` - Python language support
- `@codemirror/theme-one-dark` - Dark theme (optional)
- `@codemirror/commands` - Editor commands
- `@codemirror/search` - Search functionality
- `tailwindcss` - Utility-first CSS framework
- `@tailwindcss/typography` - Typography plugin for better text styling
- `autoprefixer` - CSS vendor prefixing
- `postcss` - CSS processing

## Implementation Plan

### ✅ Phase 1: Project Setup (COMPLETED)

1. **✅ Initialize Vite React TypeScript project**

   ```bash
   yarn create vite frontend --template react-ts
   cd frontend
   yarn install
   ```

2. **✅ Install CodeMirror and Tailwind dependencies**

   ```bash
   yarn add @codemirror/state @codemirror/view @codemirror/lang-python @codemirror/commands @codemirror/search @codemirror/theme-one-dark
   yarn add -D tailwindcss @tailwindcss/typography autoprefixer postcss
   npx tailwindcss init -p
   ```

3. **✅ Setup project structure and Tailwind**
   - ✅ Create component directories
   - ✅ Setup TypeScript types
   - ✅ Configure Vite for web workers
   - ✅ Configure Tailwind CSS in main CSS file

### ✅ Phase 2: Core Components (COMPLETED)

#### ✅ 1. CodeEditor Component (COMPLETED)

- **File**: `src/components/CodeEditor/CodeEditor.tsx`
- **Features**:
  - ✅ CodeMirror 6 integration
  - ✅ Python syntax highlighting
  - ✅ Line numbers
  - ✅ Auto-completion
  - ✅ Search/replace functionality
  - ✅ Customizable themes

#### ✅ 2. OutputPanel Component (COMPLETED)

- **File**: `src/components/OutputPanel/OutputPanel.tsx`
- **Features**:
  - ✅ Display code execution output
  - ✅ Error handling and display
  - ✅ Clear output functionality
  - ✅ Scrollable output area

#### ✅ 3. Toolbar Component (COMPLETED)

- **File**: `src/components/Toolbar/Toolbar.tsx`
- **Features**:
  - ✅ Run code button
  - ✅ Clear output button
  - ✅ Language selector (future-ready for other languages)
  - ✅ Loading states

#### ✅ 4. Layout Component (COMPLETED)

- **File**: `src/components/Layout/Layout.tsx`
- **Features**:
  - ✅ Single column layout using Tailwind CSS
  - ✅ Code editor at top
  - ✅ Run button section in middle
  - ✅ Output panel at bottom
  - ✅ Responsive design with proper spacing

### ✅ Phase 3: Python Execution System (COMPLETED)

#### ✅ 1. Python Web Worker (COMPLETED)

- **File**: `src/workers/pythonWorker.ts`
- **Features**:
  - ✅ Load Pyodide from CDN
  - ✅ Execute Python code
  - ✅ Capture stdout/stderr
  - ✅ Handle execution errors
  - ✅ Timeout handling

#### ✅ 2. Code Execution Hook (COMPLETED)

- **File**: `src/hooks/useCodeExecution.ts`
- **Features**:
  - ✅ Manage code execution state
  - ✅ Handle worker communication
  - ✅ Provide execution API to components
  - ✅ Loading and error states
  - ✅ Worker lifecycle management
  - ✅ Timeout handling (30 seconds)

#### ✅ 3. App Integration (COMPLETED)

- **File**: `src/App.tsx`
- **Features**:
  - ✅ Integrated real Python execution
  - ✅ Worker status indicators
  - ✅ Loading states for Python environment

### Phase 4: Integration & Polish

#### 1. Main App Integration

- **File**: `src/App.tsx`
- **Features**:
  - Combine all components
  - State management
  - Global error handling

#### 2. Styling & UX with Tailwind

- **Features**:
  - Tailwind CSS utility classes for styling
  - Single column responsive layout
  - Dark/light theme support using Tailwind
  - Loading indicators with Tailwind animations
  - Error states with proper color schemes
  - Professional UI/UX with consistent spacing and typography

## Technical Implementation Details

### Tailwind CSS Setup

```css
/* src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom component classes */
@layer components {
  .code-editor {
    @apply border border-gray-300 rounded-lg overflow-hidden;
  }

  .run-button {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors;
  }

  .output-panel {
    @apply bg-gray-50 border border-gray-300 rounded-lg p-4 font-mono text-sm;
  }
}
```

### Layout Structure

```typescript
// Single column layout structure
<div className="min-h-screen bg-gray-100 p-4">
  <div className="max-w-4xl mx-auto space-y-4">
    {/* Code Editor Section */}
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-900">
          Python Code Editor
        </h2>
      </div>
      <div className="p-4">
        <CodeEditor />
      </div>
    </div>

    {/* Run Button Section */}
    <div className="flex justify-center">
      <button className="run-button">Run Code</button>
    </div>

    {/* Output Section */}
    <div className="bg-white rounded-lg shadow-sm">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900">Output</h3>
      </div>
      <div className="p-4">
        <OutputPanel />
      </div>
    </div>
  </div>
</div>
```

### Web Worker Setup

```typescript
// pythonWorker.ts structure
importScripts("https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js");

addEventListener("message", async (e) => {
  await loadPyodide();
  // Execute Python code
  // Capture output
  // Send results back
});
```

### CodeMirror Integration

```typescript
// CodeEditor component structure
import { EditorView } from "@codemirror/view";
import { python } from "@codemirror/lang-python";

const extensions = [
  python(),
  EditorView.theme({...}),
  // Additional extensions
];
```

### State Management

- Use React's built-in state management (useState, useReducer)
- Custom hooks for complex logic
- Context API for global state if needed

## Future Considerations

### API Integration Ready

- Design components to easily integrate with backend API
- Abstract execution logic to support multiple languages
- Prepare for server-side code execution

### Performance Optimizations

- Code splitting for Pyodide loading
- Worker pooling for multiple executions
- Debounced execution for real-time features

### Additional Features

- File system simulation
- Package installation support
- Code sharing functionality
- Collaborative editing (future)

## Success Criteria

1. **Functional Python IDE**

   - Users can write Python code in CodeMirror editor
   - Code executes in browser via Pyodide
   - Output displays correctly
   - Errors are handled gracefully

2. **Good User Experience**

   - Fast loading and execution
   - Responsive design
   - Intuitive interface
   - Professional appearance

3. **Extensible Architecture**
   - Easy to add new languages
   - Ready for API integration
   - Maintainable codebase
   - Well-documented components

## ✅ IMPLEMENTATION COMPLETE!

### 🎉 What's Been Built:

1. **✅ Complete Python IDE Playground**

   - Modern React + TypeScript + Vite frontend
   - CodeMirror 6 editor with Python syntax highlighting
   - Real Python execution in browser via Pyodide + WebAssembly
   - Professional single-column layout with Tailwind CSS

2. **✅ Core Features Working:**

   - Write Python code in professional editor
   - Execute Python code in browser (no server needed!)
   - View output and errors in real-time
   - Loading states and error handling
   - Timeout protection (30 seconds)
   - Worker lifecycle management

3. **✅ UI/UX Complete:**
   - Viewport-fitting single column layout
   - Code editor at top
   - Run/Clear buttons in middle
   - Output panel at bottom
   - Compact, professional design
   - Loading indicators and status messages

### 🚀 Ready for Use:

The Python IDE playground is now **fully functional** and ready for users to write and execute Python code directly in their browser!
