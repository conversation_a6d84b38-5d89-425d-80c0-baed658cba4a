# Frontend Plan: Python Code IDE Playground

## Overview
Build a boot.dev-like code IDE playground for running Python code in the browser using Pyodide and CodeMirror, with a React + TypeScript + Vite frontend.

## Architecture

### Project Structure
```
web-ide-light/
├── frontend/                 # React + TypeScript + Vite frontend
│   ├── src/
│   │   ├── components/
│   │   │   ├── CodeEditor/   # CodeMirror-based editor
│   │   │   ├── OutputPanel/  # Code execution output
│   │   │   ├── Toolbar/      # Run button, language selector
│   │   │   └── Layout/       # Main layout components
│   │   ├── workers/
│   │   │   └── pythonWorker.ts  # Pyodide web worker
│   │   ├── hooks/
│   │   │   └── useCodeExecution.ts  # Code execution logic
│   │   ├── types/
│   │   │   └── index.ts      # TypeScript type definitions
│   │   └── utils/
│   │       └── workerHelper.ts  # Worker management utilities
│   └── public/
│       └── workers/          # Worker files (for proper loading)
├── api/                      # Future backend API for other languages
└── plans/
    └── frontend-plan.md      # This file
```

## Technology Stack

### Core Technologies
- **React 18** with TypeScript
- **Vite** for build tooling and dev server
- **CodeMirror 6** for the code editor
- **Pyodide** for Python execution in browser
- **Web Workers** for non-blocking code execution

### Key Dependencies
- `@codemirror/state` - CodeMirror state management
- `@codemirror/view` - CodeMirror editor view
- `@codemirror/lang-python` - Python language support
- `@codemirror/theme-one-dark` - Dark theme (optional)
- `@codemirror/commands` - Editor commands
- `@codemirror/search` - Search functionality

## Implementation Plan

### Phase 1: Project Setup
1. **Initialize Vite React TypeScript project**
   ```bash
   yarn create vite frontend --template react-ts
   cd frontend
   yarn install
   ```

2. **Install CodeMirror dependencies**
   ```bash
   yarn add @codemirror/state @codemirror/view @codemirror/lang-python @codemirror/commands @codemirror/search @codemirror/theme-one-dark
   ```

3. **Setup project structure**
   - Create component directories
   - Setup TypeScript types
   - Configure Vite for web workers

### Phase 2: Core Components

#### 1. CodeEditor Component
- **File**: `src/components/CodeEditor/CodeEditor.tsx`
- **Features**:
  - CodeMirror 6 integration
  - Python syntax highlighting
  - Line numbers
  - Auto-completion
  - Search/replace functionality
  - Customizable themes

#### 2. OutputPanel Component
- **File**: `src/components/OutputPanel/OutputPanel.tsx`
- **Features**:
  - Display code execution output
  - Error handling and display
  - Clear output functionality
  - Scrollable output area

#### 3. Toolbar Component
- **File**: `src/components/Toolbar/Toolbar.tsx`
- **Features**:
  - Run code button
  - Clear output button
  - Language selector (future-ready for other languages)
  - Loading states

#### 4. Layout Component
- **File**: `src/components/Layout/Layout.tsx`
- **Features**:
  - Responsive split-pane layout
  - Editor on left, output on right
  - Resizable panels

### Phase 3: Python Execution System

#### 1. Python Web Worker
- **File**: `src/workers/pythonWorker.ts`
- **Features**:
  - Load Pyodide from CDN
  - Execute Python code
  - Capture stdout/stderr
  - Handle execution errors
  - Timeout handling

#### 2. Worker Helper
- **File**: `src/utils/workerHelper.ts`
- **Features**:
  - Abstract worker communication
  - Message passing utilities
  - Worker lifecycle management
  - Error handling

#### 3. Code Execution Hook
- **File**: `src/hooks/useCodeExecution.ts`
- **Features**:
  - Manage code execution state
  - Handle worker communication
  - Provide execution API to components
  - Loading and error states

### Phase 4: Integration & Polish

#### 1. Main App Integration
- **File**: `src/App.tsx`
- **Features**:
  - Combine all components
  - State management
  - Global error handling

#### 2. Styling & UX
- **Features**:
  - Responsive design
  - Dark/light theme support
  - Loading indicators
  - Error states
  - Professional UI/UX

## Technical Implementation Details

### Web Worker Setup
```typescript
// pythonWorker.ts structure
importScripts("https://cdn.jsdelivr.net/pyodide/v0.24.1/full/pyodide.js");

addEventListener("message", async (e) => {
  await loadPyodide();
  // Execute Python code
  // Capture output
  // Send results back
});
```

### CodeMirror Integration
```typescript
// CodeEditor component structure
import { EditorView } from "@codemirror/view";
import { python } from "@codemirror/lang-python";

const extensions = [
  python(),
  EditorView.theme({...}),
  // Additional extensions
];
```

### State Management
- Use React's built-in state management (useState, useReducer)
- Custom hooks for complex logic
- Context API for global state if needed

## Future Considerations

### API Integration Ready
- Design components to easily integrate with backend API
- Abstract execution logic to support multiple languages
- Prepare for server-side code execution

### Performance Optimizations
- Code splitting for Pyodide loading
- Worker pooling for multiple executions
- Debounced execution for real-time features

### Additional Features
- File system simulation
- Package installation support
- Code sharing functionality
- Collaborative editing (future)

## Success Criteria

1. **Functional Python IDE**
   - Users can write Python code in CodeMirror editor
   - Code executes in browser via Pyodide
   - Output displays correctly
   - Errors are handled gracefully

2. **Good User Experience**
   - Fast loading and execution
   - Responsive design
   - Intuitive interface
   - Professional appearance

3. **Extensible Architecture**
   - Easy to add new languages
   - Ready for API integration
   - Maintainable codebase
   - Well-documented components

## Next Steps

1. Create the frontend project structure
2. Implement core components
3. Set up Python execution system
4. Integrate and test
5. Polish UI/UX
6. Prepare for API integration
